<template>
  <el-dialog
    v-model="visible"
    :title="isEditing ? '编辑分类' : '创建分类'"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="category-form-dialog"
  >
    <el-form
      ref="categoryFormRef"
      :model="categoryForm"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="父分类" prop="parent_id" v-if="!isEditing">
        <el-select
          v-model="categoryForm.parent_id"
          placeholder="请选择父分类（可选）"
          clearable
          :disabled="loading"
          data-testid="parent-category-selector"
          class="parent-category-select"
        >
          <el-option
            label="无（创建根分类）"
            :value="null"
          />
          <el-option
            v-for="category in availableParentCategories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
        <div class="form-tip">
          <span>选择父分类可创建子分类，不选择则创建根分类</span>
        </div>
      </el-form-item>

      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="categoryForm.name"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
          clearable
          :disabled="loading"
        />
      </el-form-item>
      
      <el-form-item label="分类描述" prop="description">
        <el-input
          v-model="categoryForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入分类描述（可选）"
          maxlength="200"
          show-word-limit
          :disabled="loading"
        />
      </el-form-item>
      
      <el-form-item label="启用状态" prop="is_active" v-if="isEditing">
        <el-switch
          v-model="categoryForm.is_active"
          :disabled="loading"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
        >
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  categoryData: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  categories: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'update:modelValue',
  'submit',
  'close'
])

const categoryFormRef = ref()

// 表单数据
const categoryForm = reactive({
  name: '',
  description: '',
  is_active: true,
  parent_id: null
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度应在1-50字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '分类描述不能超过200字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 可用的父分类列表（只显示根级分类，支持2级结构）
const availableParentCategories = computed(() => {
  return props.categories.filter(cat => 
    cat.level === 1 || !cat.level || cat.parent_id === null
  )
})

// 监听分类数据变化，更新表单
watch(() => props.categoryData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    categoryForm.name = newData.name || ''
    categoryForm.description = newData.description || ''
    categoryForm.is_active = newData.is_active !== false // 默认为true
    categoryForm.parent_id = newData.parent_id || null
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.modelValue, (newVisible) => {
  if (newVisible) {
    // 对话框打开时重置表单
    if (!props.isEditing) {
      resetForm()
    }
    // 聚焦到名称输入框
    nextTick(() => {
      const nameInput = categoryFormRef.value?.$el?.querySelector('input')
      if (nameInput) {
        nameInput.focus()
      }
    })
  }
})

// 重置表单
const resetForm = () => {
  categoryForm.name = ''
  categoryForm.description = ''
  categoryForm.is_active = true
  categoryForm.parent_id = null
  
  // 清除表单验证
  nextTick(() => {
    categoryFormRef.value?.clearValidate()
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await categoryFormRef.value?.validate()
    if (!valid) return
    
    const submitData = {
      name: categoryForm.name.trim(),
      description: categoryForm.description.trim(),
      parent_id: categoryForm.parent_id,
      ...(props.isEditing && { is_active: categoryForm.is_active })
    }
    
    emit('submit', submitData)
  } catch (error) {
    ElMessage.error('表单验证失败')
  }
}

// 关闭对话框
const handleClose = () => {
  if (props.loading) return
  
  visible.value = false
  emit('close')
  
  // 延迟重置表单，避免关闭动画期间看到表单清空
  setTimeout(() => {
    resetForm()
  }, 300)
}

// 按键处理
const handleKeydown = (event) => {
  if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
    handleSubmit()
  }
}

// 暴露组件方法
defineExpose({
  resetForm,
  handleSubmit,
  handleClose
})
</script>

<style lang="scss" scoped>
.category-form-dialog {
  .el-dialog {
    border-radius: 12px;
  }
  
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
      
      :deep(.el-form-item__label) {
        color: #333;
        font-weight: 500;
      }
      
      :deep(.el-input__wrapper) {
        border-radius: 6px;
      }
      
      :deep(.el-textarea__inner) {
        border-radius: 6px;
      }
      
      :deep(.el-select) {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 6px;
        }
      }
      
      .form-tip {
        margin-top: 4px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .el-button {
      min-width: 80px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-form-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
    
    .el-form {
      .el-form-item {
        :deep(.el-form-item__label) {
          width: 80px !important;
        }
      }
    }
  }
}
</style>