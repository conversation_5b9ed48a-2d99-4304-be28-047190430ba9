<template>
  <div class="muscle-diagram-pro">
    <div class="diagram-header">
      <h4>{{ exerciseName }} - 肌肉群激活</h4>
      <div class="diagram-controls">
        <!-- 视图切换 -->
        <div class="view-toggle">
          <el-button-group>
            <el-button 
              :type="currentView === 'anterior' ? 'primary' : 'default'"
              size="small"
              @click="currentView = 'anterior'"
            >
              正面
            </el-button>
            <el-button 
              :type="currentView === 'posterior' ? 'primary' : 'default'"
              size="small"
              @click="currentView = 'posterior'"
            >
              背面
            </el-button>
          </el-button-group>
        </div>
        
        <!-- 图例 -->
        <div class="diagram-legend">
          <div class="legend-item">
            <div class="legend-color primary"></div>
            <span>主要肌群</span>
          </div>
          <div class="legend-item">
            <div class="legend-color secondary"></div>
            <span>辅助肌群</span>
          </div>
          <div class="legend-item">
            <div class="legend-color stabilizer"></div>
            <span>稳定肌群</span>
          </div>
        </div>
      </div>
    </div>

    <div class="diagram-body">
      <!-- 肌肉图示区域 -->
      <div class="muscle-diagram-container">
        <div class="diagram-wrapper">
          <svg 
            viewBox="0 0 100 220" 
            class="body-svg"
            @mouseleave="hideTooltip"
          >
            <!-- 人体轮廓 -->
            <g class="body-outline">
              <!-- 头部 -->
              <ellipse cx="50" cy="15" rx="8" ry="10" fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              
              <!-- 躯干轮廓 -->
              <path v-if="currentView === 'anterior'" 
                    d="M35 25 C35 25, 38 28, 40 35 L40 75 C40 85, 38 95, 35 105 L35 115 C35 125, 38 135, 40 145 L40 165 C40 175, 42 185, 45 195 L45 210 C45 215, 47 218, 50 218 C53 218, 55 215, 55 210 L55 195 C58 185, 60 175, 60 165 L60 145 C62 135, 65 125, 65 115 L65 105 C62 95, 60 85, 60 75 L60 35 C62 28, 65 25, 65 25 C65 25, 62 22, 50 22 C38 22, 35 25, 35 25 Z" 
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              
              <path v-else 
                    d="M35 25 C35 25, 38 28, 40 35 L40 75 C40 85, 38 95, 35 105 L35 115 C35 125, 38 135, 40 145 L40 165 C40 175, 42 185, 45 195 L45 210 C45 215, 47 218, 50 218 C53 218, 55 215, 55 210 L55 195 C58 185, 60 175, 60 165 L60 145 C62 135, 65 125, 65 115 L65 105 C62 95, 60 85, 60 75 L60 35 C62 28, 65 25, 65 25 C65 25, 62 22, 50 22 C38 22, 35 25, 35 25 Z" 
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              
              <!-- 手臂轮廓 -->
              <path d="M35 35 C30 33, 25 35, 22 40 L20 60 C20 70, 22 80, 25 90 L27 95 C28 97, 30 98, 32 99"
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              <path d="M65 35 C70 33, 75 35, 78 40 L80 60 C80 70, 78 80, 75 90 L73 95 C72 97, 70 98, 68 99"
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              
              <!-- 腿部轮廓 -->
              <path d="M40 145 C38 155, 36 165, 36 175 L36 200 C36 210, 38 215, 40 218"
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
              <path d="M60 145 C62 155, 64 165, 64 175 L64 200 C64 210, 62 215, 60 218"
                    fill="none" stroke="#cbd5e0" stroke-width="0.5"/>
            </g>
            
            <!-- 动态渲染肌肉群 -->
            <g class="muscles-group">
              <polygon
                v-for="muscle in currentMuscles"
                :key="muscle.name || 'muscle-' + Math.random()"
                :points="muscle.points"
                :class="getMuscleClass(muscle.name)"
                class="muscle-polygon"
                @mouseenter="showTooltip($event, muscle)"
                @mousemove="updateTooltipPosition($event)"
                @click="toggleMuscle(muscle)"
                v-if="muscle && muscle.name && muscle.points"
              />
            </g>
          </svg>
          
          <!-- 工具提示 -->
          <div 
            v-show="tooltipVisible"
            class="muscle-tooltip"
            :style="tooltipStyle"
          >
            <div class="tooltip-content">
              <div class="muscle-name">{{ tooltipData.displayName }}</div>
              <div class="muscle-group">{{ getMuscleGroupName(tooltipData.group) }}</div>
              <div class="muscle-role" v-if="getMuscleRole(tooltipData.name)">
                {{ getMuscleRole(tooltipData.name) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 激活肌肉列表 -->
      <div class="activated-muscles" v-if="activatedMusclesList.length > 0">
        <h5>激活的肌肉群</h5>
        <div class="muscles-summary">
          <div class="summary-stats">
            <span class="total-count">共 {{ activatedMusclesList.length }} 个肌肉群</span>
            <span class="primary-count">主要 {{ primaryMusclesCount }} 个</span>
            <span class="secondary-count">辅助 {{ secondaryMusclesCount }} 个</span>
          </div>
        </div>
        
        <div class="muscles-grid">
          <div 
            v-for="muscle in activatedMusclesList"
            :key="muscle.name"
            class="muscle-item"
            :class="muscle.type"
          >
            <div class="muscle-indicator" :class="muscle.type"></div>
            <div class="muscle-content">
              <span class="muscle-name">{{ muscle.displayName }}</span>
              <span class="muscle-type">{{ getMuscleTypeLabel(muscle.type) }}</span>
            </div>
            <div class="muscle-intensity">
              <div class="intensity-bar" :class="muscle.type">
                <div class="intensity-fill" :style="{ width: getIntensityWidth(muscle.type) }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无激活肌肉时的提示 -->
      <div v-else class="no-muscles-activated">
        <el-icon class="no-muscles-icon"><Warning /></el-icon>
        <div class="no-muscles-text">
          <h5>暂无肌肉群数据</h5>
          <p>该动作的肌肉群激活信息正在完善中</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { 
  anteriorMuscles, 
  posteriorMuscles, 
  muscleGroups, 
  muscleMapping 
} from '@/data/musclePolygons.ts'

// Props
const props = defineProps({
  muscleGroups: {
    type: Object,
    default: () => ({})
  },
  exerciseName: {
    type: String,
    default: '动作'
  },
  interactive: {
    type: Boolean,
    default: true
  }
})

// Emits - 定义组件可能发出的事件
const emit = defineEmits(['muscle-selected', 'muscle-hover'])

// 响应式数据
const currentView = ref('anterior')
const tooltipVisible = ref(false)
const tooltipData = ref({})
const tooltipPosition = reactive({ x: 0, y: 0 })
const selectedMuscles = ref(new Set())

// 计算属性
const currentMuscles = computed(() => {
  try {
    const muscles = currentView.value === 'anterior' ? anteriorMuscles : posteriorMuscles
    
    // 防御性检查：确保muscles是数组且元素有效
    if (!Array.isArray(muscles)) {
      console.warn('⚠️ muscles数据不是数组:', muscles)
      return []
    }
    
    // 过滤掉无效的muscle对象
    return muscles.filter(muscle => 
      muscle && 
      typeof muscle === 'object' && 
      typeof muscle.name === 'string' && 
      typeof muscle.points === 'string'
    )
  } catch (error) {
    console.error('❌ currentMuscles计算出错:', error)
    return []
  }
})

const activatedMusclesList = computed(() => {
  const result = []
  const muscles = props.muscleGroups || {}
  
  // 处理主要肌群
  if (muscles.primary && Array.isArray(muscles.primary)) {
    muscles.primary.forEach(muscle => {
      const mappedName = muscleMapping[muscle] || muscle
      result.push({
        name: muscle,
        displayName: mappedName,
        type: 'primary',
        intensity: 100
      })
    })
  }
  
  // 处理辅助肌群
  if (muscles.secondary && Array.isArray(muscles.secondary)) {
    muscles.secondary.forEach(muscle => {
      const mappedName = muscleMapping[muscle] || muscle
      result.push({
        name: muscle,
        displayName: mappedName,
        type: 'secondary',
        intensity: 70
      })
    })
  }
  
  // 处理稳定肌群
  if (muscles.stabilizer && Array.isArray(muscles.stabilizer)) {
    muscles.stabilizer.forEach(muscle => {
      const mappedName = muscleMapping[muscle] || muscle
      result.push({
        name: muscle,
        displayName: mappedName,
        type: 'stabilizer',
        intensity: 50
      })
    })
  }
  
  return result
})

const primaryMusclesCount = computed(() => {
  return activatedMusclesList.value.filter(m => m.type === 'primary').length
})

const secondaryMusclesCount = computed(() => {
  return activatedMusclesList.value.filter(m => m.type === 'secondary').length
})

const tooltipStyle = computed(() => {
  return {
    left: `${tooltipPosition.x}px`,
    top: `${tooltipPosition.y}px`,
    transform: 'translate(-50%, -100%)'
  }
})

// 方法
const getMuscleClass = (muscleName) => {
  // 防御性检查：确保muscleName有效
  if (!muscleName || typeof muscleName !== 'string') {
    console.warn('⚠️ getMuscleClass收到无效的muscleName:', muscleName)
    return 'inactive'
  }
  
  const muscles = props.muscleGroups || {}
  const muscleKey = getMuscleMappingKey(muscleName)
  
  // 如果muscleKey为空，直接返回inactive
  if (!muscleKey) {
    return 'inactive'
  }
  
  // 检查主要肌群
  if (muscles.primary && muscles.primary.some(m => isMatchingMuscle(m, muscleKey))) {
    return 'activated primary'
  }
  
  // 检查辅助肌群
  if (muscles.secondary && muscles.secondary.some(m => isMatchingMuscle(m, muscleKey))) {
    return 'activated secondary'
  }
  
  // 检查稳定肌群
  if (muscles.stabilizer && muscles.stabilizer.some(m => isMatchingMuscle(m, muscleKey))) {
    return 'activated stabilizer'
  }
  
  return 'inactive'
}

const isMatchingMuscle = (muscleFromData, polygonMuscle) => {
  // 防御性检查：确保参数有效
  if (!muscleFromData || !polygonMuscle || 
      typeof muscleFromData !== 'string' || 
      typeof polygonMuscle !== 'string') {
    return false
  }
  
  // 直接匹配
  if (muscleFromData === polygonMuscle) return true
  
  // 通过映射匹配
  const mappedMuscle = muscleMapping[muscleFromData]
  if (mappedMuscle === polygonMuscle) return true
  
  // 包含匹配（处理复合肌肉名称）
  if (polygonMuscle.includes(muscleFromData) || muscleFromData.includes(polygonMuscle)) {
    return true
  }
  
  // 特殊匹配规则
  const matchingRules = {
    'chest': ['胸肌', 'chest', 'pectorals'],
    'shoulders': ['肩膀', 'shoulders', 'deltoids', 'delts'],
    'biceps': ['肱二头肌', 'biceps'],
    'triceps': ['肱三头肌', 'triceps'],
    'abs': ['腹肌', 'abs', 'abdominals'],
    'back': ['背肌', 'back', 'lats', 'latissimus'],
    'quadriceps': ['股四头肌', 'quadriceps', 'quads'],
    'hamstrings': ['腘绳肌', 'hamstrings'],
    'glutes': ['臀大肌', 'glutes', 'gluteus'],
    'calves': ['小腿', 'calves', 'gastrocnemius']
  }
  
  for (const [key, variants] of Object.entries(matchingRules)) {
    if (variants.includes(muscleFromData) && polygonMuscle.includes(key)) {
      return true
    }
  }
  
  return false
}

const getMuscleMappingKey = (polygonMuscleName) => {
  // 防御性检查：确保polygonMuscleName存在且是字符串
  if (!polygonMuscleName || typeof polygonMuscleName !== 'string') {
    console.warn('⚠️ getMuscleMappingKey收到无效参数:', polygonMuscleName)
    return ''
  }
  
  // 从polygon名称中提取基础肌肉类型
  if (polygonMuscleName.includes('chest')) return 'chest'
  if (polygonMuscleName.includes('deltoids')) return 'shoulders'
  if (polygonMuscleName.includes('biceps')) return 'biceps'
  if (polygonMuscleName.includes('triceps')) return 'triceps'
  if (polygonMuscleName.includes('abs')) return 'abs'
  if (polygonMuscleName.includes('lats') || polygonMuscleName.includes('trapezius') || polygonMuscleName.includes('rhomboids')) return 'back'
  if (polygonMuscleName.includes('quadriceps')) return 'quadriceps'
  if (polygonMuscleName.includes('hamstrings')) return 'hamstrings'
  if (polygonMuscleName.includes('glutes')) return 'glutes'
  if (polygonMuscleName.includes('calves')) return 'calves'
  
  return polygonMuscleName
}

const getMuscleGroupName = (group) => {
  return muscleGroups[group]?.name || group
}

const getMuscleRole = (muscleName) => {
  const muscles = props.muscleGroups || {}
  const muscleKey = getMuscleMappingKey(muscleName)
  
  if (muscles.primary && muscles.primary.some(m => isMatchingMuscle(m, muscleKey))) {
    return '主要发力肌群'
  }
  if (muscles.secondary && muscles.secondary.some(m => isMatchingMuscle(m, muscleKey))) {
    return '辅助发力肌群'
  }
  if (muscles.stabilizer && muscles.stabilizer.some(m => isMatchingMuscle(m, muscleKey))) {
    return '稳定支撑肌群'
  }
  return ''
}

const getMuscleTypeLabel = (type) => {
  const typeMap = {
    primary: '主要',
    secondary: '辅助',
    stabilizer: '稳定'
  }
  return typeMap[type] || type
}

const getIntensityWidth = (type) => {
  const intensityMap = {
    primary: '100%',
    secondary: '70%',
    stabilizer: '50%'
  }
  return intensityMap[type] || '0%'
}

const showTooltip = (event, muscle) => {
  if (!props.interactive) return
  
  // 防御性检查：确保muscle对象有效
  if (!muscle || typeof muscle !== 'object') {
    console.warn('⚠️ showTooltip收到无效的muscle对象:', muscle)
    return
  }
  
  tooltipData.value = muscle
  updateTooltipPosition(event)
  tooltipVisible.value = true
}

const updateTooltipPosition = (event) => {
  try {
    const container = event.currentTarget.closest('.muscle-diagram-container')
    if (!container) {
      console.warn('⚠️ 找不到muscle-diagram-container容器')
      return
    }
    
    const rect = container.getBoundingClientRect()
    tooltipPosition.x = event.clientX - rect.left
    tooltipPosition.y = event.clientY - rect.top - 10
  } catch (error) {
    console.error('❌ updateTooltipPosition出错:', error)
  }
}

const hideTooltip = () => {
  tooltipVisible.value = false
}

const toggleMuscle = (muscle) => {
  if (!props.interactive) return
  
  // 防御性检查：确保muscle对象有效且有name属性
  if (!muscle || !muscle.name || typeof muscle.name !== 'string') {
    console.warn('⚠️ toggleMuscle收到无效的muscle对象:', muscle)
    return
  }
  
  if (selectedMuscles.value.has(muscle.name)) {
    selectedMuscles.value.delete(muscle.name)
  } else {
    selectedMuscles.value.add(muscle.name)
  }
}
</script>

<style scoped>
.muscle-diagram-pro {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.diagram-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.diagram-header h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 600;
}

.diagram-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.view-toggle {
  display: flex;
  align-items: center;
}

.diagram-legend {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #718096;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.legend-color.primary {
  background-color: #dc2626;
}

.legend-color.secondary {
  background-color: #f59e0b;
}

.legend-color.stabilizer {
  background-color: #10b981;
}

.diagram-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.muscle-diagram-container {
  position: relative;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: center;
  min-height: 400px;
}

.diagram-wrapper {
  position: relative;
  width: 100%;
  max-width: 300px;
}

.body-svg {
  width: 100%;
  height: auto;
  display: block;
}

.muscle-polygon {
  cursor: pointer;
  transition: all 0.3s ease;
  stroke: #e2e8f0;
  stroke-width: 0.5;
}

.muscle-polygon.inactive {
  fill: #f1f5f9;
  opacity: 0.6;
}

.muscle-polygon.activated {
  opacity: 1;
  stroke-width: 1;
}

.muscle-polygon.activated.primary {
  fill: #dc2626;
  stroke: #b91c1c;
}

.muscle-polygon.activated.secondary {
  fill: #f59e0b;
  stroke: #d97706;
}

.muscle-polygon.activated.stabilizer {
  fill: #10b981;
  stroke: #059669;
}

.muscle-polygon:hover {
  opacity: 0.8;
  stroke-width: 1.5;
  filter: brightness(1.1);
}

.muscle-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.85rem;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
  backdrop-filter: blur(4px);
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.muscle-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.muscle-group {
  opacity: 0.8;
  font-size: 0.8rem;
}

.muscle-role {
  font-size: 0.75rem;
  color: #fbbf24;
}

.activated-muscles {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.activated-muscles h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
}

.muscles-summary {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #3182ce;
}

.summary-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.85rem;
}

.total-count {
  font-weight: 600;
  color: #2d3748;
}

.primary-count {
  color: #dc2626;
}

.secondary-count {
  color: #f59e0b;
}

.muscles-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.muscle-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.muscle-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.muscle-item.primary {
  border-left-color: #dc2626;
  background: #fef2f2;
}

.muscle-item.secondary {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.muscle-item.stabilizer {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.muscle-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.muscle-indicator.primary {
  background-color: #dc2626;
}

.muscle-indicator.secondary {
  background-color: #f59e0b;
}

.muscle-indicator.stabilizer {
  background-color: #10b981;
}

.muscle-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.muscle-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.9rem;
}

.muscle-type {
  font-size: 0.75rem;
  color: #718096;
}

.muscle-intensity {
  width: 60px;
  flex-shrink: 0;
}

.intensity-bar {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.intensity-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.intensity-bar.primary .intensity-fill {
  background: #dc2626;
}

.intensity-bar.secondary .intensity-fill {
  background: #f59e0b;
}

.intensity-bar.stabilizer .intensity-fill {
  background: #10b981;
}

.no-muscles-activated {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 3rem;
  background: #f8f9fa;
  border-radius: 12px;
  color: #718096;
  text-align: center;
}

.no-muscles-icon {
  font-size: 2rem;
  color: #cbd5e0;
}

.no-muscles-text h5 {
  margin: 0 0 0.5rem 0;
  color: #4a5568;
}

.no-muscles-text p {
  margin: 0;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .diagram-body {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .muscle-diagram-container {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .diagram-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .diagram-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .diagram-legend {
    width: 100%;
    justify-content: space-around;
  }
  
  .muscle-diagram-pro {
    padding: 1rem;
  }
  
  .muscles-summary {
    padding: 0.75rem;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>