<!--
  计划详情对话框
  Plan Details Dialog Component
-->

<template>
  <el-dialog
    v-model="visible"
    title="计划详情"
    width="800px"
    class="plan-details-dialog"
  >
    <div v-if="planData" class="plan-details">
      <!-- 计划基本信息 -->
      <div class="plan-header">
        <div class="plan-title">
          <h3>{{ planData.daily_plan_name }}</h3>
          <div class="plan-meta">
            <el-tag :type="getStatusType(planData.is_active)">
              {{ planData.is_active ? '活跃' : '已禁用' }}
            </el-tag>
            <el-tag v-if="planData.is_rest_day" type="info">
              休息日
            </el-tag>
            <el-tag :type="getPriorityType(planData.priority)">
              优先级 {{ planData.priority }}
            </el-tag>
          </div>
        </div>
        
        <div class="plan-actions">
          <el-button size="small" @click="editPlan">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-dropdown @command="handleActionCommand">
            <el-button size="small">
              更多
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="copy">复制计划</el-dropdown-item>
                <el-dropdown-item command="move">移动到其他日期</el-dropdown-item>
                <el-dropdown-item command="history">查看历史</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除计划</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 时间和负荷信息 -->
      <div class="plan-schedule">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="schedule-item">
              <div class="item-label">
                <el-icon><Calendar /></el-icon>
                计划日期
              </div>
              <div class="item-value">{{ formatDate(planData.planned_date) }}</div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="schedule-item">
              <div class="item-label">
                <el-icon><Clock /></el-icon>
                训练时间
              </div>
              <div class="item-value">
                {{ getTimeSlotText(planData.time_slot) }}
                <span v-if="planData.start_time" class="start-time">
                  ({{ planData.start_time }})
                </span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="schedule-item">
              <div class="item-label">
                <el-icon><Timer /></el-icon>
                预计时长
              </div>
              <div class="item-value">{{ planData.estimated_duration || '-' }}分钟</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 负荷调整 -->
      <div class="load-adjustment">
        <h4>训练负荷</h4>
        <div class="load-info">
          <div class="load-bar-container">
            <div class="load-label">负荷调整系数</div>
            <div class="load-bar">
              <div 
                class="load-fill"
                :style="{ 
                  width: `${Math.min(100, (planData.load_adjustment || 1) * 50)}%`,
                  backgroundColor: getLoadColor(planData.load_adjustment || 1)
                }"
              ></div>
            </div>
            <div class="load-value">{{ ((planData.load_adjustment || 1) * 100).toFixed(0) }}%</div>
          </div>
          
          <div class="load-description">
            <span class="load-text">{{ getLoadDescription(planData.load_adjustment || 1) }}</span>
            <el-popover
              placement="top"
              title="负荷说明"
              :width="300"
              trigger="hover"
              content="负荷调整系数用于调整训练强度。100%为标准负荷，低于80%为轻度训练，高于120%为高强度训练。"
            >
              <template #reference>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </template>
            </el-popover>
          </div>
        </div>
      </div>

      <!-- 自定义配置 -->
      <div class="custom-config" v-if="planData.custom_config && Object.keys(planData.custom_config).length > 0">
        <h4>自定义配置</h4>
        <div class="config-grid">
          <div 
            v-for="(value, key) in planData.custom_config"
            :key="key"
            class="config-item"
          >
            <span class="config-key">{{ formatConfigKey(key) }}:</span>
            <span class="config-value">{{ formatConfigValue(value) }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="plan-notes" v-if="planData.notes">
        <h4>备注说明</h4>
        <div class="notes-content">{{ planData.notes }}</div>
      </div>

      <!-- 执行状态和历史 -->
      <div class="execution-status">
        <h4>执行状态</h4>
        <el-timeline>
          <el-timeline-item
            :timestamp="formatDateTime(planData.created_at)"
            type="primary"
            icon="Plus"
          >
            计划创建
            <span v-if="planData.assigned_by" class="creator">
              - 由用户 #{{ planData.assigned_by }} 创建
            </span>
          </el-timeline-item>
          
          <!-- 模拟一些历史记录 -->
          <el-timeline-item
            v-if="planData.updated_at && planData.updated_at !== planData.created_at"
            :timestamp="formatDateTime(planData.updated_at)"
            type="warning"
            icon="Edit"
          >
            计划更新
          </el-timeline-item>
          
          <el-timeline-item
            v-if="!planData.is_active"
            timestamp="待定"
            type="info"
            icon="CircleClose"
          >
            计划已禁用
          </el-timeline-item>
          
          <!-- 计划执行状态（如果是过去的日期） -->
          <el-timeline-item
            v-if="isPastDate(planData.planned_date)"
            timestamp=""
            :type="getExecutionStatusType()"
            :icon="getExecutionStatusIcon()"
          >
            {{ getExecutionStatusText() }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 相关统计 -->
      <div class="related-stats">
        <h4>相关统计</h4>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic
              title="在同一周期中"
              :value="0"
              suffix="次安排"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="历史执行率"
              :value="85"
              suffix="%"
              :value-style="{ color: '#3f8600' }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="平均完成度"
              :value="92"
              suffix="%"
              :value-style="{ color: '#cf1322' }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="用户反馈"
              :value="4.2"
              suffix="/ 5.0"
              :precision="1"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 推荐调整 -->
      <div class="recommendations" v-if="recommendations.length > 0">
        <h4>推荐调整</h4>
        <div class="recommendation-list">
          <div 
            v-for="(rec, index) in recommendations"
            :key="index"
            class="recommendation-item"
            :class="`rec-${rec.type}`"
          >
            <el-icon>
              <component :is="getRecommendationIcon(rec.type)" />
            </el-icon>
            <span class="rec-message">{{ rec.message }}</span>
            <el-button 
              v-if="rec.action"
              size="small" 
              text 
              @click="applyRecommendation(rec)"
            >
              应用
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="no-plan">
      <el-empty description="没有计划数据" />
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="executeNow" v-if="canExecute">
          立即执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Edit,
  ArrowDown,
  Calendar,
  Clock,
  Timer,
  QuestionFilled,
  Plus,
  CircleClose,
  Warning,
  Success,
  Info
} from '@element-plus/icons-vue'

// Props & Emits
interface Props {
  modelValue: boolean
  planData?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', plan: any): void
  (e: 'delete', plan: any): void
  (e: 'execute', plan: any): void
}

const props = withDefaults(defineProps<Props>(), {
  planData: null
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)

// 模拟推荐数据
const recommendations = ref([
  {
    type: 'info',
    message: '建议在训练前进行5-10分钟热身',
    action: 'add_warmup'
  },
  {
    type: 'warning', 
    message: '当前负荷较高，注意休息和恢复',
    action: 'adjust_load'
  }
])

// 计算属性
const canExecute = computed(() => {
  if (!props.planData) return false
  
  const today = new Date().toISOString().split('T')[0]
  const planDate = props.planData.planned_date
  
  return planDate === today && props.planData.is_active
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const editPlan = () => {
  emit('edit', props.planData)
}

const executeNow = () => {
  emit('execute', props.planData)
  ElMessage.success('开始执行训练计划')
}

const handleActionCommand = async (command: string) => {
  switch (command) {
    case 'copy':
      handleCopyPlan()
      break
    case 'move':
      handleMovePlan()
      break
    case 'history':
      handleViewHistory()
      break
    case 'delete':
      await handleDeletePlan()
      break
  }
}

const handleCopyPlan = () => {
  // TODO: 实现复制计划
  ElMessage.success('计划已复制')
}

const handleMovePlan = () => {
  // TODO: 实现移动计划
  ElMessage.info('移动功能开发中...')
}

const handleViewHistory = () => {
  // TODO: 实现查看历史
  ElMessage.info('历史查看功能开发中...')
}

const handleDeletePlan = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个训练计划吗？删除后无法恢复。',
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    emit('delete', props.planData)
    visible.value = false
    
  } catch {
    // 用户取消删除
  }
}

const applyRecommendation = (recommendation: any) => {
  // TODO: 实现应用推荐
  ElMessage.success(`已应用推荐: ${recommendation.message}`)
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  } catch {
    return dateStr
  }
}

const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '-'
  
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}

// 获取状态类型
const getStatusType = (isActive: boolean): string => {
  return isActive ? 'success' : 'info'
}

const getPriorityType = (priority: number): string => {
  if (priority >= 4) return 'danger'
  if (priority >= 3) return 'warning'
  if (priority >= 2) return 'primary'
  return 'info'
}

// 获取时间段文本
const getTimeSlotText = (timeSlot: string): string => {
  const slotMap: Record<string, string> = {
    'morning': '上午',
    'afternoon': '下午',
    'evening': '晚上',
    'all_day': '全天'
  }
  return slotMap[timeSlot] || timeSlot || '-'
}

// 获取负荷颜色和描述
const getLoadColor = (load: number): string => {
  if (load < 0.8) return '#52c41a'      // 绿色 - 轻度
  if (load <= 1.2) return '#faad14'     // 黄色 - 标准
  if (load <= 1.5) return '#fa8c16'     // 橙色 - 高度
  return '#f5222d'                      // 红色 - 极高
}

const getLoadDescription = (load: number): string => {
  if (load < 0.8) return '轻度负荷'
  if (load <= 1.2) return '标准负荷'
  if (load <= 1.5) return '高强度负荷'
  return '极高强度负荷'
}

// 格式化配置键值
const formatConfigKey = (key: string): string => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatConfigValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

// 检查是否为过去日期
const isPastDate = (dateStr: string): boolean => {
  if (!dateStr) return false
  
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  const planDate = new Date(dateStr)
  planDate.setHours(0, 0, 0, 0)
  
  return planDate < today
}

// 获取执行状态
const getExecutionStatusType = (): string => {
  // 模拟执行状态
  return 'success'
}

const getExecutionStatusIcon = (): string => {
  return 'Success'
}

const getExecutionStatusText = (): string => {
  return '计划已完成'
}

// 获取推荐图标
const getRecommendationIcon = (type: string): any => {
  const iconMap: Record<string, any> = {
    'warning': Warning,
    'success': Success,
    'info': Info
  }
  return iconMap[type] || Info
}
</script>

<style lang="scss" scoped>
.plan-details-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
  }
}

.plan-details {
  .plan-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    
    .plan-title {
      flex: 1;
      
      h3 {
        margin: 0 0 8px 0;
        font-size: 20px;
        color: #303133;
      }
      
      .plan-meta {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
    
    .plan-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .plan-schedule {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    
    .schedule-item {
      text-align: center;
      
      .item-label {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
        
        .el-icon {
          font-size: 14px;
        }
      }
      
      .item-value {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        
        .start-time {
          font-size: 12px;
          color: #606266;
          font-weight: 400;
        }
      }
    }
  }
  
  .load-adjustment {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .load-info {
      .load-bar-container {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .load-label {
          font-size: 14px;
          color: #606266;
          min-width: 100px;
        }
        
        .load-bar {
          flex: 1;
          height: 8px;
          background: #f0f2f5;
          border-radius: 4px;
          overflow: hidden;
          
          .load-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
          }
        }
        
        .load-value {
          font-weight: 600;
          color: #303133;
          min-width: 50px;
          text-align: right;
        }
      }
      
      .load-description {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .load-text {
          font-size: 14px;
          color: #606266;
        }
        
        .help-icon {
          font-size: 14px;
          color: #c0c4cc;
          cursor: help;
        }
      }
    }
  }
  
  .custom-config, .plan-notes, .execution-status, .related-stats, .recommendations {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    
    .config-item {
      display: flex;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .config-key {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
        min-width: 100px;
      }
      
      .config-value {
        color: #303133;
        flex: 1;
        word-break: break-all;
      }
    }
  }
  
  .notes-content {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    color: #606266;
    line-height: 1.6;
    white-space: pre-wrap;
  }
  
  .execution-status {
    :deep(.el-timeline-item__timestamp) {
      font-size: 12px;
    }
    
    .creator {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .recommendation-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      border-radius: 6px;
      
      &.rec-info {
        background: #f0f9ff;
        color: #1890ff;
      }
      
      &.rec-warning {
        background: #fff7e6;
        color: #fa8c16;
      }
      
      &.rec-success {
        background: #f6ffed;
        color: #52c41a;
      }
      
      .el-icon {
        font-size: 16px;
      }
      
      .rec-message {
        flex: 1;
        font-size: 14px;
      }
    }
  }
}

.no-plan {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .plan-schedule {
    .el-row {
      flex-direction: column;
      gap: 16px;
    }
  }
  
  .load-bar-container {
    flex-direction: column;
    align-items: stretch;
    
    .load-label, .load-value {
      text-align: left;
      min-width: auto;
    }
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendation-item {
    flex-direction: column;
    align-items: stretch;
    text-align: left;
  }
}
</style>