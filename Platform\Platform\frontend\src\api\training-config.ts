/**
 * 训练配置相关的API服务
 * 处理专项事件、性别分类、等级标准等数据获取
 */

import request from '@/utils/request'
import type {
  SpecialtyCategory,
  SpecialtyEvent,
  SpecialtyApiResponse,
  GenderType,
  AthleteLevel,
  TrainingConfig
} from '@/types/training-config'

/**
 * 获取所有专项分类和事件
 */
export async function getSpecialtyCategories(): Promise<SpecialtyCategory[]> {
  // 暂时直接返回基于数据库结构的专项分类，避免API调用
  // TODO: 后续需要实现对应的后端API端点
  console.log('🏃‍♂️ 使用基于track_field_item表的专项分类数据')
  return getActualSpecialtyCategories()
}

/**
 * 根据性别筛选专项事件
 */
export async function getSpecialtiesByGender(gender: GenderType): Promise<SpecialtyEvent[]> {
  // 从本地专项分类数据中按性别筛选
  const allCategories = await getSpecialtyCategories()
  const allEvents = allCategories.flatMap(cat => cat.events)
  
  console.log(`🎯 为性别 ${gender} 筛选专项事件`)
  
  if (gender === 'ALL') {
    return allEvents
  } else {
    const filteredEvents = allEvents.filter(event => event.genderSupport.includes(gender))
    console.log(`📊 找到 ${filteredEvents.length} 个适用专项`)
    return filteredEvents
  }
}

/**
 * 从level_standards表动态生成专项分类
 * 这是后端API的备选方案
 */
async function generateSpecialtiesFromLevelStandards(): Promise<SpecialtyCategory[]> {
  try {
    // 使用从数据库直接获取的实际专项数据
    return getActualSpecialtyCategories()
    
    // 基于事件名称进行智能分类
    const categories: SpecialtyCategory[] = [
      {
        id: 'sprint_hurdles',
        name: '短跨类',
        code: 'sprint_hurdles',
        description: '短跑和跨栏项目',
        events: []
      },
      {
        id: 'middle_long_distance', 
        name: '中长跑类',
        code: 'middle_long_distance',
        description: '中距离和长距离跑步项目',
        events: []
      },
      {
        id: 'steeplechase',
        name: '障碍跑类', 
        code: 'steeplechase',
        description: '障碍跑项目',
        events: []
      },
      {
        id: 'race_walking',
        name: '竞走类',
        code: 'race_walking',
        description: '竞走项目',
        events: []
      },
      {
        id: 'jumping',
        name: '跳跃类',
        code: 'jumping', 
        description: '跳高、跳远、撑杆跳等项目',
        events: []
      },
      {
        id: 'throwing',
        name: '投掷类',
        code: 'throwing',
        description: '铅球、铁饼、标枪、链球等项目', 
        events: []
      },
      {
        id: 'combined_events',
        name: '全能类',
        code: 'combined_events',
        description: '十项全能、七项全能等综合项目',
        events: []
      }
    ]
    
    // 事件名称到分类的映射规则
    const classificationRules = {
      sprint_hurdles: [
        'SPRINT_100M', '100M', '200M', '400M',
        'HURDLES_100M', 'HURDLES_110M', 'HURDLES_400M',
        '100m', '200m', '400m', '100米', '200米', '400米', 
        '栏', '短跑'
      ],
      middle_long_distance: [
        'MIDDLE_800M', 'MIDDLE_1500M', 'LONG_3000M', 'LONG_5000M', 'LONG_10000M',
        'MARATHON', 'HALF_MARATHON',
        '800M', '1500M', '3000M', '5000M', '10000M',
        '800', '1500', '3000', '5000', '10000', '马拉松'
      ],
      steeplechase: [
        'STEEPLECHASE_2000M', 'STEEPLECHASE_3000M', 
        '障碍', 'steeplechase', '2000M', '3000M'
      ],
      race_walking: [
        'RACE_WALK_5K', 'RACE_WALK_10K', 'RACE_WALK_20K',
        '5000M_WALK', '10000M_WALK', '20KM_WALK',
        '竞走', 'walk', '5公里', '10公里', '20公里'
      ],
      jumping: [
        'HIGH_JUMP', 'POLE_VAULT', 'LONG_JUMP', 'TRIPLE_JUMP',
        '跳高', '撑杆跳', '跳远', '三级跳', 'jump', 'vault'
      ],
      throwing: [
        'SHOT_PUT', 'DISCUS', 'HAMMER', 'JAVELIN',
        '铅球', '铁饼', '链球', '标枪', '投掷', 'throw'
      ],
      combined_events: [
        'PENTATHLON', 'HEPTATHLON', 'DECATHLON', 'TETRATHLON',
        '五项', '七项', '十项', '四项', '全能', 'athlon'
      ]
    }
    
    // 根据eventData中的事件进行分类
    Object.entries(eventData).forEach(([eventName, genderInfo]: [string, any]) => {
      const genderSupport = genderInfo.genders as GenderType[]
      
      // 找到匹配的分类
      let matchedCategory = 'sprint_hurdles' // 默认分类
      
      for (const [categoryKey, keywords] of Object.entries(classificationRules)) {
        if (keywords.some(keyword => eventName.toUpperCase().includes(keyword.toUpperCase()))) {
          matchedCategory = categoryKey
          break
        }
      }
      
      // 创建事件对象
      const specialtyEvent: SpecialtyEvent = {
        id: eventName,
        name: formatEventName(eventName),
        code: eventName,
        category: matchedCategory,
        genderSupport,
        description: `${formatEventName(eventName)} - 支持性别: ${genderSupport.join('/')}`
      }
      
      // 添加到对应分类
      const category = categories.find(cat => cat.code === matchedCategory)
      if (category) {
        category.events.push(specialtyEvent)
      }
    })
    
    // 过滤掉空的分类
    return categories.filter(cat => cat.events.length > 0)
    
  } catch (error) {
    console.error('从level_standards生成专项分类失败:', error)
    return getDefaultSpecialtyCategories()
  }
}

/**
 * 格式化事件名称为用户友好的显示名称
 */
function formatEventName(eventCode: string): string {
  const nameMap: Record<string, string> = {
    // 短跑
    'SPRINT_100M': '100米短跑',
    'SPRINT_200M': '200米短跑',
    'SPRINT_400M': '400米短跑', 
    '100M': '100米',
    '200M': '200米',
    '400M': '400米',
    
    // 中长跑
    'MIDDLE_800M': '800米中跑',
    'MIDDLE_1500M': '1500米中跑',
    'LONG_3000M': '3000米长跑',
    'LONG_5000M': '5000米长跑',
    'LONG_10000M': '10000米长跑',
    '800M': '800米',
    '1500M': '1500米',
    '3000M': '3000米',
    '5000M': '5000米',
    '10000M': '10000米',
    'MARATHON': '马拉松',
    'HALF_MARATHON': '半程马拉松',
    
    // 跨栏
    'HURDLES_100M': '100米栏',
    'HURDLES_110M': '110米栏', 
    'HURDLES_400M': '400米栏',
    
    // 障碍跑
    'STEEPLECHASE_2000M': '2000米障碍',
    'STEEPLECHASE_3000M': '3000米障碍',
    
    // 竞走
    'RACE_WALK_5K': '5公里竞走',
    'RACE_WALK_10K': '10公里竞走',
    'RACE_WALK_20K': '20公里竞走',
    '5000M_WALK': '5000米竞走',
    '10000M_WALK': '10000米竞走',
    '20KM_WALK': '20公里竞走',
    
    // 跳跃
    'HIGH_JUMP': '跳高',
    'POLE_VAULT': '撑杆跳高',
    'LONG_JUMP': '跳远',
    'TRIPLE_JUMP': '三级跳远',
    
    // 投掷
    'SHOT_PUT': '铅球',
    'DISCUS': '铁饼',
    'HAMMER': '链球',
    'JAVELIN': '标枪',
    
    // 全能
    'TETRATHLON': '四项全能',
    'PENTATHLON': '五项全能', 
    'HEPTATHLON': '七项全能',
    'DECATHLON': '十项全能'
  }
  
  return nameMap[eventCode] || eventCode
}

/**
 * 获取实际的专项分类数据（基于数据库track_field_item表）
 * 混合模式：通用项目（男女都有）使用单一分类，性别特定项目使用男/女分类
 */
function getActualSpecialtyCategories(): SpecialtyCategory[] {
  return [
    // 短跑+跨栏混合分类（根据性别动态调整）
    {
      id: 'sprint_hurdles',
      name: '短跨类',
      code: '短跨类',
      description: '短跑和跨栏项目',
      events: [
        // 短跑 - 男女通用
        { id: '100M', name: '100米', code: '100M', category: '短跨类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '200M', name: '200米', code: '200M', category: '短跨类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '400M', name: '400米', code: '400M', category: '短跨类', genderSupport: ['MALE', 'FEMALE'] },
        // 跨栏 - 性别特定
        { id: '110M_HURDLES', name: '110米栏', code: '110M_HURDLES', category: '短跨类', genderSupport: ['MALE'] },
        { id: '100M_HURDLES', name: '100米栏', code: '100M_HURDLES', category: '短跨类', genderSupport: ['FEMALE'] },
        { id: '400M_HURDLES', name: '400米栏', code: '400M_HURDLES', category: '短跨类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    },
    // 中长跑（包含所有中长距离项目）
    {
      id: 'middle_long_distance',
      name: '中长跑类',
      code: '中长跑类',
      description: '中距离和长距离跑步项目',
      events: [
        { id: '800M', name: '800米', code: '800M', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '1500M', name: '1500米', code: '1500M', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '3000M', name: '3000米', code: '3000M', category: '中长跑类', genderSupport: ['FEMALE'] },
        { id: '5000M', name: '5000米', code: '5000M', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '10000M', name: '10000米', code: '10000M', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'MARATHON', name: '马拉松', code: 'MARATHON', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'HALF_MARATHON', name: '半程马拉松', code: 'HALF_MARATHON', category: '中长跑类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    },
    // 障碍跑
    {
      id: 'steeplechase',
      name: '障碍跑类',
      code: '障碍跑类',
      description: '障碍跑项目',
      events: [
        { id: '2000M_STEEPLECHASE', name: '2000米障碍', code: '2000M_STEEPLECHASE', category: '障碍跑类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '3000M_STEEPLECHASE', name: '3000米障碍', code: '3000M_STEEPLECHASE', category: '障碍跑类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    },
    // 竞走
    {
      id: 'race_walking',
      name: '竞走类',
      code: '竞走类',
      description: '竞走项目',
      events: [
        { id: '5000M_WALK', name: '5000米竞走', code: '5000M_WALK', category: '竞走类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '10000M_WALK', name: '10000米竞走', code: '10000M_WALK', category: '竞走类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '20KM_WALK', name: '20公里竞走', code: '20KM_WALK', category: '竞走类', genderSupport: ['MALE', 'FEMALE'] },
        { id: '50KM_WALK', name: '50公里竞走', code: '50KM_WALK', category: '竞走类', genderSupport: ['MALE'] }
      ]
    },
    // 跳跃
    {
      id: 'jumping',
      name: '跳跃类',
      code: '跳跃类',
      description: '跳高、跳远、撑杆跳等项目',
      events: [
        { id: 'HIGH_JUMP', name: '跳高', code: 'HIGH_JUMP', category: '跳跃类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'POLE_VAULT', name: '撑杆跳高', code: 'POLE_VAULT', category: '跳跃类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'LONG_JUMP', name: '跳远', code: 'LONG_JUMP', category: '跳跃类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'TRIPLE_JUMP', name: '三级跳远', code: 'TRIPLE_JUMP', category: '跳跃类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    },
    // 投掷
    {
      id: 'throwing',
      name: '投掷类',
      code: '投掷类',
      description: '铅球、铁饼、标枪、链球等项目',
      events: [
        { id: 'SHOT_PUT', name: '铅球', code: 'SHOT_PUT', category: '投掷类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'DISCUS', name: '铁饼', code: 'DISCUS', category: '投掷类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'HAMMER', name: '链球', code: 'HAMMER', category: '投掷类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'JAVELIN', name: '标枪', code: 'JAVELIN', category: '投掷类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    },
    // 全能
    {
      id: 'combined_events',
      name: '全能类',
      code: '全能类',
      description: '十项全能、七项全能等综合项目',
      events: [
        { id: 'DECATHLON', name: '十项全能', code: 'DECATHLON', category: '全能类', genderSupport: ['MALE'] },
        { id: 'HEPTATHLON', name: '七项全能', code: 'HEPTATHLON', category: '全能类', genderSupport: ['MALE', 'FEMALE'] },
        { id: 'PENTATHLON', name: '五项全能', code: 'PENTATHLON', category: '全能类', genderSupport: ['FEMALE'] },
        { id: 'TETRATHLON', name: '四项全能', code: 'TETRATHLON', category: '全能类', genderSupport: ['MALE', 'FEMALE'] }
      ]
    }
  ]
}

/**
 * 获取默认专项分类（当API调用失败时使用）
 */
function getDefaultSpecialtyCategories(): SpecialtyCategory[] {
  return [
    {
      id: 'sprint_hurdles',
      name: '短跨类',
      code: 'sprint_hurdles',
      description: '短跑和跨栏项目',
      events: [
        {
          id: '100M',
          name: '100米',
          code: '100M',
          category: 'sprint_hurdles',
          genderSupport: ['MALE', 'FEMALE']
        },
        {
          id: '200M', 
          name: '200米',
          code: '200M',
          category: 'sprint_hurdles',
          genderSupport: ['MALE', 'FEMALE']
        },
        {
          id: 'HURDLES_110M',
          name: '110米栏',
          code: 'HURDLES_110M',
          category: 'sprint_hurdles',
          genderSupport: ['MALE']
        },
        {
          id: 'HURDLES_100M',
          name: '100米栏', 
          code: 'HURDLES_100M',
          category: 'sprint_hurdles',
          genderSupport: ['FEMALE']
        }
      ]
    },
    {
      id: 'jumping',
      name: '跳跃类',
      code: 'jumping',
      description: '跳高、跳远、撑杆跳等项目',
      events: [
        {
          id: 'HIGH_JUMP',
          name: '跳高',
          code: 'HIGH_JUMP', 
          category: 'jumping',
          genderSupport: ['MALE', 'FEMALE']
        },
        {
          id: 'LONG_JUMP',
          name: '跳远',
          code: 'LONG_JUMP',
          category: 'jumping',
          genderSupport: ['MALE', 'FEMALE']
        }
      ]
    }
  ]
}

/**
 * 验证训练配置
 */
export async function validateTrainingConfig(config: TrainingConfig): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = []
  
  // 验证性别和专项的兼容性
  if (config.gender && config.specialty && config.specialty.length > 0) {
    const availableEvents = await getSpecialtiesByGender(config.gender)
    const availableEventCodes = availableEvents.map(event => event.code)
    
    const invalidSpecialties = config.specialty.filter(specialty => 
      !availableEventCodes.includes(specialty)
    )
    
    if (invalidSpecialties.length > 0) {
      errors.push(`所选专项 ${invalidSpecialties.join(', ')} 不支持性别 ${config.gender}`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 格式化配置为显示文本
 */
export function formatConfigForDisplay(config: TrainingConfig): string {
  const parts: string[] = []
  
  if (config.gender) {
    const genderMap = { 'ALL': '全部', 'MALE': '男性', 'FEMALE': '女性' }
    parts.push(`性别: ${genderMap[config.gender]}`)
  }
  
  if (config.level) {
    const levelMap = {
      'AMATEUR': '业余',
      'THIRD_CLASS': '三级',
      'SECOND_CLASS': '二级', 
      'FIRST_CLASS': '一级',
      'MASTER': '健将',
      'INTERNATIONAL_MASTER': '国际健将'
    }
    parts.push(`等级: ${levelMap[config.level]}`)
  }
  
  if (config.specialty && config.specialty.length > 0) {
    parts.push(`专项: ${config.specialty.length}项`)
  }
  
  return parts.length > 0 ? parts.join(' | ') : '未配置'
}

/**
 * 获取等级标准数据
 */
export async function getLevelStandards(gender?: GenderType, event?: string) {
  try {
    const params = new URLSearchParams()
    if (gender && gender !== 'ALL') params.append('gender', gender)
    if (event) params.append('event', event)
    
    const response = await request({
      url: `/api/v1/level-standards?${params.toString()}`,
      method: 'get'
    })
    return response.data
  } catch (error) {
    console.error('获取等级标准失败:', error)
    throw error
  }
}