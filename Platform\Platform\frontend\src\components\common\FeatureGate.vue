<template>
  <div v-if="shouldRender">
    <!-- 功能启用时渲染主要内容 -->
    <slot />
  </div>
  <div v-else-if="showFallback">
    <!-- 功能禁用时渲染后备内容 -->
    <slot name="fallback" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { isFeatureEnabled, type FeatureFlags } from '@/config/features'

/**
 * FeatureGate 组件属性
 */
interface Props {
  /** 要检查的功能特性名称 */
  feature: keyof FeatureFlags
  /** 是否显示后备内容 */
  showFallback?: boolean
  /** 反转逻辑 - 当功能禁用时显示内容 */
  invert?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFallback: true,
  invert: false
})

/**
 * 计算是否应该渲染主要内容
 */
const shouldRender = computed(() => {
  const enabled = isFeatureEnabled(props.feature)
  return props.invert ? !enabled : enabled
})

/**
 * 在开发环境下提供调试信息
 */
if (import.meta.env.DEV) {
  console.log(`FeatureGate: ${props.feature} = ${shouldRender.value}`)
}
</script>

<style scoped>
/* 功能门组件不需要特殊样式 */
</style>