# License and Attribution

## Original Library

The SVG anatomy data in this directory is derived from the [vue-human-muscle-anatomy](https://github.com/lucawahlen/vue-human-muscle-anatomy) library.

**Original License**: MIT
**Original Author**: <PERSON> (@lucawahlen)
**Original Repository**: https://github.com/lucawahlen/vue-human-muscle-anatomy

## Modifications

This forked implementation includes the following enhancements:

1. **Native Hair Features**: Added authentic hair SVG paths directly integrated into the anatomy data for better gender differentiation
2. **TypeScript Support**: Full TypeScript definitions and type safety
3. **Custom Vue 3 Component**: Rebuilt using Vue 3 Composition API
4. **Enhanced Styling**: Improved outline rendering and color management
5. **Modular Architecture**: Separated male and female anatomy data for better maintainability

## License Compliance

This derivative work maintains the MIT license terms of the original project:

```
MIT License

Copyright (c) 2023 Luca Wahlen
Copyright (c) 2024 Platform Project (derivative work)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## Acknowledgments

We thank Luca Wahlen for creating the original vue-human-muscle-anatomy library that serves as the foundation for this enhanced implementation.