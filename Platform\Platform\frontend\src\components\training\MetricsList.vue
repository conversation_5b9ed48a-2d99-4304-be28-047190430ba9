<template>
  <div class="metrics-library-wrapper">
    <div class="list-header" v-if="category">
      <h3>{{ category.name }}</h3>
      <p v-if="category.description">{{ category.description }}</p>
    </div>

    <!-- 使用表格显示指标 -->
    <el-table 
      :data="metrics" 
      class="metrics-table" 
      v-if="metrics.length > 0"
      style="width: 100%"
    >
      <el-table-column prop="name" label="指标名称" min-width="150" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="unit" label="单位" width="80" />
      <el-table-column prop="type" label="类型" width="100">
        <template #default="scope">
          <el-tag size="small">{{ scope.row.type || '数值' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <el-empty description="暂无指标数据" />
    </div>
  </div>
</template>

<script setup>
import { Edit, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  metrics: {
    type: Array,
    default: () => []
  },
  category: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['edit', 'delete'])

// 方法
const handleEdit = (metric) => {
  emit('edit', metric)
}

const handleDelete = (metric) => {
  emit('delete', metric)
}
</script>

<style scoped>
.metrics-library-wrapper {
  padding: 16px 0;
}

.list-header {
  margin-bottom: 16px;
}

.list-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.list-header p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.metrics-table {
  border-radius: 4px;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
}
</style>