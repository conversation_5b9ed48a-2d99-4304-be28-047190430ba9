<template>
  <div class="exercise-template-preview">
    <div class="preview-panel">
      <div class="preview-content">
        <h3>{{ title || '训练表格预览' }}</h3>
        
        <!-- 训练参数设置区域 -->
        <div class="training-params-section preview-section">
          <div class="section-header">
            <span class="section-title">⚙️ 训练参数设置</span>
          </div>
          <div class="params-grid">
            <!-- 环境指标部分 -->
            <template v-for="metric in environmentMetrics" :key="metric.id">
              <div class="param-item">
                <span class="param-label">{{ metric.name || metric.metric_name }}:</span>
                <span class="param-value">{{ getEnvironmentValue(metric.id) || '[待设定]' }}</span>
                <span class="param-unit">{{ metric.unit || '' }}</span>
              </div>
            </template>
            
            <!-- 固定的组数次数指标 -->
            <div class="param-item">
              <span class="param-label">目标组数:</span>
              <span class="param-value">{{ targetSets }}</span>
              <span class="param-unit">组</span>
            </div>
            <div class="param-item">
              <span class="param-label">目标次数:</span>
              <span class="param-value">{{ displayTargetReps }}</span>
              <span class="param-unit">次</span>
            </div>
            <div class="param-item">
              <span class="param-label">实际组数:</span>
              <span class="param-value actual-placeholder">-</span>
              <span class="param-unit">组</span>
            </div>
            <div class="param-item">
              <span class="param-label">实际次数:</span>
              <span class="param-value actual-placeholder">-</span>
              <span class="param-unit">次</span>
            </div>
          </div>
        </div>
        
        <!-- 预览表格 -->
        <div class="preview-table-container">
          <table class="preview-table">
            <thead>
              <tr>
                <th>序号</th>
                <!-- 只显示表格列指标（排除组数、次数等结构性参数） -->
                <th v-for="column in filteredTableColumns" :key="column.key">
                  {{ column.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in previewTableData" :key="row.id">
                <td>{{ row.display_label || row.sequence }}</td>
                <!-- 动态生成表格数据列 -->
                <td v-for="column in filteredTableColumns" :key="`${row.id}-${column.key}`">
                  <input 
                    type="text" 
                    placeholder="" 
                    :class="['preview-input', { 'readonly-input': column.readonly }]"
                    :readonly="column.readonly || readonly"
                    :title="column.readonly ? '训练时填写' : ''"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 训练统计 -->
        <div v-if="showStats" class="training-stats">
          <div class="stat-row">
            <span>配置进度:</span>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            </div>
            <span>{{ progressPercentage }}%</span>
          </div>
          <div class="stat-row">
            <span>总指标数:</span>
            <span>{{ totalMetricsCount }}</span>
          </div>
          <div class="stat-row">
            <span>预计训练时长:</span>
            <span>{{ estimatedDuration }}秒</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUnifiedMetricsState } from '@/composables/useUnifiedMetricsState'

// Props
const props = defineProps({
  // 组件标题
  title: {
    type: String,
    default: '训练表格预览'
  },
  
  // 度量指标数组
  measurementMetrics: {
    type: Array,
    default: () => []
  },
  
  // 变化指标数组
  variationMetrics: {
    type: Array,
    default: () => []
  },
  
  // 环境指标数组
  environmentMetrics: {
    type: Array,
    default: () => []
  },
  
  // 训练配置
  trainingConfig: {
    type: Object,
    default: () => ({
      sets: 3,
      reps: 10,
      measurement_granularity: 'set'
    })
  },
  
  // 环境指标的值映射
  environmentValues: {
    type: Object,
    default: () => ({})
  },
  
  // 是否显示统计信息
  showStats: {
    type: Boolean,
    default: true
  },
  
  // 是否为只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  
  // 模板数据（用于兼容不同的数据结构）
  templateData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update-progress'])

// 🆕 使用统一指标状态管理
const { showTimeMetrics, isTimeMetric } = useUnifiedMetricsState()

// 响应式数据
const targetSets = computed(() => {
  console.log('🔍 targetSets计算 - templateData keys:', props.templateData ? Object.keys(props.templateData) : 'null')
  console.log('🔍 templateData?.training_config:', props.templateData?.training_config)
  console.log('🔍 templateData?.config:', props.templateData?.config)
  console.log('🔍 trainingConfig prop:', props.trainingConfig)
  console.log('🔍 trainingConfig prop type:', typeof props.trainingConfig)
  
  // 优先使用显式传入的trainingConfig
  if (props.trainingConfig?.sets) {
    console.log('✅ 使用trainingConfig prop中的sets:', props.trainingConfig.sets)
    return props.trainingConfig.sets
  }
  
  // 然后尝试templateData中的多种数据源
  if (props.templateData?.training_config?.sets) {
    console.log('✅ 使用templateData.training_config中的sets:', props.templateData.training_config.sets)
    return props.templateData.training_config.sets
  }
  
  if (props.templateData?.config?.sets) {
    console.log('✅ 使用templateData.config中的sets:', props.templateData.config.sets)
    return props.templateData.config.sets
  }
  
  // 最后使用默认值
  const fallbackValue = 3
  console.log('❌ 使用默认值:', fallbackValue)
  return fallbackValue
})

const targetReps = computed(() => {
  console.log('🔍 targetReps计算 - templateData?.training_config:', props.templateData?.training_config)
  console.log('🔍 targetReps计算 - templateData?.config:', props.templateData?.config)
  console.log('🔍 trainingConfig prop:', props.trainingConfig)
  
  // 优先使用显式传入的trainingConfig
  if (props.trainingConfig?.reps) {
    console.log('✅ 使用trainingConfig prop中的reps:', props.trainingConfig.reps)
    return props.trainingConfig.reps
  }
  
  // 然后尝试templateData中的多种数据源
  if (props.templateData?.training_config?.reps) {
    console.log('✅ 使用templateData.training_config中的reps:', props.templateData.training_config.reps)
    return props.templateData.training_config.reps
  }
  
  if (props.templateData?.config?.reps) {
    console.log('✅ 使用templateData.config中的reps:', props.templateData.config.reps)
    return props.templateData.config.reps
  }
  
  // 最后使用默认值
  const fallbackValue = 10
  console.log('❌ 使用默认值:', fallbackValue)
  return fallbackValue
})

const displayTargetReps = computed(() => targetReps.value)

const measurementGranularity = computed(() => {
  // 优先使用显式传入的trainingConfig
  if (props.trainingConfig?.measurement_granularity) {
    return props.trainingConfig.measurement_granularity
  }
  
  // 然后尝试templateData中的多种数据源
  if (props.templateData?.training_config?.measurement_granularity) {
    return props.templateData.training_config.measurement_granularity
  }
  
  if (props.templateData?.config?.measurement_granularity) {
    return props.templateData.config.measurement_granularity
  }
  
  return 'set'
})

// 预览表格数据
const previewTableData = computed(() => {
  // 优先使用templateData中的结构化数据
  if (props.templateData?.training_config?.generated_structure) {
    const structure = props.templateData.training_config.generated_structure
    return structure.row_definitions.map(row => ({
      id: row.id,
      sequence: row.id,
      display_label: row.display_label,
      chinese_label: row.chinese_label,
      set: row.set,
      rep: row.rep
    }))
  }
  
  // 回退到原有的生成逻辑
  const data = []
  const granularity = measurementGranularity.value
  const sets = targetSets.value
  const reps = targetReps.value
  
  // 根据颗粒度生成行数
  let rowCount = 1
  if (granularity === 'overall') {
    rowCount = 1
  } else if (granularity === 'set') {
    rowCount = sets
  } else { // 'rep'
    rowCount = sets * reps
  }
  
  for (let i = 1; i <= rowCount; i++) {
    let sequence = ''
    let display_label = ''
    if (granularity === 'overall') {
      sequence = 'overall'
      display_label = '总计'
    } else if (granularity === 'set') {
      sequence = `s${i}`
      display_label = `S${i}`
    } else { // 'rep'
      const setNum = Math.ceil(i / reps)
      const repNum = ((i - 1) % reps) + 1
      sequence = `s${setNum}r${repNum}`
      display_label = `S${setNum}R${repNum}`
    }
    
    data.push({
      sequence,
      display_label,
      id: i
    })
  }
  
  return data
})

// 进度计算
const progressPercentage = computed(() => {
  let progress = 0
  const totalMetrics = props.measurementMetrics.length + props.variationMetrics.length + props.environmentMetrics.length
  
  if (totalMetrics > 0) progress += 25
  if (props.measurementMetrics.length > 0) progress += 25
  if (targetSets.value > 0 && targetReps.value > 0) progress += 25
  if (previewTableData.value.length > 0) progress += 25
  
  return Math.min(progress, 100)
})

// 总指标数
const totalMetricsCount = computed(() => {
  return props.measurementMetrics.length + props.variationMetrics.length + props.environmentMetrics.length
})

// 预计训练时长
const estimatedDuration = computed(() => {
  return targetSets.value * targetReps.value * 3
})

// 获取环境指标值
const getEnvironmentValue = (metricId) => {
  return props.environmentValues[metricId] || ''
}

// 智能生成指标显示名称，避免重复前缀
const getMetricDisplayName = (metric, type) => {
  const name = metric.name || metric.metric_name || metric.display_name || ''
  const prefix = type === 'target' ? '目标' : '实际'
  
  // 如果指标名称已经包含了前缀，就不再添加
  if (name.startsWith(prefix)) {
    return name
  }
  
  // 特殊处理：如果是实际值列，但指标名称包含"目标"，则替换而非添加前缀
  if (type === 'actual' && name.includes('目标')) {
    // 将"目标"替换为"实际"，例如："目标次间歇" -> "实际次间歇"
    return name.replace('目标', '实际')
  }
  
  // 特殊处理：如果是目标值列，但指标名称包含"实际"，则替换而非添加前缀
  if (type === 'target' && name.includes('实际')) {
    // 将"实际"替换为"目标"，例如："实际速度" -> "目标速度"
    return name.replace('实际', '目标')
  }
  
  // 如果指标名称没有包含任何前缀，才添加前缀
  if (!name.includes('目标') && !name.includes('实际')) {
    return `${prefix}${name}`
  }
  
  // 默认返回原名称
  return name
}

// 智能生成输入框placeholder，避免重复词汇
const getMetricPlaceholder = (metric, type) => {
  const name = metric.name || metric.metric_name || ''
  const suffix = type === 'target' ? '目标' : '实际'
  
  // 如果指标名称已经包含了后缀，就不再添加
  if (name.endsWith(suffix)) {
    return name
  }
  
  return `${name}${suffix}`
}

// 🆕 按指标ID排序生成表格列：排除结构性参数，统一按ID排序所有指标
const filteredTableColumns = computed(() => {
  const columns = []
  
  // 结构性参数列表（这些不应出现在表格列中）
  const structuralParams = ['组数', '次数', '目标组数', '实际组数', '目标次数', '实际次数']
  const basicInfoMetrics = ['训练项目', '训练日期', '姓名', '运动员姓名']
  
  // 🔍 获取所有指标的完整列表（从多个可能的数据源）
  let allAvailableMetrics = []
  
  // 1. 从props的指标数组获取
  if (props.measurementMetrics?.length > 0) {
    console.log('📏 measurementMetrics样本:', JSON.stringify(props.measurementMetrics[0]))
    allAvailableMetrics.push(...props.measurementMetrics.map(m => ({ ...m, inferredRole: 'measurement' })))
  }
  if (props.variationMetrics?.length > 0) {
    console.log('🔄 variationMetrics样本:', JSON.stringify(props.variationMetrics[0]))
    allAvailableMetrics.push(...props.variationMetrics.map(m => ({ ...m, inferredRole: 'variation' })))
  }
  if (props.environmentMetrics?.length > 0) {
    console.log('🌍 environmentMetrics样本:', JSON.stringify(props.environmentMetrics[0]))
    allAvailableMetrics.push(...props.environmentMetrics.map(m => ({ ...m, inferredRole: 'environment' })))
  }
  
  // 2. 如果props没有指标，尝试从templateData获取
  if (allAvailableMetrics.length === 0 && props.templateData?.metrics?.length > 0) {
    allAvailableMetrics = props.templateData.metrics.map(metric => {
      // 基于指标名称和角色推断类型
      const name = (metric.name || metric.metric_name || '').toLowerCase()
      const role = metric.metric_role || metric.role || 'normal'
      
      let inferredRole = 'measurement' // 默认为度量指标
      if (name.includes('目标') || role === 'target') {
        inferredRole = 'variation'
      } else if (role === 'actual' && (name.includes('实际') || name.includes('时间') || name.includes('速度'))) {
        inferredRole = 'measurement'
      }
      
      return { ...metric, inferredRole }
    })
  }
  
  // 3. 过滤掉结构性参数和基础信息
  let filteredMetrics = allAvailableMetrics.filter(metric => {
    const metricName = metric.name || metric.metric_name || ''
    return !structuralParams.includes(metricName) && !basicInfoMetrics.includes(metricName)
  })
  
  // 🆕 4. 根据时间指标开关过滤时间相关指标
  if (!showTimeMetrics.value) {
    const beforeTimeFilter = filteredMetrics.length
    filteredMetrics = filteredMetrics.filter(metric => !isTimeMetric(metric))
    const afterTimeFilter = filteredMetrics.length
    console.log(`⏰ 时间指标过滤: ${beforeTimeFilter} -> ${afterTimeFilter} (隐藏了 ${beforeTimeFilter - afterTimeFilter} 个时间指标)`)
  }
  
  console.log('🔧 ExerciseTemplatePreview - filteredTableColumns:', {
    totalMetrics: allAvailableMetrics.length,
    filteredMetrics: filteredMetrics.length,
    measurementFromProps: props.measurementMetrics?.length || 0,
    variationFromProps: props.variationMetrics?.length || 0,
    metricsFromTemplateData: props.templateData?.metrics?.length || 0
  })
  
  // 🆕 智能生成列 - 按指标ID和角色统一排序
  const columnDefinitions = []
  
  // 按指标ID分组（注意：数据中可能是 metric_id 或 id）
  const metricsById = new Map()
  filteredMetrics.forEach(metric => {
    const id = metric.metric_id || metric.id  // 兼容两种字段名
    if (!id) {
      console.warn('⚠️ 指标缺少ID:', metric)
      return
    }
    if (!metricsById.has(id)) {
      metricsById.set(id, [])
    }
    metricsById.get(id).push(metric)
  })
  
  // 按ID升序处理每个指标组
  const sortedIds = Array.from(metricsById.keys()).sort((a, b) => a - b)
  
  sortedIds.forEach(metricId => {
    const metricGroup = metricsById.get(metricId)
    const baseMetric = metricGroup[0] // 取第一个作为基础信息
    
    // 根据指标角色决定生成哪些列
    const role = baseMetric.metric_role || baseMetric.role || baseMetric.inferredRole || 'measurement'
    
    if (role === 'variation' || role === 'target') {
      // 变化指标：生成目标值和实际值列
      const actualId = baseMetric.metric_id || baseMetric.id || metricId
      columnDefinitions.push({
        ...baseMetric,
        id: actualId,  // 确保有ID字段
        columnType: 'target',
        sortKey: metricId * 100 + 1 // ID*100+1 确保目标列在前
      })
      columnDefinitions.push({
        ...baseMetric,
        id: actualId,  // 确保有ID字段
        columnType: 'actual', 
        sortKey: metricId * 100 + 2 // ID*100+2 确保实际列在后
      })
    } else {
      // 度量指标：只生成实际值列
      const actualId = baseMetric.metric_id || baseMetric.id || metricId
      columnDefinitions.push({
        ...baseMetric,
        id: actualId,  // 确保有ID字段
        columnType: 'actual',
        sortKey: metricId * 100 + 2 // 统一使用+2保持一致性
      })
    }
  })
  
  // 🎯 最终按sortKey排序并生成表格列
  columnDefinitions.sort((a, b) => a.sortKey - b.sortKey)
  
  columnDefinitions.forEach(metricDef => {
    const metricId = metricDef.id || metricDef.metric_id  // 兼容两种字段名
    columns.push({
      key: `${metricDef.columnType}-${metricId}`,
      label: getMetricDisplayName(metricDef, metricDef.columnType),
      placeholder: getMetricPlaceholder(metricDef, metricDef.columnType),
      readonly: metricDef.columnType === 'actual', // 实际值列只读
      type: metricDef.columnType,
      metricId: metricId, // 🆕 保存指标ID
      sortOrder: metricId, // 🆕 保存排序依据
      originalMetric: metricDef // 🆕 保存完整指标信息用于调试
    })
  })
  
  console.log('📊 Generated table columns:', columns.length, 'columns, first 5 IDs:', 
    columns.slice(0, 5).map(c => `${c.metricId}(${c.type})`).join(', '))
  
  return columns
})

// 监听进度变化，向父组件发送更新
watch(progressPercentage, (newProgress) => {
  emit('update-progress', newProgress)
}, { immediate: true })
</script>

<style scoped>
.exercise-template-preview {
  width: 100%;
}

.preview-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e4e7ed;
}

.preview-content h3 {
  color: #303133 !important;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #FF6B35;
}

.training-params-section {
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.param-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  min-width: 70px;
}

.param-value {
  flex: 1;
  font-size: 13px;
  color: #303133;
  font-weight: 600;
}

.actual-placeholder {
  color: #c0c4cc;
  font-style: italic;
}

.param-unit {
  font-size: 12px;
  color: #909399;
}

.preview-table-container {
  margin-top: 12px;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.preview-table th,
.preview-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #e4e7ed;
  color: #303133;
}

.preview-table th {
  background: #FF6B35;
  color: white;
  font-weight: 600;
  font-size: 13px;
}

.preview-input {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 8px;
  color: #303133;
  font-size: 12px;
  width: 100%;
  text-align: center;
  transition: all 0.2s ease;
}

.preview-input::placeholder {
  color: #c0c4cc;
}

.preview-input:focus {
  border-color: #FF6B35;
  outline: none;
}

.readonly-input {
  background: #f5f7fa !important;
  cursor: not-allowed;
  color: #909399 !important;
}

.readonly-input::placeholder {
  color: #c0c4cc !important;
}

.training-stats {
  margin-top: 24px;
  padding: 16px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 53, 0.2);
}

.stat-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  color: #333333;
  font-size: 14px;
  font-weight: 500;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin: 0 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6B35, #FF8B5A);
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-panel {
    padding: 16px;
  }
  
  .params-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .param-item {
    padding: 6px 10px;
  }
  
  .preview-table th,
  .preview-table td {
    padding: 8px;
    font-size: 12px;
  }
  
  .preview-input {
    padding: 4px 6px;
    font-size: 11px;
  }
}
</style>