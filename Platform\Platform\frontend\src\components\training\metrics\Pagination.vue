<template>
  <div class="modern-pagination">
    <div class="pagination-left">
      <span class="total-records">共 {{ totalItems }} 条记录</span>
    </div>
    
    <div class="pagination-center">
      <button 
        class="page-btn" 
        @click="goToPage(1)"
        :disabled="currentPage === 1"
        title="首页"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"/>
        </svg>
      </button>
      
      <button 
        class="page-btn" 
        @click="goToPage(Math.max(1, currentPage - 1))"
        :disabled="currentPage <= 1"
        title="上一页"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
        </svg>
      </button>
      
      <div class="page-numbers">
        <template v-for="page in visiblePages" :key="page">
          <button
            v-if="typeof page === 'number'"
            class="page-btn"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          <span v-else class="page-ellipsis">...</span>
        </template>
      </div>
      
      <button 
        class="page-btn" 
        @click="goToPage(Math.min(totalPages, currentPage + 1))"
        :disabled="currentPage >= totalPages"
        title="下一页"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
        </svg>
      </button>
      
      <button 
        class="page-btn" 
        @click="goToPage(totalPages)"
        :disabled="currentPage === totalPages"
        title="末页"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6v12h2V6h-2z"/>
        </svg>
      </button>
    </div>
    
    <div class="pagination-right">
      <span class="page-size-text">每页显示</span>
      <select 
        :value="pageSize" 
        @change="handleSizeChange(Number($event.target.value))"
        class="page-size-select"
      >
        <option v-for="size in pageSizes" :key="size" :value="size">
          {{ size }}
        </option>
      </select>
      <span class="page-size-text">条</span>
    </div>
  </div>
</template>

<script setup>
import { toRefs, computed } from 'vue'

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    required: true
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  }
})

const emit = defineEmits([
  'update:currentPage',
  'update:pageSize',
  'sizeChange',
  'currentChange'
])

const { currentPage, pageSize, totalItems, pageSizes } = toRefs(props)

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value)
})

// 计算可见页码
const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  if (total <= 7) {
    // 总页数少，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数多，显示部分页码
    if (current <= 4) {
      // 当前页在前面
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // 当前页在后面
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages
})

const handleSizeChange = (newSize) => {
  emit('update:pageSize', newSize)
  emit('sizeChange', newSize)
}

const handleCurrentChange = (newPage) => {
  emit('update:currentPage', newPage)
  emit('currentChange', newPage)
}

const goToPage = (page) => {
  if (page !== currentPage.value && page >= 1 && page <= totalPages.value) {
    handleCurrentChange(page)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';
</style>