<template>
  <svg
    :width="'100%'"
    :height="'100%'"
    :viewBox="anatomyData.viewBox"
    xmlns="http://www.w3.org/2000/svg"
    xml:space="preserve"
    :style="{ backgroundColor }"
  >
    <!-- Body Outline -->
    <g id="body-outline" :stroke="outlineColor" :stroke-width="outlineStrokeWidth" fill="none">
      <path
        v-for="(path, index) in anatomyData.bodyOutlinePaths"
        :key="`outline-${index}`"
        :d="path"
      />
    </g>
    
    <!-- Hair Features (for gender differentiation) -->
    <g v-if="anatomyData.hairPaths && anatomyData.hairPaths.length > 0" id="hair-features">
      <path
        v-for="(path, index) in anatomyData.hairPaths"
        :key="`hair-${index}`"
        :d="path"
        :fill="hairColor"
        :stroke="hairStrokeColor"
        :stroke-width="hairStrokeWidth"
        opacity="0.9"
      />
    </g>

    <!-- Muscle Groups -->
    <g 
      v-for="(muscleGroup, groupId) in visibleMuscleGroups"
      :key="groupId"
      :id="muscleGroup.id"
      :transform="muscleGroup.transform"
    >
      <path
        v-for="(path, pathIndex) in muscleGroup.paths"
        :key="`${groupId}-${pathIndex}`"
        :d="path"
        :style="getMuscleGroupStyle(groupId)"
      />
    </g>
  </svg>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { maleAnatomyData } from './male-anatomy';
import { femaleAnatomyData } from './female-anatomy';
import type { AnatomyProps } from './types';

// Props with defaults
const props = withDefaults(defineProps<AnatomyProps>(), {
  gender: 'MALE',
  view: 'front',
  defaultMuscleColor: '#1f1f1f',
  backgroundColor: '#000000',
  primaryHighlightColor: '#ff0000',
  secondaryHighlightColor: '#ff0000',
  primaryOpacity: 0.5,
  secondaryOpacity: 0.2,
  selectedPrimaryMuscleGroups: () => [],
  selectedSecondaryMuscleGroups: () => []
});

// Computed properties
const anatomyData = computed(() => {
  const data = props.gender === 'FEMALE' ? femaleAnatomyData : maleAnatomyData;
  console.log('🧬 HumanMuscleAnatomy - anatomyData loaded:', {
    gender: props.gender,
    view: props.view,
    viewBox: data.viewBox,
    hairPathsCount: data.hairPaths?.length || 0,
    bodyOutlinePathsCount: data.bodyOutlinePaths?.length || 0,
    muscleGroupsCount: Object.keys(data.muscleGroups).length
  });
  return data;
});

const visibleMuscleGroups = computed(() => {
  const allMuscleGroups = anatomyData.value.muscleGroups;
  
  // Define which muscle groups are visible for each view
  const frontMuscles = [
    'chest', 'abs', 'obliques', 'frontDelts', 'biceps', 'triceps', 
    'forearms', 'quadriceps', 'calves', 'glutes', 'neck', 'adductors'
  ];
  const backMuscles = [
    'traps', 'lats', 'rearDelts', 'lowerBack', 'hamstrings', 
    'glutes', 'triceps', 'calves', 'neck', 'upperBack'
  ];
  
  const visibleMuscles = props.view === 'back' ? backMuscles : frontMuscles;
  
  console.log('💪 HumanMuscleAnatomy - visibleMuscleGroups:', {
    view: props.view,
    visibleMuscles,
    allMuscleGroupKeys: Object.keys(allMuscleGroups),
    filteredCount: Object.entries(allMuscleGroups).filter(([groupId]) => visibleMuscles.includes(groupId)).length
  });
  
  // Filter muscle groups based on current view
  return Object.fromEntries(
    Object.entries(allMuscleGroups).filter(([groupId]) => 
      visibleMuscles.includes(groupId)
    )
  );
});

const outlineColor = computed(() => {
  // Make outline visible against background
  return props.backgroundColor === '#000000' ? '#666666' : '#333333';
});

const outlineStrokeWidth = computed(() => 2);

const hairColor = computed(() => {
  // Natural hair color based on gender
  return props.gender === 'FEMALE' ? '#8B4513' : '#654321'; // Female: lighter brown, Male: darker brown
});

const hairStrokeColor = computed(() => {
  return props.gender === 'FEMALE' ? '#5D2F0C' : '#3E2723'; // Darker stroke for definition
});

const hairStrokeWidth = computed(() => 1);

// Methods
const getMuscleGroupStyle = (groupId: string) => {
  const muscleGroup = visibleMuscleGroups.value[groupId];
  const opacity = muscleGroup?.opacity || 1;

  // Check for primary muscle group highlighting
  if (props.selectedPrimaryMuscleGroups?.includes(groupId)) {
    return {
      fill: props.primaryHighlightColor,
      opacity: props.primaryOpacity
    };
  }
  
  // Check for secondary muscle group highlighting
  if (props.selectedSecondaryMuscleGroups?.includes(groupId)) {
    return {
      fill: props.secondaryHighlightColor,
      opacity: props.secondaryOpacity
    };
  }
  
  // Default muscle color
  return {
    fill: props.defaultMuscleColor,
    opacity
  };
};

// Debug component initialization
onMounted(() => {
  console.log('🚀 HumanMuscleAnatomy mounted with props:', {
    gender: props.gender,
    view: props.view,
    selectedPrimaryMuscleGroups: props.selectedPrimaryMuscleGroups,
    selectedSecondaryMuscleGroups: props.selectedSecondaryMuscleGroups,
    backgroundColor: props.backgroundColor
  });
});
</script>