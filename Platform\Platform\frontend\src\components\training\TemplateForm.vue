<!--
Template Form Component - 模板表单组件
用于创建和编辑训练模板
-->

<template>
  <div class="template-form" data-testid="template-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入模板名称"
              data-testid="template-name-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select
              v-model="formData.category"
              placeholder="选择分类"
              style="width: 100%"
              data-testid="template-category-select"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
          data-testid="template-description-input"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="可见性" prop="visibility">
            <el-radio-group v-model="formData.visibility" data-testid="template-visibility-radio">
              <el-radio value="private">私有</el-radio>
              <el-radio value="public">公开</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="版本" prop="version">
            <el-input
              v-model="formData.version"
              placeholder="例如: 1.0.0"
              data-testid="template-version-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <div class="form-footer">
      <el-button @click="handleCancel" data-testid="cancel-btn">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSave"
        :loading="saving"
        data-testid="save-btn"
      >
        {{ isEditing ? '更新' : '创建' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  createExerciseTemplate, 
  updateExerciseTemplate,
  getTemplateCategories,
  type ExerciseTemplate 
} from '@/services/templateService'

// Props
interface Props {
  template?: ExerciseTemplate | null
  isEditing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
  isEditing: false
})

// Emits
const emit = defineEmits<{
  save: []
  cancel: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const categories = ref<string[]>([
  '力量训练',
  '有氧训练', 
  '柔韧性训练',
  '爆发力训练',
  '技能训练',
  '康复训练'
])

const formData = reactive({
  name: '',
  description: '',
  category: '',
  visibility: 'private' as 'private' | 'public',
  version: '1.0.0'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 x.y.z', trigger: 'blur' }
  ]
}

// 方法
const initFormData = () => {
  if (props.template) {
    formData.name = props.template.name
    formData.description = props.template.description || ''
    formData.category = props.template.category
    formData.visibility = props.template.visibility
    formData.version = props.template.version
  } else {
    // 重置表单为默认值
    formData.name = ''
    formData.description = ''
    formData.category = ''
    formData.visibility = 'private'
    formData.version = '1.0.0'
  }
}

const loadCategories = async () => {
  try {
    const loadedCategories = await getTemplateCategories()
    if (loadedCategories.length > 0) {
      categories.value = loadedCategories
    }
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    if (props.isEditing && props.template) {
      // 更新模板
      await updateExerciseTemplate(props.template.id, formData)
    } else {
      // 创建新模板
      await createExerciseTemplate(formData)
    }
    
    emit('save')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('Save template error:', error)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(() => {
  initFormData()
  loadCategories()
})
</script>

<style scoped>
.template-form {
  padding: 20px 0;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}
</style>