<template>
  <div class="intensity-level-config">
    <div class="config-header">
      <div class="config-info">
        <el-icon><TrendCharts /></el-icon>
        <span class="config-title">强度等级配置</span>
      </div>
      
      <div class="config-actions">
        <el-button size="small" @click="addLevel">
          <el-icon><Plus /></el-icon>
          添加等级
        </el-button>
        <el-button size="small" @click="resetToDefaults" v-if="hasLevels">
          重置为默认
        </el-button>
      </div>
    </div>

    <div class="levels-container" v-if="hasLevels">
      <div
        v-for="(config, levelKey) in intensityLevels"
        :key="levelKey"
        class="level-item"
        :class="{ 'level-editing': editingLevel === levelKey }"
      >
        <div class="level-header">
          <div class="level-info">
            <div class="level-label">{{ getLevelDisplayName(levelKey) }}</div>
            <div class="level-badge" :style="{ background: getLevelColor(levelKey) }">
              {{ levelKey }}
            </div>
          </div>
          
          <div class="level-actions">
            <el-button 
              size="small" 
              type="text" 
              @click="toggleEdit(levelKey)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="text" 
              @click="removeLevel(levelKey)"
              v-if="!isDefaultLevel(levelKey)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="level-content">
          <template v-if="editingLevel === levelKey">
            <!-- 编辑模式 -->
            <div class="config-form">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="建议组数" size="small">
                    <el-input-number
                      v-model="editingConfig.sets"
                      :min="1"
                      :max="20"
                      size="small"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="建议次数" size="small">
                    <el-input
                      v-model="editingConfig.reps"
                      placeholder="如: 8-12"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="休息时间(秒)" size="small">
                    <el-input-number
                      v-model="editingConfig.rest_time"
                      :min="30"
                      :max="600"
                      :step="15"
                      size="small"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="强度描述" size="small">
                    <el-input
                      v-model="editingConfig.description"
                      placeholder="描述该强度等级的特点"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="适用人群" size="small">
                    <el-input
                      v-model="editingConfig.target_audience"
                      placeholder="如: 初学者、有基础者"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <div class="form-actions">
                <el-button size="small" @click="saveLevel">保存</el-button>
                <el-button size="small" @click="cancelEdit">取消</el-button>
              </div>
            </div>
          </template>

          <template v-else>
            <!-- 显示模式 -->
            <div class="level-display">
              <div class="display-grid">
                <div class="display-item">
                  <span class="display-label">组数:</span>
                  <span class="display-value">{{ config.sets || '-' }}</span>
                </div>
                <div class="display-item">
                  <span class="display-label">次数:</span>
                  <span class="display-value">{{ config.reps || '-' }}</span>
                </div>
                <div class="display-item">
                  <span class="display-label">休息:</span>
                  <span class="display-value">{{ formatRestTime(config.rest_time) }}</span>
                </div>
              </div>
              
              <div class="level-description" v-if="config.description">
                <p>{{ config.description }}</p>
              </div>
              
              <div class="target-audience" v-if="config.target_audience">
                <el-tag size="small" type="info">{{ config.target_audience }}</el-tag>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="empty-state" v-else>
      <el-icon class="empty-icon"><TrendCharts /></el-icon>
      <p>暂无强度等级配置</p>
      <el-button type="primary" @click="loadDefaults">加载默认配置</el-button>
    </div>

    <!-- 添加新等级对话框 -->
    <el-dialog
      v-model="addLevelDialogVisible"
      title="添加强度等级"
      width="480px"
      :before-close="handleAddDialogClose"
    >
      <el-form
        ref="addLevelFormRef"
        :model="newLevelForm"
        :rules="newLevelRules"
        label-width="100px"
      >
        <el-form-item label="等级标识" prop="key">
          <el-input
            v-model="newLevelForm.key"
            placeholder="如: intermediate"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="显示名称" prop="displayName">
          <el-input
            v-model="newLevelForm.displayName"
            placeholder="如: 中级"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="addLevelDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddLevel">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, Plus, Edit, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  placeholder: {
    type: String,
    default: '配置强度等级参数'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const intensityLevels = ref({ ...props.modelValue })
const editingLevel = ref(null)
const editingConfig = ref({})
const addLevelDialogVisible = ref(false)
const addLevelFormRef = ref()

// 添加等级表单
const newLevelForm = ref({
  key: '',
  displayName: ''
})

const newLevelRules = {
  key: [
    { required: true, message: '请输入等级标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '只能包含字母、数字和下划线，以字母或下划线开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 默认强度等级配置
const defaultLevels = {
  beginner: {
    sets: 3,
    reps: '8-12',
    rest_time: 90,
    description: '初学者适用，注重动作标准',
    target_audience: '初学者'
  },
  intermediate: {
    sets: 4,
    reps: '10-15',
    rest_time: 75,
    description: '中级训练者，增加训练量',
    target_audience: '有基础者'
  },
  advanced: {
    sets: 5,
    reps: '12-20',
    rest_time: 60,
    description: '高级训练者，高强度训练',
    target_audience: '高级训练者'
  }
}

// 计算属性
const hasLevels = computed(() => {
  return Object.keys(intensityLevels.value).length > 0
})

// 方法
const getLevelDisplayName = (levelKey) => {
  const displayNames = {
    beginner: '初级',
    intermediate: '中级',
    advanced: '高级',
    elite: '精英级',
    expert: '专家级'
  }
  return displayNames[levelKey] || levelKey.charAt(0).toUpperCase() + levelKey.slice(1)
}

const getLevelColor = (levelKey) => {
  const colors = {
    beginner: '#67C23A',
    intermediate: '#E6A23C',
    advanced: '#F56C6C',
    elite: '#909399',
    expert: '#409EFF'
  }
  return colors[levelKey] || '#909399'
}

const isDefaultLevel = (levelKey) => {
  return ['beginner', 'intermediate', 'advanced'].includes(levelKey)
}

const formatRestTime = (seconds) => {
  if (!seconds) return '-'
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    if (remainingSeconds === 0) {
      return `${minutes}分钟`
    }
    return `${minutes}分${remainingSeconds}秒`
  }
  return `${seconds}秒`
}

const toggleEdit = (levelKey) => {
  if (editingLevel.value === levelKey) {
    cancelEdit()
  } else {
    editingLevel.value = levelKey
    editingConfig.value = { ...intensityLevels.value[levelKey] }
  }
}

const saveLevel = () => {
  if (!editingLevel.value) return
  
  intensityLevels.value[editingLevel.value] = { ...editingConfig.value }
  editingLevel.value = null
  editingConfig.value = {}
  
  ElMessage.success('强度等级配置已保存')
}

const cancelEdit = () => {
  editingLevel.value = null
  editingConfig.value = {}
}

const removeLevel = (levelKey) => {
  if (isDefaultLevel(levelKey)) {
    ElMessage.warning('默认等级无法删除')
    return
  }
  
  delete intensityLevels.value[levelKey]
  ElMessage.success('强度等级已删除')
}

const addLevel = () => {
  newLevelForm.value = { key: '', displayName: '' }
  addLevelDialogVisible.value = true
}

const confirmAddLevel = async () => {
  if (!addLevelFormRef.value) return
  
  try {
    const valid = await addLevelFormRef.value.validate()
    if (!valid) return
    
    const { key } = newLevelForm.value
    
    // 检查是否已存在
    if (intensityLevels.value[key]) {
      ElMessage.warning('该等级标识已存在')
      return
    }
    
    // 添加新等级
    intensityLevels.value[key] = {
      sets: 3,
      reps: '8-12',
      rest_time: 90,
      description: '',
      target_audience: ''
    }
    
    addLevelDialogVisible.value = false
    ElMessage.success('新强度等级已添加')
    
    // 立即进入编辑模式
    nextTick(() => {
      toggleEdit(key)
    })
  } catch (error) {
    console.error('添加等级失败:', error)
  }
}

const handleAddDialogClose = () => {
  addLevelDialogVisible.value = false
}

const loadDefaults = () => {
  intensityLevels.value = { ...defaultLevels }
  ElMessage.success('已加载默认强度等级配置')
}

const resetToDefaults = () => {
  intensityLevels.value = { ...defaultLevels }
  editingLevel.value = null
  editingConfig.value = {}
  ElMessage.success('已重置为默认配置')
}

// 监听器 - 避免递归更新
watch(() => props.modelValue, (newVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(intensityLevels.value)) {
    intensityLevels.value = { ...newVal }
  }
}, { immediate: true })

watch(intensityLevels, (newVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(props.modelValue)) {
    emit('update:modelValue', { ...newVal })
  }
}, { deep: true })
</script>

<style scoped>
.intensity-level-config {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.config-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.config-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-weight: 600;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.levels-container {
  padding: 20px;
}

.level-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.level-item:last-child {
  margin-bottom: 0;
}

.level-item.level-editing {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.level-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.level-label {
  font-weight: 600;
  color: #2d3748;
}

.level-badge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.level-actions {
  display: flex;
  gap: 4px;
}

.level-content {
  padding: 16px;
}

.config-form {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
}

.form-actions {
  margin-top: 16px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.level-display {
  /* 显示模式样式 */
}

.display-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 12px;
}

.display-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.display-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.display-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 600;
}

.level-description {
  margin: 12px 0;
  padding: 8px 12px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  border-radius: 6px;
  color: #409eff;
}

.level-description p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.target-audience {
  margin-top: 8px;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state p {
  margin: 0 0 20px 0;
  font-size: 16px;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

/* 输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .config-actions {
    justify-content: center;
  }
  
  .display-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .level-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .level-actions {
    justify-content: center;
  }
}
</style>