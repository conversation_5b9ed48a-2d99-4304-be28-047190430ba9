import request from '@/utils/request'

// ============ 动作相关 API ============

/**
 * 获取动作列表
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 跳过记录数
 * @param {number} params.limit - 限制记录数
 * @param {string} params.name - 动作名称搜索
 * @param {string} params.category - 动作分类
 * @param {string} params.difficulty_level - 难度等级
 * @param {Array} params.tag_ids - 标签ID数组
 * @param {boolean} params.supports_sets - 是否支持组数
 * @param {boolean} params.supports_reps - 是否支持次数
 * @param {boolean} params.supports_weight - 是否支持重量
 * @param {boolean} params.supports_distance - 是否支持距离
 * @param {boolean} params.supports_time - 是否支持时间
 * @param {boolean} params.is_active - 是否活跃
 */
export function getExercises(params) {
  return request({
    url: '/api/v1/exercises/',
    method: 'get',
    params
  })
}

/**
 * 获取单个动作详情
 * @param {number} id - 动作ID
 */
export function getExercise(id) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'get'
  })
}

/**
 * 创建新动作
 * @param {Object} data - 动作数据
 */
export function createExercise(data) {
  return request({
    url: '/api/v1/exercises/',
    method: 'post',
    data
  })
}

/**
 * 更新动作
 * @param {number} id - 动作ID
 * @param {Object} data - 更新数据
 */
export function updateExercise(id, data) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除动作
 * @param {number} id - 动作ID
 */
export function deleteExercise(id) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'delete'
  })
}

/**
 * 获取动作统计信息
 */
export function getExerciseStats() {
  return request({
    url: '/api/v1/exercises/stats/overview',
    method: 'get'
  })
}

// ============ 标签相关 API ============

/**
 * 获取标签列表
 * @param {Object} params - 查询参数
 * @param {number} params.category_id - 标签分类ID
 * @param {string} params.name - 标签名称搜索
 */
export function getTags(params) {
  return request({
    url: '/api/v1/exercises/tags/',
    method: 'get',
    params
  })
}

/**
 * 创建新标签
 * @param {Object} data - 标签数据
 */
export function createTag(data) {
  return request({
    url: '/api/v1/exercises/tags/',
    method: 'post',
    data
  })
}

/**
 * 获取标签分类列表
 */
export function getTagCategories() {
  return request({
    url: '/api/v1/exercises/tag-categories/',
    method: 'get'
  })
}

// ============ 指标相关 API ============

/**
 * 获取指标列表
 * @param {Object} params - 查询参数
 * @param {number} params.category_id - 指标分类ID
 */
export function getMetrics(params) {
  return request({
    url: '/api/v1/exercises/metrics/',
    method: 'get',
    params
  })
}

/**
 * 创建新指标
 * @param {Object} data - 指标数据
 */
export function createMetric(data) {
  return request({
    url: '/api/v1/exercises/metrics/',
    method: 'post',
    data
  })
}

/**
 * 更新指标
 * @param {number} id - 指标ID
 * @param {Object} data - 更新数据
 */
export function updateMetric(id, data) {
  return request({
    url: `/api/v1/exercises/metrics/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除指标
 * @param {number} id - 指标ID
 */
export function deleteMetric(id) {
  return request({
    url: `/api/v1/exercises/metrics/${id}`,
    method: 'delete'
  })
}

/**
 * 获取指标分类列表
 */
export function getMetricCategories() {
  return request({
    url: '/api/v1/exercises/metric-categories/',
    method: 'get'
  })
}

// ============ 动作模板相关 API ============

/**
 * 获取动作的模板列表
 * @param {number} exerciseId - 动作ID
 */
export function getExerciseTemplates(exerciseId) {
  return request({
    url: `/api/v1/exercises/${exerciseId}/templates`,
    method: 'get'
  })
}

/**
 * 创建动作模板
 * @param {Object} data - 模板数据
 */
export function createExerciseTemplate(data) {
  return request({
    url: '/api/v1/exercises/templates/',
    method: 'post',
    data
  })
}

/**
 * 获取模板详情
 * @param {number} templateId - 模板ID
 */
export function getExerciseTemplate(templateId) {
  return request({
    url: `/api/v1/exercises/templates/${templateId}`,
    method: 'get'
  })
}

/**
 * 更新模板
 * @param {number} templateId - 模板ID
 * @param {Object} data - 更新数据
 */
export function updateExerciseTemplate(templateId, data) {
  return request({
    url: `/api/v1/exercises/templates/${templateId}`,
    method: 'put',
    data
  })
}

/**
 * 删除模板
 * @param {number} templateId - 模板ID
 */
export function deleteExerciseTemplate(templateId) {
  return request({
    url: `/api/v1/exercises/templates/${templateId}`,
    method: 'delete'
  })
}

// ============ 批量操作 API ============

/**
 * 批量删除动作
 * @param {Array} ids - 动作ID数组
 */
export function batchDeleteExercises(ids) {
  return request({
    url: '/api/v1/exercises/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 批量更新动作标签
 * @param {Array} exercises - 动作更新数据数组
 */
export function batchUpdateExerciseTags(exercises) {
  return request({
    url: '/api/v1/exercises/batch/tags',
    method: 'put',
    data: { exercises }
  })
}

/**
 * 导入动作数据
 * @param {FormData} formData - 包含文件的表单数据
 */
export function importExercises(formData) {
  return request({
    url: '/api/v1/exercises/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出动作数据
 * @param {Object} params - 导出参数
 * @param {string} params.format - 导出格式 (excel, csv, json)
 * @param {Array} params.exercise_ids - 要导出的动作ID数组（可选）
 */
export function exportExercises(params) {
  return request({
    url: '/api/v1/exercises/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ============ 搜索和推荐 API ============

/**
 * 智能搜索动作
 * @param {string} query - 搜索关键词
 * @param {Object} filters - 额外筛选条件
 */
export function searchExercises(query, filters = {}) {
  return request({
    url: '/api/v1/exercises/search',
    method: 'get',
    params: {
      q: query,
      ...filters
    }
  })
}

/**
 * 获取推荐动作
 * @param {Object} params - 推荐参数
 * @param {number} params.exercise_id - 基于的动作ID
 * @param {Array} params.tag_ids - 基于的标签ID数组
 * @param {string} params.difficulty_level - 难度等级
 * @param {number} params.limit - 推荐数量限制
 */
export function getRecommendedExercises(params) {
  return request({
    url: '/api/v1/exercises/recommendations',
    method: 'get',
    params
  })
}

// ============ 数据验证和工具 API ============

/**
 * 验证动作名称是否可用
 * @param {string} name - 动作名称
 * @param {number} excludeId - 排除的动作ID（用于编辑时验证）
 */
export function validateExerciseName(name, excludeId = null) {
  return request({
    url: '/api/v1/exercises/validate-name',
    method: 'post',
    data: { name, exclude_id: excludeId }
  })
}

/**
 * 获取动作分类选项
 */
export function getExerciseCategories() {
  return request({
    url: '/api/v1/exercises/categories',
    method: 'get'
  })
}

/**
 * 获取常用标签组合
 */
export function getPopularTagCombinations() {
  return request({
    url: '/api/v1/exercises/tags/popular-combinations',
    method: 'get'
  })
}

/**
 * 分析动作使用趋势
 * @param {Object} params - 分析参数
 * @param {string} params.period - 时间段 (week, month, quarter, year)
 * @param {Array} params.exercise_ids - 动作ID数组（可选）
 */
export function getExerciseUsageTrends(params) {
  return request({
    url: '/api/v1/exercises/analytics/trends',
    method: 'get',
    params
  })
}

export default {
  // 动作管理
  getExercises,
  getExercise,
  createExercise,
  updateExercise,
  deleteExercise,
  getExerciseStats,
  
  // 标签管理
  getTags,
  createTag,
  getTagCategories,
  
  // 指标管理
  getMetrics,
  createMetric,
  updateMetric,
  deleteMetric,
  getMetricCategories,
  
  // 模板管理
  getExerciseTemplates,
  createExerciseTemplate,
  getExerciseTemplate,
  updateExerciseTemplate,
  deleteExerciseTemplate,
  
  // 批量操作
  batchDeleteExercises,
  batchUpdateExerciseTags,
  importExercises,
  exportExercises,
  
  // 搜索推荐
  searchExercises,
  getRecommendedExercises,
  
  // 工具方法
  validateExerciseName,
  getExerciseCategories,
  getPopularTagCombinations,
  getExerciseUsageTrends
}