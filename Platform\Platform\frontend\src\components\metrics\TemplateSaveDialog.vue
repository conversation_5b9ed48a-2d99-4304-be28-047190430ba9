<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="保存训练模板"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="模板名称" prop="template_name">
        <el-input 
          v-model="form.template_name" 
          placeholder="请输入模板名称"
        />
      </el-form-item>
      
      <el-form-item label="模板代码" prop="template_code">
        <el-input 
          v-model="form.template_code" 
          placeholder="系统自动生成或手动输入"
        />
      </el-form-item>
      
      <el-form-item label="模板描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请描述该模板的用途和特点"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否公开">
            <el-switch 
              v-model="form.is_public" 
              active-text="公开模板"
              inactive-text="私有模板"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="专项分类">
            <el-input 
              v-model="form.specialization" 
              placeholder="如：短跑、跳跃、投掷"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 配置预览 -->
      <el-divider content-position="left">配置预览</el-divider>
      
      <TrainingConfigSelector
        v-model:config="previewConfig"
        :options="previewOptions"
        :readonly="true"
      />
      
      <!-- 指标配置详情 -->
      <el-divider content-position="left">指标配置详情</el-divider>
      
      <div class="metrics-detail">
        <el-table :data="metricConfigList" size="small" border>
          <el-table-column prop="metric_name" label="指标名称" width="120" />
          <el-table-column prop="metric_role" label="指标角色" width="100">
            <template #default="{ row }">
              <el-tag :type="getRoleColor(row.metric_role)" size="small">
                {{ getRoleText(row.metric_role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_neutral" label="类型" width="80">
            <template #default="{ row }">
              <el-tag 
                :type="row.is_neutral ? 'warning' : 'success'" 
                size="small"
              >
                {{ row.is_neutral ? '中性' : '标准' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="neutral_level" label="中性标记" width="100">
            <template #default="{ row }">
              <template v-if="row.is_neutral">
                <el-rate 
                  :model-value="row.neutral_level" 
                  :max="3" 
                  disabled 
                  size="small"
                />
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="选中维度">
            <template #default="{ row }">
              <template v-if="row.is_neutral && row.selected_dimensions">
                <el-tag 
                  v-for="dimension in row.selected_dimensions"
                  :key="dimension"
                  size="small"
                  style="margin-right: 4px"
                >
                  {{ getDimensionText(dimension) }}
                </el-tag>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    
    <template #footer>
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        保存模板
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { Metric, Exercise } from '@/types/metrics'
import TrainingConfigSelector from '@/components/common/TrainingConfigSelector.vue'
import type { TrainingConfig, TrainingConfigSelectorOptions } from '@/types/training-config'

interface Props {
  visible: boolean
  templateConfig: any
  selectedMetrics: Metric[]
  metricConfigs: Record<number, any>
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data: any]
}>()

const formRef = ref<FormInstance>()

const form = reactive({
  template_name: '',
  template_code: '',
  description: '',
  is_public: false,
  specialization: ''
})

const rules: FormRules = {
  template_name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  template_code: [
    { required: true, message: '请输入模板代码', trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9_]*$/, message: '代码必须以小写字母开头，只能包含小写字母、数字和下划线', trigger: 'blur' }
  ]
}

// 预览配置数据
const previewConfig = computed<TrainingConfig>(() => ({
  gender: props.templateConfig?.gender || 'ALL',
  level: props.templateConfig?.level || 'SECOND_CLASS',
  specialty: props.templateConfig?.specialization ? [props.templateConfig.specialization] : []
}))

// 预览选项配置
const previewOptions: TrainingConfigSelectorOptions = {
  features: {
    showGender: true,
    showLevel: true,
    showSpecialty: false,
    showCustom: true
  },
  display: {
    layout: 'horizontal',
    compact: true,
    showLabels: false,
    showDescriptions: false
  }
}

// 计算属性
const exerciseName = computed(() => {
  // 这里应该从exercise列表中找到对应的动作名称
  return '深蹲' // 模拟数据
})


const metricConfigList = computed(() => {
  return props.selectedMetrics.map(metric => {
    const config = props.metricConfigs[metric.id] || {}
    return {
      ...metric,
      metric_role: config.metric_role,
      neutral_level: config.neutral_level,
      selected_dimensions: config.selected_dimensions
    }
  })
})

// 方法
const getRoleColor = (role: string) => {
  const colors = {
    environment: 'info',
    measurement: 'primary',
    variable: 'warning'
  }
  return colors[role as keyof typeof colors] || 'info'
}

const getRoleText = (role: string) => {
  const texts = {
    environment: '环境',
    measurement: '度量',
    variable: '变化'
  }
  return texts[role as keyof typeof texts] || role
}

const getDimensionText = (dimension: string) => {
  const texts = {
    target: '目标',
    actual: '实际',
    baseline: '基准'
  }
  return texts[dimension as keyof typeof texts] || dimension
}

const generateTemplateCode = () => {
  if (form.template_name && !form.template_code) {
    // 基于模板名称生成代码
    const code = form.template_name
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
    
    form.template_code = code
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const templateData = {
      ...form,
      ...props.templateConfig,
      template_config: {
        measurement_granularity: props.templateConfig.measurement_granularity,
        metrics: props.selectedMetrics.map(metric => {
          const config = props.metricConfigs[metric.id]
          return {
            metric_id: metric.id,
            metric_code: metric.metric_code,
            metric_name: metric.metric_name,
            ...config
          }
        })
      },
      preview_data: generatePreviewData()
    }
    
    emit('submit', templateData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

const generatePreviewData = () => {
  // 生成预览数据结构
  if (props.templateConfig.measurement_granularity === 'overall') {
    return { type: 'overall', data: {} }
  } else if (props.templateConfig.measurement_granularity === 'set') {
    return {
      type: 'sets',
      data: Array.from({ length: 4 }, (_, i) => ({
        set_number: i + 1,
        ...generateSampleMetricValues()
      }))
    }
  } else {
    return {
      type: 'reps',
      data: Array.from({ length: 8 }, (_, i) => ({
        rep_number: i + 1,
        ...generateSampleMetricValues()
      }))
    }
  }
}

const generateSampleMetricValues = () => {
  const values: any = {}
  
  props.selectedMetrics.forEach(metric => {
    const config = props.metricConfigs[metric.id]
    
    if (metric.is_neutral && config.selected_dimensions) {
      config.selected_dimensions.forEach((dimension: string) => {
        const key = `${dimension}_${metric.metric_code}`
        values[key] = config.target_values?.[dimension] || getSampleValue(metric)
      })
    } else {
      values[metric.metric_code] = config.target_values?.default || getSampleValue(metric)
    }
  })
  
  return values
}

const getSampleValue = (metric: Metric) => {
  if (metric.data_type === 'integer') {
    return Math.floor(Math.random() * 10) + 1
  } else if (metric.data_type === 'decimal') {
    return (Math.random() * 100).toFixed(2)
  } else {
    return `示例${metric.metric_name}`
  }
}

// 监听模板名称变化，自动生成代码
watch(() => form.template_name, generateTemplateCode)

// 监听弹窗打开，初始化表单
watch(() => props.visible, (visible) => {
  if (visible) {
    // 从templateConfig初始化表单
    Object.assign(form, {
      template_name: props.templateConfig.template_name || '',
      template_code: props.templateConfig.template_code || '',
      description: props.templateConfig.description || '',
      is_public: props.templateConfig.is_public || false,
      specialization: props.templateConfig.specialization || ''
    })
  }
})
</script>

<style scoped lang="scss">
.config-preview {
  margin-bottom: 20px;
}

.metrics-detail {
  max-height: 300px;
  overflow-y: auto;
}
</style>