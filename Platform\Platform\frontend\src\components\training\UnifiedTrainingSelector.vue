<!--
UnifiedTrainingSelector.vue - 统一训练选择器组件
支持1:n关系的前瞻性设计：一个动作可以有多个模板变体
提供训练动作和训练日模板的统一选择界面
-->

<template>
  <div class="unified-training-selector">
    <!-- 智能筛选栏 - 全新多维标签筛选系统 -->
    <div class="smart-filter-bar">
      <!-- 主搜索框 -->
      <div class="main-search">
        <el-input 
          v-model="searchQuery" 
          placeholder="搜索动作名称、技术要点或肌群..."
          clearable
          size="large"
          @input="handleSearch">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #suffix>
            <el-tooltip content="智能搜索支持：动作名、技术标签、肌群、能力等">
              <el-icon class="search-help">❓</el-icon>
            </el-tooltip>
          </template>
        </el-input>
      </div>
      
      <!-- 多维标签筛选器 -->
      <div class="multi-dimension-filters">
        <!-- 训练目标（基础分类） -->
        <el-select 
          v-model="targetFilter" 
          placeholder="训练目标" 
          clearable
          size="default"
          class="filter-select"
          :loading="!dataLoaded">
          <template #prefix>
            <el-icon>🎯</el-icon>
          </template>
          <el-option label="全部目标" value="" />
          <el-option 
            v-for="target in filterOptions.targets" 
            :key="target.value"
            :label="target.label" 
            :value="target.value">
            <span class="option-with-icon">{{ target.icon }} {{ target.label }}</span>
          </el-option>
        </el-select>
        
        <!-- 技术要点筛选 -->
        <el-select 
          v-model="techniqueFilters" 
          placeholder="技术要点" 
          clearable
          multiple
          collapse-tags
          class="filter-select"
          :loading="!dataLoaded">
          <template #prefix>
            <el-icon>🔧</el-icon>
          </template>
          <el-option-group label="增强技术">
            <el-option 
              v-for="technique in filterOptions.techniques" 
              :key="technique.value"
              :label="technique.label" 
              :value="technique.value" />
          </el-option-group>
        </el-select>
        
        <!-- 肌群标签 -->
        <el-select 
          v-model="muscleFilters" 
          placeholder="目标肌群" 
          clearable
          multiple
          collapse-tags
          class="filter-select"
          :loading="!dataLoaded">
          <template #prefix>
            <el-icon>💪</el-icon>
          </template>
          <el-option-group label="增强肌群">
            <el-option 
              v-for="muscle in filterOptions.muscles" 
              :key="muscle.value"
              :label="muscle.label" 
              :value="muscle.value" />
          </el-option-group>
        </el-select>
        
        <!-- 训练能力 -->
        <el-select 
          v-model="abilityFilters" 
          placeholder="训练能力" 
          clearable
          multiple
          collapse-tags
          class="filter-select"
          :loading="!dataLoaded">
          <template #prefix>
            <el-icon>⚡</el-icon>
          </template>
          <el-option-group label="增强能力">
            <el-option 
              v-for="ability in filterOptions.abilities" 
              :key="ability.value"
              :label="ability.label" 
              :value="ability.value" />
          </el-option-group>
        </el-select>
      </div>
      
      <!-- 内容类型快速切换（简化版） -->
      <div class="content-type-tabs">
        <el-segmented 
          v-model="contentFilter" 
          :options="contentTypeOptions"
          @change="handleFilterChange" />
      </div>
      
      <!-- 已选标签展示 -->
      <div class="active-tags" v-if="hasActiveFilters">
        <div class="tags-row">
          <span class="tags-label">已选筛选：</span>
          
          <!-- 训练目标标签 -->
          <el-tag 
            v-if="targetFilter"
            type="primary"
            closable
            @close="targetFilter = ''">
            🎯 {{ targetFilter }}
          </el-tag>
          
          <!-- 技术标签 -->
          <el-tag 
            v-for="tech in techniqueFilters" 
            :key="tech"
            type="success"
            closable
            @close="removeTechniqueFilter(tech)">
            🔧 {{ tech }}
          </el-tag>
          
          <!-- 肌群标签 -->
          <el-tag 
            v-for="muscle in muscleFilters" 
            :key="muscle"
            type="warning"
            closable
            @close="removeMuscleFilter(muscle)">
            💪 {{ muscle }}
          </el-tag>
          
          <!-- 能力标签 -->
          <el-tag 
            v-for="ability in abilityFilters" 
            :key="ability"
            type="info"
            closable
            @close="removeAbilityFilter(ability)">
            ⚡ {{ ability }}
          </el-tag>
          
          <!-- 清除所有按钮 -->
          <el-button 
            size="small" 
            text 
            type="danger"
            @click="clearAllFilters">
            清除所有
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-tree" v-loading="loading">
      <!-- 训练动作部分（支持展开显示多个模板） -->
      <div v-if="showExercises" class="exercises-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon class="section-icon">💪</el-icon> 
            训练动作
            <el-badge :value="filteredExercises.length" class="count-badge" />
          </h4>
          
          <!-- 快速展开/折叠所有 -->
          <el-button-group class="batch-actions">
            <el-button size="small" @click="expandAllExercises">全部展开</el-button>
            <el-button size="small" @click="collapseAllExercises">全部折叠</el-button>
          </el-button-group>
        </div>
        
        <div class="exercises-list">
          <div v-for="exercise in filteredExercises" 
               :key="exercise.id"
               class="exercise-item"
               :class="{ 'expanded': expandedExercises.includes(exercise.id) }">
            
            <!-- 动作主体（可展开） -->
            <div class="exercise-header" 
                 @click="toggleExercise(exercise.id)">
              
              <div class="exercise-left">
                <el-icon class="expand-icon">
                  <ArrowRight v-if="!expandedExercises.includes(exercise.id)" />
                  <ArrowDown v-else />
                </el-icon>
                
                <div class="exercise-info">
                  <span class="exercise-name">{{ exercise.name }}</span>
                  <span v-if="exercise.description" class="exercise-description">
                    {{ exercise.description }}
                  </span>
                  
                  <!-- 新增：标签显示 -->
                  <div v-if="exercise.tags && exercise.tags.length > 0" class="exercise-tags">
                    <el-tag v-for="tag in exercise.tags.slice(0, 3)" 
                           :key="tag" 
                           size="small"
                           effect="plain"
                           class="tag-item">
                      {{ tag }}
                    </el-tag>
                    <span v-if="exercise.tags.length > 3" class="more-tags">
                      +{{ exercise.tags.length - 3 }}
                    </span>
                  </div>
                  
                  <!-- 新增：相关性评分（当有筛选条件时显示） -->
                  <div v-if="activeFiltersCount > 0" class="relevance-score">
                    <el-icon class="score-icon"><TrendCharts /></el-icon>
                    <span class="score-text">匹配度: {{ getRelevanceScore(exercise) }}%</span>
                  </div>
                </div>
              </div>
              
              <div class="exercise-right">
                <!-- 模板数量标识 -->
                <el-badge v-if="exercise.templates.length > 0" 
                         :value="exercise.templates.length" 
                         class="template-count"
                         type="primary">
                  <span class="badge-label">模板</span>
                </el-badge>
                
                <!-- 快速使用按钮（使用默认模板） - 响应式尺寸 -->
                <el-button v-if="exercise.defaultTemplate"
                          size="small" 
                          type="primary"
                          class="quick-use-btn"
                          @click.stop="quickUse(exercise, exercise.defaultTemplate)">
                  <el-icon><Lightning /></el-icon>
                  <span class="btn-text">快速使用</span>
                </el-button>
                
                <!-- 无模板时的基础使用 -->
                <el-button v-else
                          size="small"
                          type="default"
                          class="basic-use-btn"
                          @click.stop="useBasic(exercise)">
                  <el-icon><Plus /></el-icon>
                  <span class="btn-text">基础配置</span>
                </el-button>
              </div>
            </div>
            
            <!-- 展开的模板列表 -->
            <el-collapse-transition>
              <div v-show="expandedExercises.includes(exercise.id)" 
                   class="templates-panel">
                
                <!-- 无模板提示 -->
                <div v-if="exercise.templates.length === 0" class="no-templates">
                  <el-empty description="暂无配置模板" :image-size="60">
                    <el-button size="small" type="primary" @click="createTemplate(exercise)">
                      <el-icon><Plus /></el-icon>
                      创建模板
                    </el-button>
                  </el-empty>
                </div>
                
                <!-- 模板列表 -->
                <div v-else class="template-list">
                  <div v-for="template in exercise.templates" 
                       :key="template.id"
                       class="template-card"
                       @click="selectTemplate(exercise, template)"
                       :draggable="true"
                       @dragstart="handleDragStart('template', {exercise, template}, $event)">
                    
                    <div class="template-header">
                      <!-- 默认标记 -->
                      <el-tag v-if="template.id === exercise.defaultTemplateId" 
                             type="success" 
                             size="small"
                             class="default-tag">
                        <el-icon><Star /></el-icon>
                        默认
                      </el-tag>
                      
                      <!-- 模板信息 -->
                      <div class="template-info">
                        <span class="template-name">
                          {{ template.exercise_name || `配置${template.id}` }}
                        </span>
                        <div class="template-meta">
                          <!-- 指标数量显示 -->
                          <el-tag v-if="template.metrics_count" 
                                 size="small" 
                                 type="info" 
                                 class="metrics-count">
                            <el-icon><DataAnalysis /></el-icon>
                            {{ template.metrics_count }}个指标
                          </el-tag>
                          
                          <el-tag size="small" class="granularity-tag">
                            {{ getGranularityLabel(template.training_config?.measurement_granularity) }}
                          </el-tag>
                          <span class="created-time">
                            {{ formatTime(template.created_at) }}
                          </span>
                        </div>
                        
                        <!-- 指标详情（可选展示前几个主要指标） -->
                        <div v-if="template.metrics && template.metrics.length > 0" class="template-metrics">
                          <div class="metrics-summary">
                            <span class="metrics-label">主要指标:</span>
                            <el-tag v-for="metric in template.metrics.slice(0, 3)" 
                                   :key="metric.id" 
                                   size="small"
                                   class="metric-tag">
                              {{ metric.name }}
                            </el-tag>
                            <span v-if="template.metrics.length > 3" class="more-metrics">
                              +{{ template.metrics.length - 3 }}个
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="template-actions" @click.stop>
                      <el-button-group>
                        <el-button size="small" type="primary" @click="useTemplate(exercise, template)">
                          <el-icon><Check /></el-icon>
                          使用
                        </el-button>
                        <el-button size="small" @click="editTemplate(template)">
                          <el-icon><Edit /></el-icon>
                          编辑
                        </el-button>
                        <el-button size="small" type="danger" @click="deleteTemplate(template)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </el-button-group>
                    </div>
                  </div>
                  
                  <!-- 添加新模板按钮 -->
                  <div class="add-template-card" @click="createTemplate(exercise)">
                    <el-icon class="add-icon"><Plus /></el-icon>
                    <span>添加新模板</span>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>

      <!-- 配置模板专用区域 -->
      <div v-if="showTemplatesOnly" class="templates-only-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon class="section-icon">📋</el-icon> 
            训练配置模板
            <el-badge :value="allTemplatesCount" class="count-badge" />
          </h4>
          
          <div class="template-filters">
            <el-select v-model="templateCategoryFilter" placeholder="按类别筛选" clearable size="small">
              <el-option label="全部类别" value="" />
              <el-option v-for="category in templateCategories" :key="category" :label="category" :value="category" />
            </el-select>
          </div>
        </div>
        
        <div class="templates-grid">
          <div v-for="template in filteredTemplatesOnly" 
               :key="template.id"
               class="template-card-large"
               @click="selectTemplate(null, template)">
            
            <div class="template-card-header">
              <el-tag v-if="template.is_default" type="success" size="small" class="default-tag">
                <el-icon><Star /></el-icon>
                默认配置
              </el-tag>
              
              <div class="template-actions-corner" @click.stop>
                <el-dropdown trigger="click">
                  <el-icon class="more-actions"><More /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="useTemplate(null, template)">
                        <el-icon><Check /></el-icon>
                        使用模板
                      </el-dropdown-item>
                      <el-dropdown-item @click="editTemplate(template)">
                        <el-icon><Edit /></el-icon>
                        编辑模板
                      </el-dropdown-item>
                      <el-dropdown-item @click="duplicateTemplate(template)">
                        <el-icon><DocumentCopy /></el-icon>
                        复制模板
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="deleteTemplate(template)">
                        <el-icon><Delete /></el-icon>
                        删除模板
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div class="template-card-content">
              <h5 class="template-title">{{ template.exercise_name || `模板 ${template.id}` }}</h5>
              <p v-if="template.description" class="template-desc">{{ template.description }}</p>
              
              <div class="template-stats">
                <el-tag size="small" type="info" class="stat-tag">
                  <el-icon><DataAnalysis /></el-icon>
                  {{ template.metrics_count || 0 }}个指标
                </el-tag>
                <el-tag size="small" class="stat-tag">
                  {{ getGranularityLabel(template.training_config?.measurement_granularity) }}
                </el-tag>
                <span class="template-time">{{ formatTime(template.created_at) }}</span>
              </div>
              
              <!-- 指标预览 -->
              <div v-if="template.metrics && template.metrics.length > 0" class="metrics-preview">
                <div class="metrics-tags">
                  <el-tag v-for="metric in template.metrics.slice(0, 4)" 
                         :key="metric.id" 
                         size="small"
                         effect="plain"
                         class="metric-preview-tag">
                    {{ metric.name }}
                  </el-tag>
                  <span v-if="template.metrics.length > 4" class="more-metrics">
                    +{{ template.metrics.length - 4 }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="template-card-footer">
              <el-button size="small" type="primary" @click.stop="useTemplate(null, template)">
                <el-icon><Check /></el-icon>
                使用此模板
              </el-button>
              <el-button size="small" @click.stop="editTemplate(template)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </div>
          
          <!-- 创建新模板卡片 -->
          <div class="create-template-card" @click="createNewTemplate">
            <div class="create-content">
              <el-icon class="create-icon"><Plus /></el-icon>
              <h5>创建新模板</h5>
              <p>从零开始构建训练配置</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练日模板部分 -->
      <div v-if="showDailyTemplates" class="daily-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon class="section-icon">📅</el-icon> 
            训练日模板
            <el-badge :value="filteredDailyTemplates.length" class="count-badge" />
          </h4>
          
          <!-- 快速展开/折叠所有类别 -->
          <el-button-group class="batch-actions">
            <el-button size="small" @click="expandAllCategories">全部展开</el-button>
            <el-button size="small" @click="collapseAllCategories">全部折叠</el-button>
          </el-button-group>
        </div>
        
        <!-- 按类别分组 -->
        <div v-for="category in dailyCategories" :key="category" class="category-group">
          <div class="category-header" @click="toggleCategory(category)">
            <el-icon class="expand-icon">
              <ArrowRight v-if="!expandedCategories.includes(category)" />
              <ArrowDown v-else />
            </el-icon>
            <h5 class="category-title">{{ category }}</h5>
            <el-badge :value="getCategoryCount(category)" class="category-count" />
          </div>
          
          <el-collapse-transition>
            <div v-show="expandedCategories.includes(category)" class="daily-templates">
              <div v-for="template in getDailyByCategory(category)"
                   :key="template.id"
                   class="daily-template-card"
                   @click="selectDailyTemplate(template)"
                   :draggable="true"
                   @dragstart="handleDragStart('daily', template, $event)">
                
                <div class="daily-template-content">
                  <div class="daily-template-info">
                    <h6 class="daily-template-name">{{ template.name }}</h6>
                    <p class="daily-template-description">{{ template.description }}</p>
                  </div>
                  
                  <div class="daily-template-meta">
                    <el-tag size="small" type="info">
                      {{ template.estimated_duration }}分钟
                    </el-tag>
                    <el-tag v-if="template.difficulty_level" size="small">
                      {{ template.difficulty_level }}
                    </el-tag>
                  </div>
                </div>
                
                <div class="daily-template-actions" @click.stop>
                  <el-button size="small" type="primary" @click="useDailyTemplate(template)">
                    <el-icon><Check /></el-icon>
                    应用
                  </el-button>
                  <el-button size="small" @click="viewDailyTemplate(template)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && filteredExercises.length === 0 && filteredDailyTemplates.length === 0" 
           class="empty-state">
        <el-empty description="没有找到匹配的训练内容">
          <el-button @click="clearFilters">清除筛选条件</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, ArrowRight, ArrowDown, Plus, Lightning, Check, Edit, Delete, 
  Star, View, DataAnalysis, Grid, Trophy, DocumentCopy, Calendar, More, TrendCharts
} from '@element-plus/icons-vue'
import { getCategories } from '@/api/training'

// Props定义
const props = defineProps({
  exercises: {
    type: Array,
    default: () => []
  },
  templates: {
    type: Array,
    default: () => []
  },
  dailyTemplates: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits([
  'use-exercise',
  'use-template', 
  'use-daily',
  'create-template',
  'edit-template',
  'delete-template',
  'view-daily'
])

// 响应式状态
const searchQuery = ref('')
const contentFilter = ref('all')
const expandedExercises = ref([])
const expandedCategories = ref([])
const templateCategoryFilter = ref('')

// 新增：多维筛选系统状态
const targetFilter = ref('') // 训练目标筛选
const techniqueFilters = ref([]) // 技术要点筛选（多选）
const muscleFilters = ref([]) // 目标肌群筛选（多选）
const abilityFilters = ref([]) // 训练能力筛选（多选）
const activeFiltersCount = ref(0) // 激活的筛选条件数量

// 动态数据状态（从API获取）
const categories = ref([]) // 分类数据
const tags = ref([]) // 标签数据
const dataLoaded = ref(false) // 数据加载状态

// 内容类型选项配置
const contentTypeOptions = [
  { label: '全部', value: 'all' },
  { label: '训练动作', value: 'exercises' },
  { label: '配置模板', value: 'templates' },
  { label: '训练日模板', value: 'daily' }
]

// 筛选选项配置（动态从API获取）
const filterOptions = computed(() => {
  if (!dataLoaded.value) return { targets: [], techniques: [], muscles: [], abilities: [] }
  
  return {
    // 训练目标（基础分类）
    targets: categories.value.map(cat => ({
      value: cat.name,
      label: cat.name,
      icon: getTargetIcon(cat.name),
      id: cat.id
    })),
    
    // 按分组获取标签
    techniques: tags.value.filter(tag => tag.category_name === '增强技术').map(tag => ({
      value: tag.name,
      label: tag.name,
      group: tag.category_name,
      id: tag.id
    })),
    
    muscles: tags.value.filter(tag => tag.category_name === '增强肌群').map(tag => ({
      value: tag.name,
      label: tag.name,
      group: tag.category_name,
      id: tag.id
    })),
    
    abilities: tags.value.filter(tag => tag.category_name === '增强能力').map(tag => ({
      value: tag.name,
      label: tag.name,
      group: tag.category_name,
      id: tag.id
    }))
  }
})

// 获取目标图标的辅助函数
const getTargetIcon = (targetName) => {
  const iconMap = {
    '速度训练': '⚡',
    '力量训练': '💪', 
    '爆发力训练': '💥',
    '技术训练': '🎯',
    '综合训练': '🔄'
  }
  return iconMap[targetName] || '🏃'
}

// 数据处理逻辑：支持1:n关系
const processedExercises = computed(() => {
  return props.exercises.map(exercise => {
    // 查找该动作的所有模板（支持1:n）
    const relatedTemplates = props.templates.filter(t => t.exercise_id === exercise.id)
    
    return {
      ...exercise,
      templates: relatedTemplates,
      defaultTemplate: relatedTemplates.find(t => t.is_default) || relatedTemplates[0],
      defaultTemplateId: relatedTemplates.find(t => t.is_default)?.id || relatedTemplates[0]?.id
    }
  })
})

// 计算属性 - 重新设计显示逻辑
const showExercises = computed(() => {
  return contentFilter.value === 'all' || contentFilter.value === 'exercises'
})

const showTemplatesOnly = computed(() => {
  return contentFilter.value === 'templates'
})

const showDailyTemplates = computed(() => {
  return contentFilter.value === 'all' || contentFilter.value === 'daily'
})

const filteredExercises = computed(() => {
  return updateFilteredExercises()
})

const filteredDailyTemplates = computed(() => {
  let filtered = props.dailyTemplates
  
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(template =>
      template.name.toLowerCase().includes(query) ||
      template.description?.toLowerCase().includes(query) ||
      template.template_category?.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

const dailyCategories = computed(() => {
  const categories = new Set()
  filteredDailyTemplates.value.forEach(template => {
    if (template.template_category) {
      categories.add(template.template_category)
    }
  })
  return Array.from(categories).sort()
})

// 新增：模板相关计算属性
const allTemplatesCount = computed(() => {
  return props.templates.length
})

const templateCategories = computed(() => {
  const categories = new Set()
  props.templates.forEach(template => {
    if (template.category) {
      categories.add(template.category)
    }
  })
  return Array.from(categories).sort()
})

const filteredTemplatesOnly = computed(() => {
  let filtered = props.templates
  
  // 按类别筛选
  if (templateCategoryFilter.value) {
    filtered = filtered.filter(t => t.category === templateCategoryFilter.value)
  }
  
  // 按搜索词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(template =>
      template.exercise_name?.toLowerCase().includes(query) ||
      template.description?.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

// 判断是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return targetFilter.value || 
         techniqueFilters.value.length > 0 || 
         muscleFilters.value.length > 0 || 
         abilityFilters.value.length > 0
})

// 方法
const handleSearch = () => {
  // 搜索时可以自动展开相关项目
  if (searchQuery.value.trim()) {
    // 如果搜索到匹配的动作，自动展开第一个
    if (filteredExercises.value.length > 0 && expandedExercises.value.length === 0) {
      expandedExercises.value.push(filteredExercises.value[0].id)
    }
  }
}

const handleFilterChange = () => {
  // 切换筛选类型时，清空展开状态
  expandedExercises.value = []
  expandedCategories.value = []
}

const toggleExercise = (exerciseId) => {
  const index = expandedExercises.value.indexOf(exerciseId)
  if (index > -1) {
    expandedExercises.value.splice(index, 1)
  } else {
    expandedExercises.value.push(exerciseId)
  }
}

const toggleCategory = (category) => {
  const index = expandedCategories.value.indexOf(category)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(category)
  }
}

const expandAllExercises = () => {
  expandedExercises.value = filteredExercises.value.map(ex => ex.id)
}

const collapseAllExercises = () => {
  expandedExercises.value = []
}

const expandAllCategories = () => {
  expandedCategories.value = [...dailyCategories.value]
}

const collapseAllCategories = () => {
  expandedCategories.value = []
}

const getCategoryCount = (category) => {
  return filteredDailyTemplates.value.filter(t => t.template_category === category).length
}

const getDailyByCategory = (category) => {
  return filteredDailyTemplates.value.filter(t => t.template_category === category)
}

const getGranularityLabel = (granularity) => {
  const labels = {
    'rep': '次数',
    'set': '组数',
    'time': '时间',
    'distance': '距离'
  }
  return labels[granularity] || '标准'
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString()
}

const clearFilters = () => {
  searchQuery.value = ''
  contentFilter.value = 'all'
}

// 事件处理器
const quickUse = (exercise, template) => {
  emit('use-template', exercise, template)
  ElMessage.success(`已使用：${exercise.name}（默认配置）`)
}

const useBasic = (exercise) => {
  emit('use-exercise', exercise)
  ElMessage.success(`已添加：${exercise.name}（基础配置）`)
}

const selectTemplate = (exercise, template) => {
  emit('use-template', exercise, template)
}

const useTemplate = (exercise, template) => {
  emit('use-template', exercise, template)
  ElMessage.success(`已使用：${exercise.name}（${template.exercise_name}）`)
}

const createTemplate = (exercise) => {
  emit('create-template', exercise)
}

const editTemplate = (template) => {
  emit('edit-template', template)
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.exercise_name}"吗？`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }
    )
    emit('delete-template', template)
  } catch {
    // 用户取消
  }
}

const selectDailyTemplate = (template) => {
  emit('use-daily', template)
}

const useDailyTemplate = (template) => {
  emit('use-daily', template)
  ElMessage.success(`已应用训练日模板：${template.name}`)
}

const viewDailyTemplate = (template) => {
  emit('view-daily', template)
}

const handleDragStart = (type, data, event) => {
  event.dataTransfer.setData('application/json', JSON.stringify({
    type,
    data
  }))
}

// 新增方法
const createNewTemplate = () => {
  emit('create-template', null)
}

const duplicateTemplate = (template) => {
  emit('create-template', null, template) // 传递模板作为复制源
  ElMessage.success(`正在复制模板：${template.exercise_name}`)
}

// 数据加载方法
const loadFilterData = async () => {
  try {
    // 加载分类数据
    const categoriesRes = await getCategories()
    if (categoriesRes.data) {
      categories.value = categoriesRes.data
      
      // 从分类数据中提取所有子分类作为标签
      const allSubcategories = []
      categoriesRes.data.forEach(category => {
        if (category.subcategories && category.subcategories.length > 0) {
          category.subcategories.forEach(subcategory => {
            allSubcategories.push({
              ...subcategory,
              category_name: category.name
            })
          })
        }
      })
      tags.value = allSubcategories
    }

    dataLoaded.value = true
    console.log('筛选数据加载成功:', {
      categoriesCount: categories.value.length,
      tagsCount: tags.value.length
    })
  } catch (error) {
    console.error('加载筛选数据失败:', error)
    // 不显示错误提示，静默处理
    dataLoaded.value = true // 即使失败也标记为已加载，使用空数据
  }
}

// 多维筛选逻辑更新
const updateFilteredExercises = () => {
  let filtered = processedExercises.value

  // 文本搜索
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(exercise => 
      exercise.name.toLowerCase().includes(query) ||
      exercise.description?.toLowerCase().includes(query) ||
      exercise.templates.some(t => 
        t.exercise_name?.toLowerCase().includes(query)
      )
    )
  }

  // 训练目标筛选
  if (targetFilter.value) {
    filtered = filtered.filter(exercise => 
      exercise.category === targetFilter.value ||
      exercise.templates.some(t => t.category === targetFilter.value)
    )
  }

  // 技术要点筛选
  if (techniqueFilters.value.length > 0) {
    filtered = filtered.filter(exercise => {
      if (exercise.tags) {
        return techniqueFilters.value.some(technique => 
          exercise.tags.includes(technique)
        )
      }
      return false
    })
  }

  // 目标肌群筛选
  if (muscleFilters.value.length > 0) {
    filtered = filtered.filter(exercise => {
      if (exercise.target_muscles || exercise.muscle_groups) {
        const muscles = [...(exercise.target_muscles || []), ...(exercise.muscle_groups || [])]
        return muscleFilters.value.some(muscle => 
          muscles.includes(muscle) || muscles.some(m => m.includes(muscle))
        )
      }
      return false
    })
  }

  // 训练能力筛选
  if (abilityFilters.value.length > 0) {
    filtered = filtered.filter(exercise => {
      if (exercise.abilities || exercise.training_abilities) {
        const abilities = [...(exercise.abilities || []), ...(exercise.training_abilities || [])]
        return abilityFilters.value.some(ability => 
          abilities.includes(ability) || abilities.some(a => a.includes(ability))
        )
      }
      return false
    })
  }

  return filtered
}

// 清除所有筛选条件
const clearAllFilters = () => {
  searchQuery.value = ''
  targetFilter.value = ''
  techniqueFilters.value = []
  muscleFilters.value = []
  abilityFilters.value = []
  activeFiltersCount.value = 0
}

// 移除单个筛选条件的方法
const removeTechniqueFilter = (technique) => {
  const index = techniqueFilters.value.indexOf(technique)
  if (index > -1) {
    techniqueFilters.value.splice(index, 1)
  }
}

const removeMuscleFilter = (muscle) => {
  const index = muscleFilters.value.indexOf(muscle)
  if (index > -1) {
    muscleFilters.value.splice(index, 1)
  }
}

const removeAbilityFilter = (ability) => {
  const index = abilityFilters.value.indexOf(ability)
  if (index > -1) {
    abilityFilters.value.splice(index, 1)
  }
}

// 计算活跃筛选条件数量
const updateActiveFiltersCount = () => {
  let count = 0
  if (targetFilter.value) count++
  count += techniqueFilters.value.length
  count += muscleFilters.value.length
  count += abilityFilters.value.length
  activeFiltersCount.value = count
}

// 计算动作的相关性评分（0-100）
const getRelevanceScore = (exercise) => {
  let score = 0
  let totalPossible = 0
  
  // 训练目标匹配 (权重：30%)
  if (targetFilter.value) {
    totalPossible += 30
    if (exercise.category === targetFilter.value || 
        exercise.templates.some(t => t.category === targetFilter.value)) {
      score += 30
    }
  }
  
  // 技术要点匹配 (权重：25%)
  if (techniqueFilters.value.length > 0) {
    totalPossible += 25
    const matchedTechniques = techniqueFilters.value.filter(technique => 
      exercise.tags?.includes(technique)
    )
    score += Math.round((matchedTechniques.length / techniqueFilters.value.length) * 25)
  }
  
  // 目标肌群匹配 (权重：25%)
  if (muscleFilters.value.length > 0) {
    totalPossible += 25
    const muscles = [...(exercise.target_muscles || []), ...(exercise.muscle_groups || [])]
    const matchedMuscles = muscleFilters.value.filter(muscle => 
      muscles.includes(muscle) || muscles.some(m => m.includes(muscle))
    )
    score += Math.round((matchedMuscles.length / muscleFilters.value.length) * 25)
  }
  
  // 训练能力匹配 (权重：20%)
  if (abilityFilters.value.length > 0) {
    totalPossible += 20
    const abilities = [...(exercise.abilities || []), ...(exercise.training_abilities || [])]
    const matchedAbilities = abilityFilters.value.filter(ability => 
      abilities.includes(ability) || abilities.some(a => a.includes(ability))
    )
    score += Math.round((matchedAbilities.length / abilityFilters.value.length) * 20)
  }
  
  // 如果没有筛选条件，返回100%
  if (totalPossible === 0) return 100
  
  // 计算最终评分并限制在0-100范围内
  return Math.min(100, Math.max(0, Math.round((score / totalPossible) * 100)))
}

// 监听筛选条件变化
watch([targetFilter, techniqueFilters, muscleFilters, abilityFilters], updateActiveFiltersCount, { deep: true })

// 组件挂载时加载数据
onMounted(() => {
  loadFilterData()
})
</script>

<style scoped>
.unified-training-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.selector-header {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e1e8ed;
  gap: 12px;
  display: flex;
  flex-direction: column;
}

.content-filter {
  margin-top: 12px;
}

.content-tree {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  padding-bottom: 20px; /* 底部留白确保最后内容完全可见 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 8px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 18px;
}

.count-badge {
  margin-left: 8px;
}

.batch-actions {
  flex-shrink: 0;
}

.exercises-section, .daily-section {
  background: white;
  margin-bottom: 8px;
}

.daily-section {
  margin-bottom: 20px; /* 训练日模板区域增加底部间距 */
}

.exercises-list {
  padding: 0 16px 16px;
}

.exercise-item {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.3s ease;
}

.exercise-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.exercise-item.expanded {
  border-color: #409eff;
}

.exercise-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
}

.exercise-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.expand-icon {
  color: #909399;
  transition: transform 0.3s ease;
}

.exercise-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.exercise-name {
  font-weight: 500;
  color: #303133 !important; /* 确保深色文字可见 */
  font-size: 14px;
}

.exercise-description {
  font-size: 12px;
  color: #909399;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 新增：标签相关样式 */
.exercise-tags {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  flex-wrap: wrap;
}

.tag-item {
  background: #f0f7ff;
  border-color: #b3d8ff;
  color: #409eff;
  font-size: 11px;
  padding: 2px 6px;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-tags {
  font-size: 11px;
  color: #909399;
  font-style: italic;
}

/* 新增：相关性评分样式 */
.relevance-score {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  padding: 2px 6px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-radius: 12px;
  max-width: fit-content;
}

.score-icon {
  font-size: 12px;
  color: white;
}

.score-text {
  font-size: 11px;
  color: white;
  font-weight: 500;
}

/* 智能筛选栏样式 */
.smart-filter-bar {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.main-search .el-input {
  --el-input-border-radius: 20px;
}

.multi-dimension-filters {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

@media (max-width: 768px) {
  .multi-dimension-filters {
    grid-template-columns: 1fr;
  }
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.filter-count-badge {
  background: #409eff;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
}

.clear-all-btn {
  margin-left: auto;
  flex-shrink: 0;
}

.exercise-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  max-width: 160px; /* 限制最大宽度防止溢出 */
}

.template-count {
  flex-shrink: 0;
}

.badge-label {
  font-size: 11px;
  color: #666;
}

.templates-panel {
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  padding: 16px;
}

.no-templates {
  padding: 20px 0;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-card {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.template-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.default-tag {
  flex-shrink: 0;
}

.template-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.template-name {
  font-weight: 500;
  font-size: 13px;
  color: #303133 !important; /* 确保深色文字可见 */
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.granularity-tag {
  font-size: 11px;
}

.created-time {
  font-size: 11px;
  color: #909399;
}

.template-actions {
  flex-shrink: 0;
}

.add-template-card {
  background: #f8f9fa;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.add-template-card:hover {
  border-color: #409eff;
  color: #409eff;
  background: #f0f7ff;
}

.add-icon {
  font-size: 20px;
}

.category-group {
  margin-bottom: 8px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f8f9fa;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #e1e8ed;
}

.category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.category-count {
  flex-shrink: 0;
}

.daily-templates {
  padding: 16px 20px;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.daily-template-card {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.daily-template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.daily-template-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.daily-template-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.daily-template-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133 !important; /* 确保深色文字可见 */
}

.daily-template-description {
  margin: 0;
  font-size: 12px;
  color: #909399;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.daily-template-meta {
  display: flex;
  gap: 8px;
}

.daily-template-actions {
  flex-shrink: 0;
}

/* 新增：配置模板专用区域样式 */
.templates-only-section {
  background: white;
  margin-bottom: 20px;
}

.template-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 20px;
}

.template-card-large {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.template-card-large:hover {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0;
}

.template-actions-corner {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.template-actions-corner:hover {
  background-color: #f5f7fa;
}

.more-actions {
  color: #909399;
  font-size: 16px;
}

.template-card-content {
  padding: 12px 16px;
}

.template-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133 !important;
  line-height: 1.4;
}

.template-desc {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.stat-tag {
  flex-shrink: 0;
}

.template-time {
  font-size: 11px;
  color: #909399;
  margin-left: auto;
}

.metrics-preview {
  margin-top: 12px;
}

.metrics-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.metric-preview-tag {
  font-size: 11px;
}

.template-card-footer {
  padding: 12px 16px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

.create-template-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f7ff 100%);
  border: 2px dashed #c6d9ff;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.create-template-card:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f2ff 100%);
  transform: translateY(-2px);
}

.create-content {
  text-align: center;
  color: #409eff;
}

.create-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.create-content h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.create-content p {
  margin: 0;
  font-size: 13px;
  color: #909399;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 快速使用按钮响应式样式 */
.quick-use-btn, .basic-use-btn {
  white-space: nowrap;
  max-width: 100px;
}

.quick-use-btn .btn-text, .basic-use-btn .btn-text {
  margin-left: 4px;
}

@media (max-width: 400px) {
  .quick-use-btn .btn-text, .basic-use-btn .btn-text {
    display: none; /* 小屏幕下只显示图标 */
  }
  
  .exercise-right {
    max-width: 80px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-header {
    padding: 12px;
  }
  
  .section-header {
    padding: 12px 16px 8px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .exercise-header {
    padding: 10px 12px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .exercise-left {
    order: 1;
  }
  
  .exercise-right {
    order: 2;
    justify-content: flex-end;
  }
  
  .template-card, .daily-template-card {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .template-actions, .daily-template-actions {
    align-self: stretch;
  }
}
</style>