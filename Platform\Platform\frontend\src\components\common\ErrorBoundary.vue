<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">⚠️</div>
      <h3 class="error-title">出现了一些问题</h3>
      <p class="error-message">{{ errorMessage }}</p>
      
      <div class="error-actions">
        <el-button @click="retry" type="primary">
          重试
        </el-button>
        <el-button @click="reportError" type="default">
          报告问题
        </el-button>
        <el-button @click="goBack" type="default">
          返回上一页
        </el-button>
      </div>
      
      <details v-if="isDevelopment" class="error-details">
        <summary>技术详情</summary>
        <pre class="error-stack">{{ errorDetails }}</pre>
      </details>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, provide } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

interface Props {
  fallback?: string
  onError?: (error: Error, instance: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallback: '组件加载失败'
})

const router = useRouter()

const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')
const errorInstance = ref<Error | null>(null)

const isDevelopment = process.env.NODE_ENV === 'development'

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  console.error('ErrorBoundary captured:', error, info)
  
  hasError.value = true
  errorMessage.value = props.fallback
  errorDetails.value = `${error.message}\n\n${error.stack}`
  errorInstance.value = error
  
  // 调用自定义错误处理
  props.onError?.(error, instance)
  
  // 阻止错误继续传播
  return false
})

// 提供重置错误状态的方法
provide('resetError', () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  errorInstance.value = null
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  errorInstance.value = null
  
  // 重新渲染子组件
  location.reload()
}

const reportError = () => {
  if (errorInstance.value) {
    // 这里可以集成错误报告服务，如Sentry
    console.log('Reporting error:', errorInstance.value)
    ElMessage.success('错误报告已发送')
  }
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 500px;
  background: #fff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 12px;
}

.error-message {
  color: #606266;
  margin-bottom: 24px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.error-details {
  text-align: left;
  margin-top: 20px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.error-stack {
  font-size: 12px;
  color: #909399;
  white-space: pre-wrap;
  overflow: auto;
  max-height: 200px;
}
</style>