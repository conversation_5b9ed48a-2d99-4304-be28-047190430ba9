<template>
  <div class="video-urls-manager">
    <div class="manager-header">
      <div class="manager-info">
        <el-icon><VideoPlay /></el-icon>
        <span class="manager-title">教学视频管理</span>
        <el-tag size="small" type="info" v-if="videoList.length > 0">
          {{ videoList.length }} 个视频
        </el-tag>
      </div>
      
      <el-button size="small" @click="addVideo">
        <el-icon><Plus /></el-icon>
        添加视频
      </el-button>
    </div>

    <div class="video-list" v-if="videoList.length > 0">
      <div
        v-for="(video, index) in videoList"
        :key="video.id"
        class="video-item"
        :class="{ 'video-editing': editingIndex === index }"
      >
        <div class="video-header">
          <div class="video-info">
            <div class="video-index">{{ index + 1 }}</div>
            <div class="video-details">
              <div class="video-title">{{ video.title || `教学视频 ${index + 1}` }}</div>
              <div class="video-url" v-if="!isEditing(index)">{{ formatUrl(video.url) }}</div>
            </div>
          </div>
          
          <div class="video-actions">
            <el-button size="small" type="text" @click="toggleEdit(index)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button size="small" type="text" @click="previewVideo(video)" v-if="video.url">
              <el-icon><View /></el-icon>
            </el-button>
            <el-button size="small" type="text" @click="moveUp(index)" :disabled="index === 0">
              <el-icon><Top /></el-icon>
            </el-button>
            <el-button size="small" type="text" @click="moveDown(index)" :disabled="index === videoList.length - 1">
              <el-icon><Bottom /></el-icon>
            </el-button>
            <el-button size="small" type="text" @click="removeVideo(index)" class="delete-btn">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="video-content" v-if="isEditing(index)">
          <el-form
            ref="videoFormRef"
            :model="editingVideo"
            :rules="videoRules"
            label-width="80px"
            size="small"
          >
            <el-form-item label="视频标题" prop="title">
              <el-input
                v-model="editingVideo.title"
                placeholder="请输入视频标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="视频URL" prop="url">
              <el-input
                v-model="editingVideo.url"
                placeholder="请输入视频地址（支持优酷、B站、YouTube等）"
                @blur="validateVideoUrl"
              >
                <template #append>
                  <el-button @click="testVideoUrl" :loading="testingUrl">
                    <el-icon><Connection /></el-icon>
                    测试
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="视频描述">
              <el-input
                v-model="editingVideo.description"
                type="textarea"
                :rows="2"
                placeholder="简要描述视频内容（可选）"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="时长">
              <el-input
                v-model="editingVideo.duration"
                placeholder="如: 5:30 或 5分30秒"
                style="width: 150px"
              />
            </el-form-item>

            <div class="form-actions">
              <el-button size="small" @click="saveVideo" type="primary">保存</el-button>
              <el-button size="small" @click="cancelEdit">取消</el-button>
            </div>
          </el-form>
        </div>

        <div class="video-preview" v-else-if="video.url">
          <div class="preview-info" v-if="video.description || video.duration">
            <div class="preview-description" v-if="video.description">
              {{ video.description }}
            </div>
            <div class="preview-duration" v-if="video.duration">
              <el-icon><Timer /></el-icon>
              {{ video.duration }}
            </div>
          </div>
          
          <div class="url-status" :class="getUrlStatusClass(video.url_status)">
            <el-icon v-if="video.url_status === 'valid'"><CircleCheck /></el-icon>
            <el-icon v-else-if="video.url_status === 'invalid'"><CircleClose /></el-icon>
            <el-icon v-else><QuestionFilled /></el-icon>
            <span>{{ getUrlStatusText(video.url_status) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="empty-state" v-else>
      <el-icon class="empty-icon"><VideoPlay /></el-icon>
      <p>暂无教学视频</p>
      <p class="empty-hint">添加视频可以帮助用户更好地学习动作要领</p>
    </div>

    <!-- 视频预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="视频预览"
      width="800px"
      :before-close="handlePreviewClose"
    >
      <div class="video-preview-content" v-if="previewingVideo">
        <h3>{{ previewingVideo.title }}</h3>
        
        <div class="video-embed" v-if="embedCode">
          <div v-html="embedCode" class="embed-container"></div>
        </div>
        
        <div class="video-link" v-else>
          <p>无法嵌入预览，请<a :href="previewingVideo.url" target="_blank">点击链接</a>在新窗口中查看</p>
        </div>
        
        <div class="video-metadata">
          <p><strong>URL:</strong> {{ previewingVideo.url }}</p>
          <p v-if="previewingVideo.description"><strong>描述:</strong> {{ previewingVideo.description }}</p>
          <p v-if="previewingVideo.duration"><strong>时长:</strong> {{ previewingVideo.duration }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Plus, Edit, View, Delete, Top, Bottom, Connection, Timer, CircleCheck, CircleClose, QuestionFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const videoList = ref([])
const editingIndex = ref(-1)
const editingVideo = ref({})
const videoFormRef = ref()
const testingUrl = ref(false)
const previewDialogVisible = ref(false)
const previewingVideo = ref(null)

// 表单验证规则
const videoRules = {
  title: [
    { required: true, message: '请输入视频标题', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入视频URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ]
}

// 计算属性
const embedCode = computed(() => {
  if (!previewingVideo.value?.url) return null
  return generateEmbedCode(previewingVideo.value.url)
})

// 初始化数据
const initializeVideoList = () => {
  videoList.value = props.modelValue.map((video, index) => {
    if (typeof video === 'string') {
      // 如果是字符串，转换为对象格式
      return {
        id: generateId(),
        url: video,
        title: `教学视频 ${index + 1}`,
        description: '',
        duration: '',
        url_status: 'unknown'
      }
    }
    // 确保对象有必要的字段
    return {
      id: video.id || generateId(),
      url: video.url || '',
      title: video.title || `教学视频 ${index + 1}`,
      description: video.description || '',
      duration: video.duration || '',
      url_status: video.url_status || 'unknown'
    }
  })
}

// 方法
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

const isEditing = (index) => {
  return editingIndex.value === index
}

const formatUrl = (url) => {
  if (!url) return ''
  if (url.length > 60) {
    return url.substring(0, 60) + '...'
  }
  return url
}

const addVideo = () => {
  const newVideo = {
    id: generateId(),
    url: '',
    title: `教学视频 ${videoList.value.length + 1}`,
    description: '',
    duration: '',
    url_status: 'unknown'
  }
  
  videoList.value.push(newVideo)
  editingIndex.value = videoList.value.length - 1
  editingVideo.value = { ...newVideo }
}

const toggleEdit = (index) => {
  if (editingIndex.value === index) {
    cancelEdit()
  } else {
    editingIndex.value = index
    editingVideo.value = { ...videoList.value[index] }
  }
}

const saveVideo = async () => {
  if (!videoFormRef.value) return
  
  try {
    const valid = await videoFormRef.value.validate()
    if (!valid) return
    
    videoList.value[editingIndex.value] = { ...editingVideo.value }
    editingIndex.value = -1
    editingVideo.value = {}
    
    ElMessage.success('视频信息已保存')
  } catch (error) {
    console.error('保存视频失败:', error)
  }
}

const cancelEdit = () => {
  editingIndex.value = -1
  editingVideo.value = {}
}

const removeVideo = (index) => {
  videoList.value.splice(index, 1)
  if (editingIndex.value === index) {
    editingIndex.value = -1
    editingVideo.value = {}
  } else if (editingIndex.value > index) {
    editingIndex.value--
  }
  ElMessage.success('视频已删除')
}

const moveUp = (index) => {
  if (index === 0) return
  
  const temp = videoList.value[index]
  videoList.value[index] = videoList.value[index - 1]
  videoList.value[index - 1] = temp
  
  // 更新编辑索引
  if (editingIndex.value === index) {
    editingIndex.value = index - 1
  } else if (editingIndex.value === index - 1) {
    editingIndex.value = index
  }
}

const moveDown = (index) => {
  if (index === videoList.value.length - 1) return
  
  const temp = videoList.value[index]
  videoList.value[index] = videoList.value[index + 1]
  videoList.value[index + 1] = temp
  
  // 更新编辑索引
  if (editingIndex.value === index) {
    editingIndex.value = index + 1
  } else if (editingIndex.value === index + 1) {
    editingIndex.value = index
  }
}

const validateVideoUrl = () => {
  if (!editingVideo.value || !editingVideo.value.url) {
    if (editingVideo.value) {
      editingVideo.value.url_status = 'unknown'
    }
    return
  }
  
  // 简单的URL验证
  try {
    new URL(editingVideo.value.url)
    editingVideo.value.url_status = 'valid'
  } catch {
    editingVideo.value.url_status = 'invalid'
  }
}

const testVideoUrl = async () => {
  if (!editingVideo.value || !editingVideo.value.url) {
    ElMessage.warning('请先输入视频URL')
    return
  }
  
  testingUrl.value = true
  
  try {
    // 简单测试URL的可访问性
    const response = await fetch(editingVideo.value.url, { 
      method: 'HEAD',
      mode: 'no-cors'
    })
    
    editingVideo.value.url_status = 'valid'
    ElMessage.success('URL测试通过')
  } catch (error) {
    editingVideo.value.url_status = 'invalid'
    ElMessage.warning('URL无法访问，请检查地址是否正确')
  } finally {
    testingUrl.value = false
  }
}

const previewVideo = (video) => {
  previewingVideo.value = video
  previewDialogVisible.value = true
}

const handlePreviewClose = () => {
  previewDialogVisible.value = false
  previewingVideo.value = null
}

const getUrlStatusClass = (status) => {
  return {
    'status-valid': status === 'valid',
    'status-invalid': status === 'invalid',
    'status-unknown': status === 'unknown'
  }
}

const getUrlStatusText = (status) => {
  const statusMap = {
    valid: 'URL有效',
    invalid: 'URL无效',
    unknown: '未验证'
  }
  return statusMap[status] || '未知状态'
}

const generateEmbedCode = (url) => {
  if (!url) return null
  
  // B站视频
  if (url.includes('bilibili.com')) {
    const bvMatch = url.match(/BV[\w\d]+/)
    if (bvMatch) {
      return `<iframe src="//player.bilibili.com/player.html?bvid=${bvMatch[0]}" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true" width="100%" height="400"></iframe>`
    }
  }
  
  // YouTube视频
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    let videoId
    if (url.includes('youtu.be/')) {
      videoId = url.split('youtu.be/')[1].split('?')[0]
    } else {
      const match = url.match(/[?&]v=([^&]+)/)
      videoId = match ? match[1] : null
    }
    
    if (videoId) {
      return `<iframe width="100%" height="400" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>`
    }
  }
  
  // 优酷视频
  if (url.includes('youku.com')) {
    // 简单处理，实际可能需要更复杂的解析
    return `<iframe width="100%" height="400" src="${url}" frameborder="0" allowfullscreen></iframe>`
  }
  
  return null
}

// 辅助函数：获取当前简化列表用于比较
const getCurrentSimplifiedList = () => {
  return videoList.value.map(video => ({
    url: video.url,
    title: video.title,
    description: video.description,
    duration: video.duration
  }))
}

// 监听器 - 避免递归更新
watch(() => props.modelValue, (newVal) => {
  if (JSON.stringify(newVal) !== JSON.stringify(getCurrentSimplifiedList())) {
    initializeVideoList()
  }
}, { immediate: true })

watch(videoList, (newList) => {
  // 转换为简化的格式发送给父组件
  const simplified = newList.map(video => ({
    url: video.url,
    title: video.title,
    description: video.description,
    duration: video.duration
  }))
  
  if (JSON.stringify(simplified) !== JSON.stringify(props.modelValue)) {
    emit('update:modelValue', simplified)
  }
}, { deep: true })
</script>

<style scoped>
.video-urls-manager {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.manager-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.manager-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #409eff;
  font-weight: 600;
}

.video-list {
  padding: 20px;
}

.video-item {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-item:last-child {
  margin-bottom: 0;
}

.video-item.video-editing {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.video-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.video-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 2px;
}

.video-url {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

.video-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
}

.video-content {
  padding: 20px;
  background: #fafafa;
}

.form-actions {
  margin-top: 16px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.video-preview {
  padding: 16px;
}

.preview-info {
  margin-bottom: 12px;
}

.preview-description {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.preview-duration {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.url-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  width: fit-content;
}

.status-valid {
  color: #67c23a;
  background: #f0f9ff;
  border: 1px solid #b3e19d;
}

.status-invalid {
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.status-unknown {
  color: #909399;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state p {
  margin: 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: #c0c4cc;
}

/* 视频预览对话框样式 */
.video-preview-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2d3748;
}

.video-embed {
  margin-bottom: 20px;
}

.embed-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.embed-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-link {
  text-align: center;
  padding: 40px;
  background: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.video-link a {
  color: #409eff;
  text-decoration: none;
}

.video-link a:hover {
  text-decoration: underline;
}

.video-metadata {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  font-size: 14px;
}

.video-metadata p {
  margin: 8px 0;
  line-height: 1.5;
}

.video-metadata strong {
  color: #2d3748;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .video-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .video-actions {
    justify-content: center;
  }
  
  .video-info {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>