# Custom Human Muscle Anatomy Components

This directory contains a custom implementation of human muscle anatomy visualization components, forked and enhanced from the original `vue-human-muscle-anatomy` library.

## Features

### 🆕 Enhanced Gender Differentiation
- **Native Hair Features**: Authentic hair SVG paths integrated directly into anatomy data
- **Male Hair**: Shorter, cleaner design for masculine appearance  
- **Female Hair**: Longer, flowing design for feminine appearance
- **No Overlay Issues**: Hair is part of the SVG structure, not a JavaScript overlay

### 🎨 Visual Improvements
- **Thickened Body Outlines**: Enhanced visibility and clarity
- **Dynamic Outline Colors**: Adaptive colors based on background
- **Proper Color Management**: Consistent stroke and fill properties
- **Natural Hair Colors**: Gender-appropriate hair coloring

### 🔧 Technical Enhancements  
- **Vue 3 Composition API**: Modern Vue 3 implementation
- **Full TypeScript Support**: Complete type definitions and safety
- **Modular Architecture**: Separated male/female anatomy data
- **Custom Component**: No external library dependency

## File Structure

```
anatomy/
├── README.md                    # This file
├── LICENSE.md                   # License and attribution
├── types.ts                     # TypeScript interfaces
├── male-anatomy.ts              # Male anatomy SVG data with hair
├── female-anatomy.ts            # Female anatomy SVG data with hair  
├── HumanMuscleAnatomy.vue      # Main Vue component
├── extract-svg-data.js         # Data extraction script
└── extracted-svg-data.json     # Raw extracted data
```

## Usage

```vue
<template>
  <HumanMuscleAnatomy 
    :gender="currentGender"
    :selected-primary-muscle-groups="primaryMuscles"
    :selected-secondary-muscle-groups="secondaryMuscles"
    :primary-highlight-color="primaryColor"
    :secondary-highlight-color="secondaryColor"
    :primary-opacity="0.8"
    :secondary-opacity="0.6"
    default-muscle-color="#f5f5f5"
  />
</template>

<script setup>
import HumanMuscleAnatomy from '@/components/anatomy/HumanMuscleAnatomy.vue'
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `gender` | `'male' \| 'female'` | `'male'` | Gender to display |
| `selectedPrimaryMuscleGroups` | `string[]` | `[]` | Primary highlighted muscles |
| `selectedSecondaryMuscleGroups` | `string[]` | `[]` | Secondary highlighted muscles |
| `defaultMuscleColor` | `string` | `'#1f1f1f'` | Default muscle color |
| `backgroundColor` | `string` | `'#000000'` | Background color |
| `primaryHighlightColor` | `string` | `'#ff0000'` | Primary highlight color |
| `secondaryHighlightColor` | `string` | `'#ff0000'` | Secondary highlight color |
| `primaryOpacity` | `number` | `0.5` | Primary highlight opacity |
| `secondaryOpacity` | `number` | `0.2` | Secondary highlight opacity |

## Muscle Groups

Available muscle group IDs:
- `abs`, `adductors`, `abductors`, `biceps`, `calves`
- `chest`, `forearms`, `frontDelts`, `glutes`, `hamstrings`  
- `lats`, `lowerBack`, `neck`, `obliques`, `quads`
- `rearDelts`, `rotatorCuffs`, `shins`, `sideDelts`
- `traps`, `triceps`

## Gender Differences

### Male Features
- Shorter, cleaner hair design
- Darker brown hair color (`#654321`)
- More angular body outline
- Emphasis on upper body muscle definition

### Female Features  
- Longer, flowing hair design with multiple sections
- Lighter brown hair color (`#8B4513`)
- Softer body outline curves
- Anatomically appropriate proportions

## Development

### Extracting New Data
```bash
cd /path/to/anatomy
node extract-svg-data.js
```

### Adding New Muscle Groups
1. Extract path data from the original library
2. Add to `male-anatomy.ts` and `female-anatomy.ts`
3. Update the `MuscleGroupId` type in `types.ts`
4. Test with the component

## Attribution

Based on [vue-human-muscle-anatomy](https://github.com/lucawahlen/vue-human-muscle-anatomy) by Luca Wahlen.
Enhanced with native hair features and modern Vue 3 implementation.

See [LICENSE.md](./LICENSE.md) for full attribution and license information.