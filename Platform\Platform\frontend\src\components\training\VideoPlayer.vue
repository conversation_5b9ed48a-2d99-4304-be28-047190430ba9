<template>
  <div class="video-player-container">
    <!-- 主视频播放区域 -->
    <div class="main-video-area">
      <div class="video-wrapper">
        <video 
          ref="videoPlayer"
          :src="currentVideoUrl"
          controls
          class="main-video"
          @loadstart="handleLoadStart"
          @canplay="handleCanPlay"
          @error="handleVideoError"
        >
          <source :src="currentVideoUrl" type="video/mp4">
          <source :src="currentVideoUrl" type="video/webm">
          您的浏览器不支持视频播放
        </video>
        
        <!-- 视频加载状态 -->
        <div class="video-loading" v-if="videoLoading && videos && videos.length > 0">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载视频中...</span>
        </div>
        
        <!-- 无视频状态 -->
        <div class="video-empty" v-if="!videos || videos.length === 0">
          <el-icon class="empty-icon"><VideoCamera /></el-icon>
          <span>无视频</span>
        </div>
        
        <!-- 视频错误状态 -->
        <div class="video-error" v-if="videoError">
          <el-icon class="error-icon"><Warning /></el-icon>
          <div class="error-content">
            <h4>视频加载失败</h4>
            <p>{{ videoError }}</p>
            <el-button size="small" @click="retryLoad">重试</el-button>
          </div>
        </div>
      </div>
      
      <!-- 视频信息 -->
      <div class="video-info" v-if="currentVideo">
        <h3 class="video-title">{{ currentVideo.title }}</h3>
        <p class="video-description" v-if="currentVideo.description">
          {{ currentVideo.description }}
        </p>
      </div>
    </div>

    <!-- 视频列表（如果有多个视频） -->
    <div class="video-playlist" v-if="videos.length > 1">
      <h4 class="playlist-title">视频列表</h4>
      <div class="playlist-items">
        <div
          v-for="(video, index) in videos"
          :key="index"
          class="playlist-item"
          :class="{ 
            active: index === currentVideo,
            playing: index === currentVideo && !videoPaused
          }"
          @click="switchVideo(index)"
        >
          <div class="item-thumbnail">
            <!-- 视频缩略图或播放图标 -->
            <div class="thumbnail-placeholder">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
            <div class="play-indicator" v-if="index === currentVideo">
              <el-icon><CaretRight /></el-icon>
            </div>
          </div>
          
          <div class="item-content">
            <div class="item-title">{{ video.title }}</div>
            <div class="item-duration" v-if="video.duration">
              {{ formatDuration(video.duration) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, VideoPlay, CaretRight, VideoCamera } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  videos: {
    type: Array,
    default: () => []
  },
  currentVideo: {
    type: Number,
    default: 0
  },
  autoplay: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['video-change', 'video-error', 'video-ended'])

// 响应式数据
const videoPlayer = ref(null)
const videoLoading = ref(false)
const videoError = ref('')
const videoPaused = ref(true)
const currentVideoIndex = ref(props.currentVideo)

// 计算属性
const currentVideo = computed(() => {
  if (!props.videos || props.videos.length === 0) return null
  return props.videos[currentVideoIndex.value] || props.videos[0]
})

const currentVideoUrl = computed(() => {
  return currentVideo.value?.url || ''
})

// 监听器
watch(() => props.currentVideo, (newIndex) => {
  if (newIndex !== currentVideoIndex.value) {
    currentVideoIndex.value = newIndex
  }
})

watch(() => props.videos, (newVideos) => {
  if (newVideos && newVideos.length > 0) {
    // 重置状态
    videoError.value = ''
    videoLoading.value = false
    
    // 确保当前视频索引有效
    if (currentVideoIndex.value >= newVideos.length) {
      currentVideoIndex.value = 0
    }
  }
}, { immediate: true })

// 方法
const switchVideo = async (index) => {
  if (index === currentVideoIndex.value) return
  
  try {
    currentVideoIndex.value = index
    videoError.value = ''
    videoLoading.value = true
    
    // 通知父组件视频切换
    emit('video-change', index)
    
    // 等待下一个tick确保DOM更新
    await nextTick()
    
    // 如果设置了自动播放，开始播放
    if (props.autoplay && videoPlayer.value) {
      videoPlayer.value.currentTime = 0
      videoPlayer.value.play().catch(err => {
        console.log('自动播放失败:', err)
      })
    }
  } catch (err) {
    console.error('切换视频失败:', err)
    ElMessage.error('切换视频失败')
  }
}

const handleLoadStart = () => {
  videoLoading.value = true
  videoError.value = ''
}

const handleCanPlay = () => {
  videoLoading.value = false
  videoError.value = ''
}

const handleVideoError = (event) => {
  videoLoading.value = false
  
  const video = event.target
  let errorMsg = '视频加载失败'
  
  if (video.error) {
    switch (video.error.code) {
      case video.error.MEDIA_ERR_ABORTED:
        errorMsg = '视频加载被中断'
        break
      case video.error.MEDIA_ERR_NETWORK:
        errorMsg = '网络错误，无法加载视频'
        break
      case video.error.MEDIA_ERR_DECODE:
        errorMsg = '视频解码失败'
        break
      case video.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
        errorMsg = '不支持的视频格式'
        break
      default:
        errorMsg = '未知错误'
    }
  }
  
  videoError.value = errorMsg
  emit('video-error', { index: currentVideoIndex.value, error: errorMsg })
}

const retryLoad = () => {
  if (videoPlayer.value) {
    videoError.value = ''
    videoLoading.value = true
    videoPlayer.value.load()
  }
}

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 监听视频播放状态
const handlePlay = () => {
  videoPaused.value = false
}

const handlePause = () => {
  videoPaused.value = true
}

const handleEnded = () => {
  videoPaused.value = true
  emit('video-ended', currentVideoIndex.value)
}

// 生命周期
onMounted(() => {
  if (videoPlayer.value) {
    videoPlayer.value.addEventListener('play', handlePlay)
    videoPlayer.value.addEventListener('pause', handlePause)
    videoPlayer.value.addEventListener('ended', handleEnded)
  }
})

onUnmounted(() => {
  if (videoPlayer.value) {
    videoPlayer.value.removeEventListener('play', handlePlay)
    videoPlayer.value.removeEventListener('pause', handlePause)
    videoPlayer.value.removeEventListener('ended', handleEnded)
  }
})
</script>

<style scoped>
.video-player-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.main-video-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.video-wrapper {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}

.main-video {
  width: 100%;
  height: auto;
  max-height: 500px;
  display: block;
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 1.5rem;
  border-radius: 8px;
}

.loading-icon {
  font-size: 2rem;
  animation: rotate 2s linear infinite;
}

.video-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: #999;
  background: rgba(0, 0, 0, 0.7);
  padding: 1.5rem;
  border-radius: 8px;
}

.empty-icon {
  font-size: 2rem;
  color: #666;
}

.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
}

.error-icon {
  font-size: 2.5rem;
  color: #f56565;
}

.error-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.error-content p {
  margin: 0 0 1rem 0;
  color: rgba(255, 255, 255, 0.8);
}

.video-info {
  padding: 0 0.5rem;
}

.video-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.video-description {
  margin: 0;
  color: #718096;
  line-height: 1.5;
}

/* 播放列表样式 */
.video-playlist {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.playlist-title {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.playlist-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.playlist-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.playlist-item:hover {
  background: #f0f9ff;
  border-color: #bfdbfe;
}

.playlist-item.active {
  background: #eff6ff;
  border-color: #409EFF;
}

.playlist-item.playing {
  background: #e0f2fe;
  border-color: #22d3ee;
}

.item-thumbnail {
  position: relative;
  flex-shrink: 0;
  width: 60px;
  height: 45px;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #718096;
}

.play-icon {
  font-size: 1.5rem;
}

.play-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #22d3ee;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.item-title {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-duration {
  font-size: 0.85rem;
  color: #718096;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player-container {
    gap: 1rem;
  }
  
  .playlist-item {
    padding: 0.75rem;
  }
  
  .item-thumbnail {
    width: 50px;
    height: 37px;
  }
  
  .video-error {
    padding: 1.5rem;
  }
  
  .video-loading {
    padding: 1rem;
  }
  
  .video-empty {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .video-player-container {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .main-video-area {
    flex: 2;
  }
  
  .video-playlist {
    flex: 1;
    max-width: 350px;
  }
}
</style>