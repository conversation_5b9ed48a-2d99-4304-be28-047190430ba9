<template>
  <div 
    class="metric-card"
    :class="{ 
      'assigned': role !== null,
      'dragging': isDragging,
      'selected': selected,
      'keyboard-focus': focused,
      [`role-${role}`]: role
    }"
    @dragstart="onDragStart"
    @dragend="onDragEnd"
    @click="handleCardClick"
    draggable="true"
    data-testid="metric-card"
  >
    <div class="card-header">
      <div class="metric-info">
        <h5 class="metric-name">{{ metric.display_name || metric.name }}</h5>
        <span class="metric-unit" v-if="metric.unit">{{ metric.unit }}</span>
      </div>
      
      <!-- 角色指示器 -->
      <div class="role-indicator" v-if="role">
        <el-tag 
          :type="roleTagType" 
          size="small"
          effect="plain"
        >
          {{ roleDisplayName }}
        </el-tag>
      </div>
    </div>
    
    <div class="card-body">
      <p class="metric-description" v-if="metric.description">
        {{ metric.description }}
      </p>
      
      <!-- 指标属性 -->
      <div class="metric-attributes">
        <span class="attribute" v-if="metric.category">
          <el-icon><Collection /></el-icon>
          {{ metric.category }}
        </span>
        <span class="attribute" v-if="metric.data_type">
          <el-icon><DataLine /></el-icon>
          {{ dataTypeDisplayName }}
        </span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="card-actions" v-if="!role">
      <el-button-group size="small">
        <el-button 
          type="info"
          @click="$emit('add-to-role', metric.id, 'environment')"
          title="添加为环境指标"
        >
          环境
        </el-button>
        <el-button 
          type="success"
          @click="$emit('add-to-role', metric.id, 'measurement')"
          title="添加为度量指标"
        >
          度量
        </el-button>
        <el-button 
          type="warning"
          @click="$emit('add-to-role', metric.id, 'variation')"
          title="添加为变化指标"
        >
          变化
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 已分配时的操作 -->
    <div class="assigned-actions" v-if="role">
      <el-dropdown trigger="click" @command="onActionCommand">
        <el-button 
          type="text" 
          size="small"
          :icon="MoreFilled"
        />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item 
              v-if="role !== 'environment'"
              command="change-to-environment"
            >
              转为环境指标
            </el-dropdown-item>
            <el-dropdown-item 
              v-if="role !== 'measurement'"
              command="change-to-measurement"
            >
              转为度量指标
            </el-dropdown-item>
            <el-dropdown-item 
              v-if="role !== 'variation'"
              command="change-to-variation"
            >
              转为变化指标
            </el-dropdown-item>
            <el-dropdown-item 
              command="remove"
              divided
            >
              <span style="color: #f56c6c;">移除</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 拖拽提示 -->
    <div class="drag-hint" v-if="!role">
      <el-icon><Rank /></el-icon>
      <span>拖拽分配</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Collection, DataLine, MoreFilled, Rank } from '@element-plus/icons-vue'
import type { Metric, MetricRole } from '@/types'

interface Props {
  metric: Metric
  role: MetricRole | null
  selected?: boolean
  focused?: boolean
}

interface Emits {
  (e: 'role-change', metricId: number, fromRole: MetricRole | null, toRole: MetricRole): void
  (e: 'remove', metricId: number, role: MetricRole): void
  (e: 'add-to-role', metricId: number, role: MetricRole): void
  (e: 'selection-change', metricId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isDragging = ref(false)

const roleTagType = computed(() => {
  switch (props.role) {
    case 'environment':
      return 'info'
    case 'measurement':
      return 'success'
    case 'variation':
      return 'warning'
    default:
      return ''
  }
})

const roleDisplayName = computed(() => {
  switch (props.role) {
    case 'environment':
      return '环境'
    case 'measurement':
      return '度量'
    case 'variation':
      return '变化'
    default:
      return ''
  }
})

const dataTypeDisplayName = computed(() => {
  switch (props.metric.data_type) {
    case 'numeric':
      return '数值'
    case 'text':
      return '文本'
    case 'boolean':
      return '布尔'
    case 'datetime':
      return '时间'
    default:
      return props.metric.data_type || '未知'
  }
})

const onDragStart = (event: DragEvent) => {
  isDragging.value = true
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('application/json', JSON.stringify({
      metricId: props.metric.id,
      currentRole: props.role
    }))
  }
}

const onDragEnd = () => {
  isDragging.value = false
}

const onActionCommand = (command: string) => {
  switch (command) {
    case 'change-to-environment':
      emit('role-change', props.metric.id, props.role, 'environment')
      break
    case 'change-to-measurement':
      emit('role-change', props.metric.id, props.role, 'measurement')
      break
    case 'change-to-variation':
      emit('role-change', props.metric.id, props.role, 'variation')
      break
    case 'remove':
      if (props.role) {
        emit('remove', props.metric.id, props.role)
      }
      break
  }
}

const handleCardClick = (event: MouseEvent) => {
  // Only handle selection for unassigned metrics
  if (!props.role && event.target && !(event.target as HTMLElement).closest('.card-actions')) {
    emit('selection-change', props.metric.id)
  }
}
</script>

<style scoped>
.metric-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  cursor: grab;
  transition: all 0.3s;
  position: relative;
  min-height: 100px;
}

.metric-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.metric-card.dragging {
  opacity: 0.6;
  transform: rotate(5deg);
  cursor: grabbing;
}

.metric-card.assigned {
  border-width: 2px;
}

.metric-card.role-environment {
  border-color: #17a2b8;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
}

.metric-card.role-measurement {
  border-color: #28a745;
  background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
}

.metric-card.role-variation {
  border-color: #ffc107;
  background: linear-gradient(135deg, #ffffff 0%, #fffaf0 100%);
}

.metric-card.selected {
  border-color: #409eff;
  background: linear-gradient(135deg, #ffffff 0%, #ecf5ff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.metric-card:not(.role):hover {
  cursor: pointer;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.metric-info {
  flex: 1;
  min-width: 0;
}

.metric-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  word-wrap: break-word;
}

.metric-unit {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.role-indicator {
  margin-left: 8px;
  flex-shrink: 0;
}

.card-body {
  margin-bottom: 12px;
}

.metric-description {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.metric-attributes {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.attribute {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.attribute .el-icon {
  font-size: 12px;
}

.card-actions {
  margin-top: 8px;
}

.card-actions .el-button-group {
  width: 100%;
}

.card-actions .el-button {
  flex: 1;
  font-size: 11px;
}

.assigned-actions {
  position: absolute;
  top: 8px;
  right: 8px;
}

.drag-hint {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #c0c4cc;
  opacity: 0;
  transition: opacity 0.3s;
}

.metric-card:hover .drag-hint {
  opacity: 1;
}

.drag-hint .el-icon {
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-actions .el-button {
    font-size: 10px;
    padding: 4px 8px;
  }
  
  .metric-name {
    font-size: 13px;
  }
  
  .metric-description {
    font-size: 11px;
  }
}
</style>