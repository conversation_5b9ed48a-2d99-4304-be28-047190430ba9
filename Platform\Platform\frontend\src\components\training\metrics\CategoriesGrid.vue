<template>
  <div class="categories-section">
    <div class="categories-header">
      <h3 class="categories-title">📊 指标分类</h3>
      <el-button
        size="small"
        class="manage-categories-btn"
        @click="handleManageCategories"
      >
        管理分类
      </el-button>
    </div>
    
    <div class="categories-grid">
      <div
        v-for="category in rootCategories"
        :key="category.id"
        class="category-card"
        :class="{ 'active': selectedCategoryId == category.id }"
        @click="handleSelectCategory(category.id)"
      >
        <div class="category-content">
          <div class="category-icon">
            <el-icon :size="20">
              <Folder />
            </el-icon>
          </div>
          <div class="category-info">
            <div class="category-name">{{ category.name }}</div>
            <div class="category-stats">📈 {{ getCategoryMetricsCount(category) }} 个指标</div>
            <div v-if="getChildCategories(category.id).length > 0" class="sub-categories">
              <span class="sub-categories-label">子分类：</span>
              <el-tag
                v-for="child in getChildCategories(category.id)"
                :key="child.id"
                size="small"
                type="info"
                class="sub-category-tag"
              >
                {{ child.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import { Folder } from '@element-plus/icons-vue'

const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategoryId: [String, Number],
  metrics: {
    type: Array,
    required: true
  }
})

const emit = defineEmits([
  'selectCategory',
  'manageCategories'
])

const { categories, selectedCategoryId, metrics } = toRefs(props)

// 只显示一级分类
const rootCategories = computed(() => {
  return categories.value.filter(cat => cat.level === 1 || !cat.level)
})

// 获取子分类
const getChildCategories = (parentId) => {
  return categories.value.filter(cat => cat.parent_id === parentId)
}

const getCategoryMetricsCount = (category) => {
  // 优先使用后端返回的metric_count，如果没有则回退到前端计算
  if (category && typeof category.metric_count === 'number') {
    return category.metric_count
  }
  // 回退方案：前端计算（兼容旧API）
  return metrics.value.filter(metric => metric.category_id === category.id).length
}

const handleSelectCategory = (categoryId) => {
  emit('selectCategory', categoryId)
}

const handleManageCategories = () => {
  emit('manageCategories')
}
</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';

/* 分类区域整体样式 */
.categories-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

/* 分类区域头部 */
.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid #f5f5f5;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
}

.categories-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.manage-categories-btn {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  color: #495057 !important;
  font-size: 13px !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  
  &:hover {
    background: #e9ecef !important;
    border-color: #dee2e6 !important;
    color: #343a40 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* 分类网格布局 */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 20px 24px 24px 24px;
  background: #fafbfc;
}

/* 分类卡片样式 */
.category-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 10px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }
  
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
    
    &::before {
      transform: scaleX(1);
    }
    
    .category-icon {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      color: white;
      transform: scale(1.1);
    }
    
    .category-name {
      color: #1e40af;
    }
  }
  
  &.active {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
    
    &::before {
      transform: scaleX(1);
    }
    
    .category-icon {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6);
      color: white;
    }
    
    .category-name {
      color: #1e40af;
      font-weight: 600;
    }
  }
}

/* 分类卡片内容布局 */
.category-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 分类图标样式 */
.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

/* 分类信息区域 */
.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 4px 0;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.category-stats {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sub-categories {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 6px;

  .sub-categories-label {
    font-size: 12px;
    color: #9ca3af;
    margin-right: 2px;
  }

  .sub-category-tag {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: #f8fafc;
    border-color: #e2e8f0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .categories-grid {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }
  
  .categories-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .categories-title {
    text-align: center;
  }
  
  .category-card {
    padding: 16px;
  }
  
  .category-content {
    gap: 12px;
  }
  
  .category-icon {
    width: 40px;
    height: 40px;
  }
  
  .category-name {
    font-size: 15px;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .categories-section {
    background: #1f2937;
    border-color: #374151;
  }
  
  .categories-header {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-bottom-color: #374151;
  }
  
  .categories-title {
    color: #f9fafb;
  }
  
  .categories-grid {
    background: #111827;
  }
  
  .category-card {
    background: #1f2937;
    border-color: #374151;
    
    &:hover {
      border-color: #60a5fa;
      box-shadow: 0 8px 25px rgba(96, 165, 250, 0.15);
    }
    
    &.active {
      background: linear-gradient(135deg, #1e3a8a 0%, #1f2937 100%);
    }
  }
  
  .category-icon {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
  }
  
  .category-name {
    color: #f3f4f6;
  }
  
  .category-stats {
    color: #9ca3af;
  }
}
</style>