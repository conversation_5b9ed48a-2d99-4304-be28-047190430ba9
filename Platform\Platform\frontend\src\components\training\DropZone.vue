<template>
  <div 
    class="drop-zone enhanced-drop-zone"
    :class="{ 
      'drag-over': isDragOver, 
      'has-items': items.length > 0,
      'empty': items.length === 0,
      'magnetic': isMagnetic
    }"
    @dragover.prevent="handleDragOver"
    @dragleave="handleDragLeave"
    @drop.prevent="handleDrop"
  >
    <div class="drop-zone-header">
      <h4>{{ title }}</h4>
      <span class="item-count">{{ items.length }} 项</span>
    </div>
    
    <div class="drop-zone-content">
      <div v-if="items.length === 0" class="empty-state zone-hint">
        <div class="hint-icon">🎯</div>
        <p>{{ emptyText || '拖拽指标到此区域' }}</p>
      </div>
      
      <div v-else class="items-list">
        <div 
          v-for="(item, index) in items"
          :key="item.id"
          class="dropped-item metric-card assigned-metric"
          :class="{ 'dragging': item.isDragging }"
          draggable="true"
          @dragstart="handleItemDragStart(item, index, $event)"
          @dragend="handleItemDragEnd(item, index, $event)"
        >
          <div class="item-content">
            <el-icon class="drag-handle"><Rank /></el-icon>
            <span class="item-name">{{ item.metric_name || item.name }}</span>
            
            <div class="item-actions">
              <el-button 
                size="small" 
                type="danger" 
                text
                @click="removeItem(index)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Plus, Rank, Close } from '@element-plus/icons-vue'

interface Props {
  title: string
  items: any[]
  emptyText?: string
  acceptTypes?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  emptyText: '拖拽指标到此区域',
  acceptTypes: () => ['metric']
})

const emit = defineEmits<{
  'drop': [item: any, targetZone: string]
  'remove': [index: number]
  'reorder': [fromIndex: number, toIndex: number]
}>()

const isDragOver = ref(false)
const isMagnetic = ref(false)

const handleDragOver = (event: DragEvent) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (data) {
    try {
      const dragData = JSON.parse(data)
      if (props.acceptTypes.includes(dragData.type)) {
        isDragOver.value = true
        event.dataTransfer!.dropEffect = 'move'
        
        // 磁性吸附效果 - 当拖拽元素靠近边界时触发
        const rect = (event.currentTarget as Element).getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2
        const distance = Math.sqrt(
          Math.pow(event.clientX - centerX, 2) + 
          Math.pow(event.clientY - centerY, 2)
        )
        
        // 如果距离中心很近，启用磁性效果
        if (distance < 100) {
          isMagnetic.value = true
        } else {
          isMagnetic.value = false
        }
      }
    } catch (e) {
      // 忽略无效数据
    }
  }
}

const handleDragLeave = (event: DragEvent) => {
  // 只有当真正离开组件时才设置为false
  const rect = (event.currentTarget as Element).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
    isMagnetic.value = false
  }
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  isMagnetic.value = false
  
  const data = event.dataTransfer?.getData('text/plain')
  if (data) {
    try {
      const dragData = JSON.parse(data)
      if (props.acceptTypes.includes(dragData.type)) {
        emit('drop', dragData.data, props.title)
        
        // 添加成功反馈动画
        nextTick(() => {
          const element = event.currentTarget as HTMLElement
          element.style.animation = 'drop-success 0.6s ease-out'
          setTimeout(() => {
            element.style.animation = ''
          }, 600)
        })
      }
    } catch (e) {
      console.error('Invalid drag data:', e)
    }
  }
}

const handleItemDragStart = (item: any, index: number, event: DragEvent) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify({
    type: 'reorder',
    sourceZone: props.title,
    sourceIndex: index,
    data: item
  }))
  
  item.isDragging = true
}

const handleItemDragEnd = (item: any, index: number, event: DragEvent) => {
  item.isDragging = false
}

const removeItem = (index: number) => {
  emit('remove', index)
}
</script>

<style scoped lang="scss">
.drop-zone {
  border: 2px dashed #DCDFE6;
  border-radius: 8px;
  padding: 16px;
  min-height: 120px;
  transition: all 0.3s;
  background-color: #FAFAFA;
  
  &.drag-over {
    border-color: #409EFF;
    background-color: #ECF5FF;
    
    .empty-state {
      color: #409EFF;
    }
  }
  
  &.has-items {
    border-style: solid;
    border-color: #E4E7ED;
    background-color: #fff;
  }
  
  .drop-zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    h4 {
      margin: 0;
      color: #303133;
      font-size: 14px;
    }
    
    .item-count {
      font-size: 12px;
      color: #909399;
      background-color: #F0F2F5;
      padding: 2px 8px;
      border-radius: 12px;
    }
  }
  
  .drop-zone-content {
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 80px;
      color: #C0C4CC;
      
      .el-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
    
    .items-list {
      .dropped-item {
        background-color: #F8F9FA;
        border: 1px solid #E9ECEF;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: all 0.3s;
        cursor: move;
        
        &:hover {
          background-color: #E9ECEF;
          border-color: #DEE2E6;
        }
        
        &.dragging {
          opacity: 0.6;
          transform: rotate(1deg);
        }
        
        .item-content {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          
          .drag-handle {
            color: #6C757D;
            cursor: grab;
            
            &:active {
              cursor: grabbing;
            }
          }
          
          .item-name {
            flex: 1;
            font-size: 14px;
            color: #495057;
          }
          
          .item-actions {
            opacity: 0.6;
            transition: opacity 0.3s;
            
            &:hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}

// 成功放置动画
@keyframes drop-success {
  0% { 
    transform: scale(1); 
    background-color: inherit;
  }
  50% { 
    transform: scale(1.05); 
    background-color: var(--sports-energy, rgba(51, 181, 229, 0.2));
  }
  100% { 
    transform: scale(1); 
    background-color: inherit;
  }
}
</style>