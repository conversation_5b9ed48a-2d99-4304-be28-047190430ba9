<template>
  <div class="muscle-diagram-container">
    <div class="diagram-header">
      <h4>{{ exerciseName }} - 肌肉群激活</h4>
      <div class="diagram-legend">
        <div class="legend-item">
          <div class="legend-color primary"></div>
          <span>主要肌群</span>
        </div>
        <div class="legend-item">
          <div class="legend-color secondary"></div>
          <span>辅助肌群</span>
        </div>
        <div class="legend-item">
          <div class="legend-color stabilizer"></div>
          <span>稳定肌群</span>
        </div>
      </div>
    </div>

    <div class="diagrams-container">
      <!-- 前视图 -->
      <div class="diagram-section">
        <h5>前视图</h5>
        <div class="muscle-diagram front-view">
          <svg viewBox="0 0 300 600" class="body-svg">
            <!-- 改进的人体轮廓 -->
            <g class="body-outline-group">
              <!-- 头部 -->
              <ellipse cx="150" cy="60" rx="35" ry="45" fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 躯干 -->
              <path d="M115 105 
                      C115 105, 120 110, 125 130
                      L125 180
                      C125 200, 120 220, 115 240
                      L115 300
                      C115 320, 120 340, 125 360
                      L125 420
                      C125 440, 130 460, 135 480
                      L140 520
                      C140 530, 145 535, 150 535
                      C155 535, 160 530, 160 520
                      L165 480
                      C170 460, 175 440, 175 420
                      L175 360
                      C180 340, 185 320, 185 300
                      L185 240
                      C180 220, 175 200, 175 180
                      L175 130
                      C180 110, 185 105, 185 105
                      C185 105, 175 100, 150 100
                      C125 100, 115 105, 115 105 Z" 
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 手臂 -->
              <path d="M115 130 C100 125, 85 130, 80 145 L75 180 C75 200, 80 220, 85 240 L90 260 C95 265, 100 268, 105 270"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              <path d="M185 130 C200 125, 215 130, 220 145 L225 180 C225 200, 220 220, 215 240 L210 260 C205 265, 200 268, 195 270"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 腿部 -->
              <path d="M135 420 C130 440, 125 460, 125 480 L125 520 C125 540, 130 560, 135 575"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              <path d="M165 420 C170 440, 175 460, 175 480 L175 520 C175 540, 170 560, 165 575"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
            </g>
            
            <!-- 改进的肌肉群定义 -->
            <!-- 胸大肌 -->
            <path d="M125 130 C140 125, 160 125, 175 130 L175 170 C170 180, 160 185, 150 185 C140 185, 130 180, 125 170 Z"
                  :class="getMuscleClass('chest')"
                  class="muscle chest"
                  @mouseenter="showMuscleInfo('chest', '胸大肌')"
                  @mouseleave="hideMuscleInfo"/>
            
            <!-- 三角肌 (肩膀) -->
            <ellipse cx="110" cy="135" rx="15" ry="25" 
                    :class="getMuscleClass('shoulders')"
                    class="muscle shoulders"
                    @mouseenter="showMuscleInfo('shoulders', '三角肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="190" cy="135" rx="15" ry="25" 
                    :class="getMuscleClass('shoulders')"
                    class="muscle shoulders"
                    @mouseenter="showMuscleInfo('shoulders', '三角肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 肱二头肌 -->
            <ellipse cx="95" cy="180" rx="12" ry="30" 
                    :class="getMuscleClass('biceps')"
                    class="muscle biceps"
                    @mouseenter="showMuscleInfo('biceps', '肱二头肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="205" cy="180" rx="12" ry="30" 
                    :class="getMuscleClass('biceps')"
                    class="muscle biceps"
                    @mouseenter="showMuscleInfo('biceps', '肱二头肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 腹直肌 -->
            <rect x="135" y="190" width="30" height="80" rx="5"
                  :class="getMuscleClass('abs')"
                  class="muscle abs"
                  @mouseenter="showMuscleInfo('abs', '腹直肌')"
                  @mouseleave="hideMuscleInfo"/>
            
            <!-- 股四头肌 -->
            <ellipse cx="135" cy="350" rx="18" ry="60" 
                    :class="getMuscleClass('quadriceps')"
                    class="muscle quadriceps"
                    @mouseenter="showMuscleInfo('quadriceps', '股四头肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="165" cy="350" rx="18" ry="60" 
                    :class="getMuscleClass('quadriceps')"
                    class="muscle quadriceps"
                    @mouseenter="showMuscleInfo('quadriceps', '股四头肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 小腿三头肌 -->
            <ellipse cx="135" cy="480" rx="15" ry="45" 
                    :class="getMuscleClass('calves')"
                    class="muscle calves"
                    @mouseenter="showMuscleInfo('calves', '小腿三头肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="165" cy="480" rx="15" ry="45" 
                    :class="getMuscleClass('calves')"
                    class="muscle calves"
                    @mouseenter="showMuscleInfo('calves', '小腿三头肌')"
                    @mouseleave="hideMuscleInfo"/>
          </svg>
          
          <!-- 肌肉信息提示 -->
          <div 
            class="muscle-tooltip" 
            v-show="tooltipVisible"
            :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
          >
            {{ tooltipText }}
          </div>
        </div>
      </div>

      <!-- 后视图 -->
      <div class="diagram-section">
        <h5>后视图</h5>
        <div class="muscle-diagram back-view">
          <svg viewBox="0 0 300 600" class="body-svg">
            <!-- 后视图人体轮廓 -->
            <g class="body-outline-group">
              <!-- 头部 -->
              <ellipse cx="150" cy="60" rx="35" ry="45" fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 躯干背面 -->
              <path d="M115 105 
                      C115 105, 120 110, 125 130
                      L125 180
                      C125 200, 120 220, 115 240
                      L115 300
                      C115 320, 120 340, 125 360
                      L125 420
                      C125 440, 130 460, 135 480
                      L140 520
                      C140 530, 145 535, 150 535
                      C155 535, 160 530, 160 520
                      L165 480
                      C170 460, 175 440, 175 420
                      L175 360
                      C180 340, 185 320, 185 300
                      L185 240
                      C180 220, 175 200, 175 180
                      L175 130
                      C180 110, 185 105, 185 105
                      C185 105, 175 100, 150 100
                      C125 100, 115 105, 115 105 Z" 
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 手臂背面 -->
              <path d="M115 130 C100 125, 85 130, 80 145 L75 180 C75 200, 80 220, 85 240 L90 260 C95 265, 100 268, 105 270"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              <path d="M185 130 C200 125, 215 130, 220 145 L225 180 C225 200, 220 220, 215 240 L210 260 C205 265, 200 268, 195 270"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              
              <!-- 腿部背面 -->
              <path d="M135 420 C130 440, 125 460, 125 480 L125 520 C125 540, 130 560, 135 575"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
              <path d="M165 420 C170 440, 175 460, 175 480 L175 520 C175 540, 170 560, 165 575"
                    fill="none" stroke="#cbd5e0" stroke-width="2"/>
            </g>
            
            <!-- 背部肌肉群 -->
            <!-- 背阔肌 -->
            <path d="M125 130 C140 135, 160 135, 175 130 L185 200 C180 220, 170 230, 150 235 C130 230, 120 220, 115 200 Z"
                  :class="getMuscleClass('back')"
                  class="muscle back"
                  @mouseenter="showMuscleInfo('back', '背阔肌')"
                  @mouseleave="hideMuscleInfo"/>
            
            <!-- 肱三头肌 -->
            <ellipse cx="95" cy="180" rx="12" ry="30" 
                    :class="getMuscleClass('triceps')"
                    class="muscle triceps"
                    @mouseenter="showMuscleInfo('triceps', '肱三头肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="205" cy="180" rx="12" ry="30" 
                    :class="getMuscleClass('triceps')"
                    class="muscle triceps"
                    @mouseenter="showMuscleInfo('triceps', '肱三头肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 臀大肌 -->
            <ellipse cx="135" cy="290" rx="20" ry="30" 
                    :class="getMuscleClass('glutes')"
                    class="muscle glutes"
                    @mouseenter="showMuscleInfo('glutes', '臀大肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="165" cy="290" rx="20" ry="30" 
                    :class="getMuscleClass('glutes')"
                    class="muscle glutes"
                    @mouseenter="showMuscleInfo('glutes', '臀大肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 腘绳肌 -->
            <ellipse cx="135" cy="360" rx="18" ry="50" 
                    :class="getMuscleClass('hamstrings')"
                    class="muscle hamstrings"
                    @mouseenter="showMuscleInfo('hamstrings', '腘绳肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="165" cy="360" rx="18" ry="50" 
                    :class="getMuscleClass('hamstrings')"
                    class="muscle hamstrings"
                    @mouseenter="showMuscleInfo('hamstrings', '腘绳肌')"
                    @mouseleave="hideMuscleInfo"/>
            
            <!-- 小腿后侧 -->
            <ellipse cx="135" cy="480" rx="15" ry="45" 
                    :class="getMuscleClass('calves')"
                    class="muscle calves-back"
                    @mouseenter="showMuscleInfo('calves', '小腿三头肌')"
                    @mouseleave="hideMuscleInfo"/>
            <ellipse cx="165" cy="480" rx="15" ry="45" 
                    :class="getMuscleClass('calves')"
                    class="muscle calves-back"
                    @mouseenter="showMuscleInfo('calves', '小腿三头肌')"
                    @mouseleave="hideMuscleInfo"/>
          </svg>
          
          <!-- 肌肉信息提示 -->
          <div 
            class="muscle-tooltip" 
            v-show="tooltipVisible"
            :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
          >
            {{ tooltipText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 激活肌肉列表 -->
    <div class="activated-muscles" v-if="activatedMusclesList.length > 0">
      <h5>激活的肌肉群</h5>
      <div class="muscles-grid">
        <div 
          v-for="muscle in activatedMusclesList"
          :key="muscle.name"
          class="muscle-item"
          :class="muscle.type"
        >
          <div class="muscle-indicator" :class="muscle.type"></div>
          <div class="muscle-content">
            <span class="muscle-name">{{ muscle.displayName }}</span>
            <span class="muscle-type">{{ getMuscleTypeLabel(muscle.type) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'

// Props
const props = defineProps({
  muscleGroups: {
    type: Object,
    default: () => ({})
  },
  exerciseName: {
    type: String,
    default: '动作'
  }
})

// 响应式数据
const tooltipVisible = ref(false)
const tooltipText = ref('')
const tooltipX = ref(0)
const tooltipY = ref(0)

// 注释：现在使用改进的SVG形状，不再需要复杂的路径定义

// 肌肉群映射
const muscleMapping = {
  // 英文到中文的映射
  chest: '胸肌',
  shoulders: '肩膀',
  biceps: '肱二头肌',
  triceps: '肱三头肌',
  abs: '腹肌',
  back: '背肌',
  glutes: '臀大肌',
  quadriceps: '股四头肌',
  hamstrings: '腘绳肌',
  calves: '小腿',
  core: '核心',
  // 中文键值
  '胸肌': 'chest',
  '肩膀': 'shoulders',
  '肱二头肌': 'biceps', 
  '肱三头肌': 'triceps',
  '腹肌': 'abs',
  '背肌': 'back',
  '臀大肌': 'glutes',
  '股四头肌': 'quadriceps',
  '腘绳肌': 'hamstrings',
  '小腿': 'calves',
  '核心': 'core'
}

// 计算属性
const activatedMusclesList = computed(() => {
  const result = []
  const muscles = props.muscleGroups || {}
  
  // 处理主要肌群
  if (muscles.primary && Array.isArray(muscles.primary)) {
    muscles.primary.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscleMapping[muscle] || muscle,
        type: 'primary'
      })
    })
  }
  
  // 处理辅助肌群
  if (muscles.secondary && Array.isArray(muscles.secondary)) {
    muscles.secondary.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscleMapping[muscle] || muscle,
        type: 'secondary'
      })
    })
  }
  
  // 处理稳定肌群
  if (muscles.stabilizer && Array.isArray(muscles.stabilizer)) {
    muscles.stabilizer.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscleMapping[muscle] || muscle,
        type: 'stabilizer'
      })
    })
  }
  
  return result
})

// 方法
const getMuscleClass = (muscleName) => {
  const muscles = props.muscleGroups || {}
  
  // 检查是否为主要肌群
  if (muscles.primary && muscles.primary.includes(muscleName)) {
    return 'activated primary'
  }
  
  // 检查是否为辅助肌群
  if (muscles.secondary && muscles.secondary.includes(muscleName)) {
    return 'activated secondary'
  }
  
  // 检查是否为稳定肌群
  if (muscles.stabilizer && muscles.stabilizer.includes(muscleName)) {
    return 'activated stabilizer'
  }
  
  return 'inactive'
}

const getMuscleTypeLabel = (type) => {
  const typeMap = {
    primary: '主要',
    secondary: '辅助',
    stabilizer: '稳定'
  }
  return typeMap[type] || type
}

const showMuscleInfo = (muscleName, displayName) => {
  tooltipText.value = displayName
  tooltipVisible.value = true
  
  // 获取鼠标位置
  window.addEventListener('mousemove', updateTooltipPosition)
}

const hideMuscleInfo = () => {
  tooltipVisible.value = false
  window.removeEventListener('mousemove', updateTooltipPosition)
}

const updateTooltipPosition = (event) => {
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 10
}
</script>

<style scoped>
.muscle-diagram-container {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
}

.diagram-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.diagram-header h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.diagram-legend {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #718096;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.legend-color.primary {
  background-color: #dc2626;
}

.legend-color.secondary {
  background-color: #f59e0b;
}

.legend-color.stabilizer {
  background-color: #10b981;
}

.diagrams-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .diagrams-container {
    grid-template-columns: 1fr;
  }
}

.diagram-section h5 {
  margin: 0 0 1rem 0;
  text-align: center;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 500;
}

.muscle-diagram {
  position: relative;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  justify-content: center;
}

.body-svg {
  width: 100%;
  max-width: 200px;
  height: auto;
}

.muscle {
  cursor: pointer;
  transition: all 0.3s ease;
  stroke: #e2e8f0;
  stroke-width: 1;
}

.muscle.inactive {
  fill: #f1f5f9;
  opacity: 0.7;
}

.muscle.activated {
  opacity: 1;
}

.muscle.activated.primary {
  fill: #dc2626;
}

.muscle.activated.secondary {
  fill: #f59e0b;
}

.muscle.activated.stabilizer {
  fill: #10b981;
}

.muscle:hover {
  opacity: 0.8;
  stroke: #4a5568;
  stroke-width: 2;
}

.muscle-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.85rem;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

.activated-muscles {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.activated-muscles h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
}

.muscles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.muscle-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid transparent;
}

.muscle-item.primary {
  border-left-color: #dc2626;
  background: #fef2f2;
}

.muscle-item.secondary {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.muscle-item.stabilizer {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.muscle-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.muscle-indicator.primary {
  background-color: #dc2626;
}

.muscle-indicator.secondary {
  background-color: #f59e0b;
}

.muscle-indicator.stabilizer {
  background-color: #10b981;
}

.muscle-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.muscle-name {
  font-weight: 500;
  color: #2d3748;
}

.muscle-type {
  font-size: 0.75rem;
  color: #718096;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diagram-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .diagram-legend {
    width: 100%;
    justify-content: space-around;
  }
  
  .diagrams-container {
    grid-template-columns: 1fr;
  }
  
  .muscles-grid {
    grid-template-columns: 1fr;
  }
}
</style>