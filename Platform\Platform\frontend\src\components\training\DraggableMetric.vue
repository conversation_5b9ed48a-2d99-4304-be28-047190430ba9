<template>
  <div 
    class="draggable-metric metric-card metric-item"
    :class="{ 'dragging': isDragging, 'selected': isSelected }"
    draggable="true"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @click="handleClick"
  >
    <div class="metric-content">
      <el-checkbox 
        :model-value="isSelected"
        @change="$emit('toggle', metric)"
      />
      
      <div class="metric-info">
        <div class="metric-name">
          {{ metric.metric_name }}
          <el-tag 
            v-if="metric.is_neutral" 
            type="warning" 
            size="small"
          >
            中性
          </el-tag>
          <el-tag 
            v-if="metric.is_required" 
            type="danger" 
            size="small"
          >
            必填
          </el-tag>
        </div>
        <div class="metric-desc">
          {{ metric.unit ? `单位: ${metric.unit}` : '' }}
          {{ metric.description }}
        </div>
      </div>
      
      <div class="metric-actions">
        <el-rate 
          :model-value="metric.usage_frequency" 
          :max="3" 
          disabled 
          size="small"
        />
        <el-icon class="drag-handle"><Rank /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Rank } from '@element-plus/icons-vue'

interface Props {
  metric: any
  isSelected: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'toggle': [metric: any]
  'dragstart': [metric: any, event: DragEvent]
  'dragend': [metric: any, event: DragEvent]
}>()

const isDragging = ref(false)

const handleDragStart = (event: DragEvent) => {
  isDragging.value = true
  
  // 设置拖拽数据
  event.dataTransfer?.setData('text/plain', JSON.stringify({
    id: props.metric.id,
    type: 'metric',
    data: props.metric
  }))
  
  // 设置拖拽效果
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
  }
  
  emit('dragstart', props.metric, event)
}

const handleDragEnd = (event: DragEvent) => {
  isDragging.value = false
  emit('dragend', props.metric, event)
}

const handleClick = () => {
  emit('toggle', props.metric)
}
</script>

<style scoped lang="scss">
.draggable-metric {
  border: 1px solid #EBEEF5;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
  
  &:hover {
    border-color: #C0C4CC;
    background-color: #F5F7FA;
    
    .drag-handle {
      opacity: 1;
    }
  }
  
  &.selected {
    border-color: #409EFF;
    background-color: #ECF5FF;
  }
  
  &.dragging {
    opacity: 0.6;
    transform: rotate(2deg);
  }
  
  .metric-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    
    .metric-info {
      flex: 1;
      
      .metric-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 6px;
      }
      
      .metric-desc {
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
    }
    
    .metric-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .drag-handle {
        opacity: 0.3;
        transition: opacity 0.3s;
        cursor: grab;
        
        &:active {
          cursor: grabbing;
        }
      }
    }
  }
}
</style>