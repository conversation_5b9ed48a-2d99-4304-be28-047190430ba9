<!--
  时间指标状态显示组件
  Time Metrics Status Display Component
  用于显示时间指标的当前状态（显示/隐藏）
-->

<template>
  <div class="time-metrics-status-display" data-testid="metrics-status-display">
    <div class="status-container">
      <!-- 图标 -->
      <div class="status-icon" :class="{ active: showTimeMetrics }">
        <el-icon>
          <Clock v-if="showTimeMetrics" />
          <Hide v-else />
        </el-icon>
      </div>
      
      <!-- 状态文本 -->
      <div class="status-content">
        <div class="status-text">{{ statusText }}</div>
        <div class="status-subtitle" v-if="timeMetricsCount > 0">
          {{ showTimeMetrics ? '在指标选择中显示时间相关指标' : '时间指标已隐藏，不影响已选择的指标' }}
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="{ active: showTimeMetrics }">
        <span class="indicator-dot"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Clock, Hide } from '@element-plus/icons-vue'
import { useTimeMetricsToggle } from '@/composables/useTimeMetricsToggle'

interface Props {
  // 所有指标列表
  allMetrics?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  allMetrics: () => []
})

// 使用时间指标开关状态
const {
  showTimeMetrics,
  getTimeMetricsCount,
  getTimeMetricsStatus
} = useTimeMetricsToggle()

// 计算时间指标数量
const timeMetricsCount = computed(() => getTimeMetricsCount(props.allMetrics))

// 计算状态文本（响应式）
const statusText = computed(() => {
  // 明确依赖showTimeMetrics.value来触发重新计算
  const isTimeMetricsEnabled = showTimeMetrics.value
  const result = getTimeMetricsStatus(props.allMetrics)
  console.log(`📊 TimeMetricsStatusDisplay: showTimeMetrics=${isTimeMetricsEnabled}, statusText="${result}"`)
  return result
})
</script>

<style scoped>
.time-metrics-status-display {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.time-metrics-status-display:hover {
  background: var(--el-bg-color);
  border-color: var(--el-color-primary-light-7);
}

.status-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
  font-size: 14px;
  transition: all 0.3s ease;
}

.status-icon.active {
  background: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.status-content {
  flex: 1;
  min-width: 0;
}

.status-text {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  line-height: 1.2;
}

.status-subtitle {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
  margin-top: 2px;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--el-color-info-light-3);
  transition: all 0.3s ease;
}

.status-indicator.active .indicator-dot {
  background: var(--el-color-success);
  box-shadow: 0 0 8px var(--el-color-success-light-5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-metrics-status-display {
    padding: 6px 8px;
  }
  
  .status-container {
    gap: 8px;
  }
  
  .status-icon {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }
  
  .status-text {
    font-size: 12px;
  }
  
  .status-subtitle {
    font-size: 10px;
    display: none; /* 移动端隐藏副标题 */
  }
  
  .indicator-dot {
    width: 6px;
    height: 6px;
  }
}

/* 主题适配 */
.athletics-orange-theme .time-metrics-status-display {
  border-color: var(--orange-border, #f4a261);
}

.athletics-orange-theme .time-metrics-status-display:hover {
  border-color: var(--orange-primary, #e76f51);
  background: var(--orange-bg-light, #fef7f0);
}

.athletics-orange-theme .status-icon.active {
  background: var(--orange-success-bg, #e8f5e8);
  color: var(--orange-success, #52c41a);
}

.athletics-orange-theme .status-indicator.active .indicator-dot {
  background: var(--orange-success, #52c41a);
  box-shadow: 0 0 8px var(--orange-success-light, #b7eb8f);
}
</style>