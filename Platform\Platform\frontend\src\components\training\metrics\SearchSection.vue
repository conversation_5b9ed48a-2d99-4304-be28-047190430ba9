<template>
  <div class="search-section">
    <div class="search-content">
      <div class="search-input-wrapper">
        <el-input
          v-model="searchQuery"
          placeholder="搜索指标名称、描述..."
          :style="{ width: '100%' }"
          @input="handleSearchInput"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="search-actions">
        <el-select
          v-model="selectedCategory"
          placeholder="选择分类"
          :style="{ width: '150px' }"
          @change="handleCategoryChange"
          class="category-select"
        >
          <el-option
            v-for="option in categoryOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-select
          v-model="selectedStatus"
          placeholder="状态"
          :style="{ width: '120px' }"
          @change="handleStatusChange"
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-select
          v-model="selectedDataType"
          placeholder="数据类型"
          :style="{ width: '120px' }"
          @change="handleDataTypeChange"
        >
          <el-option value="" label="全部类型" />
          <el-option
            v-for="option in dataTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-button
          class="search-btn secondary"
          @click="handleClearFilters"
        >
          <el-icon><refresh /></el-icon>
          清空筛选
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import { Refresh, Search } from '@element-plus/icons-vue'

const props = defineProps({
  searchQuery: String,
  selectedCategory: String,
  selectedStatus: String,
  selectedDataType: String,
  categoryOptions: Array,
  statusOptions: Array,
  dataTypeOptions: Array
})

const emit = defineEmits([
  'update:searchQuery',
  'update:selectedCategory', 
  'update:selectedStatus',
  'update:selectedDataType',
  'applyFilters',
  'clearFilters'
])

const { 
  searchQuery,
  selectedCategory,
  selectedStatus,
  selectedDataType,
  categoryOptions,
  statusOptions,
  dataTypeOptions
} = toRefs(props)

const handleSearchInput = (value) => {
  emit('update:searchQuery', value)
  emit('applyFilters')
}

const handleCategoryChange = (value) => {
  emit('update:selectedCategory', value)
  emit('applyFilters')
}

const handleStatusChange = (value) => {
  emit('update:selectedStatus', value)
  emit('applyFilters')
}

const handleDataTypeChange = (value) => {
  emit('update:selectedDataType', value)
  emit('applyFilters')
}

const handleClearFilters = () => {
  emit('clearFilters')
}
</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';

/* 修复 Element Plus 选择器下拉选项的灰色字体问题 */
:deep(.el-select-dropdown) {
  .el-option {
    color: #000000 !important;
    
    &:hover {
      color: #ffffff !important;
      background-color: #409eff !important;
    }
    
    &.selected {
      color: #409eff !important;
      font-weight: 600 !important;
    }
    
    &.is-disabled {
      color: #c0c4cc !important;
    }
  }
  
  .el-option__text {
    color: inherit !important;
  }
}

/* 全局修复所有 Element Plus 选择器的下拉选项文字颜色 */
</style>

<style lang="scss">
/* 全局样式：修复所有页面中 Element Plus 选择器下拉选项的文字颜色问题 */
.el-select-dropdown {
  .el-option {
    color: #000000 !important;
    
    &:hover {
      color: #ffffff !important;
      background-color: #409eff !important;
    }
    
    &.selected {
      color: #409eff !important;
      font-weight: 600 !important;
    }
    
    &.is-disabled {
      color: #c0c4cc !important;
    }
  }
  
  .el-option__text {
    color: inherit !important;
  }
}

/* 修复选择器输入框中的占位符和选中文字颜色 */
.el-select {
  .el-input__inner {
    color: #000000 !important;
    
    &::placeholder {
      color: #606266 !important;  /* 更深的灰色，提高可读性 */
    }
  }
  
  .el-input__wrapper {
    .el-input__inner {
      color: #000000 !important;
    }
  }
}
</style>