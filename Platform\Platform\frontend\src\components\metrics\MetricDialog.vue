<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="680px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="指标代码" prop="metric_code">
            <el-input 
              v-model="form.name" 
              placeholder="请输入指标代码"
              :disabled="mode === 'edit'"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="指标名称" prop="display_name">
            <el-input 
              v-model="form.display_name" 
              placeholder="请输入指标名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="指标分类" prop="category_id">
            <el-select 
              v-model="form.category_id" 
              placeholder="请选择分类"
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="数据类型" prop="data_type">
            <el-select 
              v-model="form.data_type" 
              placeholder="请选择数据类型"
              style="width: 100%"
            >
              <el-option label="整数" value="integer" />
              <el-option label="小数" value="decimal" />
              <el-option label="文本" value="text" />
              <el-option label="枚举" value="enum" />
              <el-option label="日期" value="date" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input 
              v-model="form.unit" 
              placeholder="如：kg、次、分钟等"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="使用频率" prop="usage_frequency">
            <el-rate 
              v-model="form.usage_frequency" 
              :max="3" 
              size="large"
              :texts="['低频', '中频', '高频']"
              show-text
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="指标描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请描述该指标的用途和含义"
        />
      </el-form-item>
      
      <!-- 指标角色配置 -->
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        指标角色配置
      </el-divider>
      
      <el-form-item label="指标角色" prop="metric_role">
        <el-radio-group v-model="form.metric_role">
          <el-space direction="vertical" size="small">
            <el-radio value="normal">
              <div class="role-option">
                <div class="option-title">
                  <el-icon><Document /></el-icon>
                  普通指标
                </div>
                <div class="option-desc">标准的训练指标，用于记录具体数值</div>
              </div>
            </el-radio>
            
            <el-radio value="neutral">
              <div class="role-option">
                <div class="option-title">
                  <el-icon><Switch /></el-icon>
                  基准指标
                </div>
                <div class="option-desc">作为分类或基准的抽象指标，不记录具体数值</div>
              </div>
            </el-radio>
            
            <el-radio value="target">
              <div class="role-option">
                <div class="option-title">
                  <el-icon><Aim /></el-icon>
                  目标指标
                </div>
                <div class="option-desc">记录训练目标值</div>
              </div>
            </el-radio>
            
            <el-radio value="actual">
              <div class="role-option">
                <div class="option-title">
                  <el-icon><Check /></el-icon>
                  实际指标
                </div>
                <div class="option-desc">记录实际完成值</div>
              </div>
            </el-radio>
          </el-space>
        </el-radio-group>
      </el-form-item>
      
      <!-- 枚举值配置 -->
      <el-form-item 
        v-if="form.data_type === 'enum'" 
        label="枚举选项"
        prop="enum_values"
      >
        <div class="enum-config">
          <div 
            v-for="(value, index) in enumOptions" 
            :key="index"
            class="enum-item"
          >
            <el-input 
              v-model="enumOptions[index]" 
              placeholder="选项值"
              style="width: 200px"
            />
            <el-button 
              type="danger" 
              size="small" 
              @click="removeEnumOption(index)"
              :disabled="enumOptions.length <= 1"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          
          <el-button 
            type="primary" 
            size="small" 
            @click="addEnumOption"
          >
            <el-icon><Plus /></el-icon>
            添加选项
          </el-button>
        </div>
      </el-form-item>
      
      <!-- 验证规则配置 -->
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        验证规则
      </el-divider>
      
      <el-row :gutter="20" v-if="form.data_type === 'integer' || form.data_type === 'decimal'">
        <el-col :span="12">
          <el-form-item label="最小值" prop="min_value">
            <el-input-number 
              v-model="form.min_value" 
              :precision="form.data_type === 'decimal' ? 2 : 0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="最大值" prop="max_value">
            <el-input-number 
              v-model="form.max_value" 
              :precision="form.data_type === 'decimal' ? 2 : 0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="默认值" prop="default_value">
        <el-input 
          v-model="form.default_value" 
          placeholder="可选，设置指标的默认值"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否必填">
            <el-switch 
              v-model="form.is_required" 
              active-text="必填"
              inactive-text="可选"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="是否启用">
            <el-switch 
              v-model="form.is_active" 
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer v-if="mode !== 'view'">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Switch, Document, Delete, Plus, Setting, Aim, Check } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { Metric, MetricCategory } from '@/types/metrics'

interface Props {
  visible: boolean
  metric: Metric | null
  categories: MetricCategory[]
  mode: 'view' | 'edit' | 'create'
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data: Metric]
}>()

const formRef = ref<FormInstance>()
const enumOptions = ref<string[]>([''])

const form = reactive<Partial<Metric>>({
  metric_code: '',
  metric_name: '',
  category_id: undefined,
  data_type: 'text',
  unit: '',
  description: '',
  metric_role: 'normal',
  is_required: false,
  usage_frequency: 1,
  min_value: undefined,
  max_value: undefined,
  default_value: '',
  is_active: true,
  enum_values: []
})

const rules: FormRules = {
  metric_code: [
    { required: true, message: '请输入指标代码', trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9_]*$/, message: '代码必须以小写字母开头，只能包含小写字母、数字和下划线', trigger: 'blur' }
  ],
  metric_name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择指标分类', trigger: 'change' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  metric_role: [
    { required: true, message: '请选择指标角色', trigger: 'change' }
  ]
}

const dialogTitle = computed(() => {
  const titles = {
    view: '查看指标',
    edit: '编辑指标',
    create: '创建指标'
  }
  return titles[props.mode]
})



const addEnumOption = () => {
  enumOptions.value.push('')
}

const removeEnumOption = (index: number) => {
  enumOptions.value.splice(index, 1)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 处理枚举值
    if (form.data_type === 'enum') {
      form.enum_values = enumOptions.value.filter(v => v.trim())
    }
    
    emit('submit', form as Metric)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

// 监听弹窗打开，初始化表单数据
watch(() => props.visible, (visible) => {
  if (visible && props.metric) {
    Object.assign(form, props.metric)
    
    // 初始化枚举选项
    if (props.metric.enum_values && Array.isArray(props.metric.enum_values)) {
      enumOptions.value = [...props.metric.enum_values]
    } else {
      enumOptions.value = ['']
    }
  } else if (visible && props.mode === 'create') {
    // 重置表单
    Object.assign(form, {
      metric_code: '',
      metric_name: '',
      category_id: undefined,
      data_type: 'text',
      unit: '',
      description: '',
      metric_role: 'normal',
      is_required: false,
      usage_frequency: 1,
      min_value: undefined,
      max_value: undefined,
      default_value: '',
      is_active: true,
      enum_values: []
    })
    enumOptions.value = ['']
  }
})
</script>

<style scoped lang="scss">
.help-text {
  margin-top: 10px;
  
  :deep(.el-alert) {
    border-radius: 6px;
  }
}

.role-option {
  .option-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #303133;
  }
  
  .option-desc {
    color: #909399;
    font-size: 13px;
    margin-top: 4px;
    margin-left: 16px;
  }
}

.enum-config {
  .enum-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
}

</style>