<template>
  <div class="metric-role-assigner">
    <div class="assigner-header">
      <h3>指标角色配置</h3>
      <p class="help-text">将指标拖拽到对应的角色区域，或使用切换按钮分配角色</p>
    </div>
    
    <div class="role-sections">
      <!-- 环境指标区域 -->
      <div class="role-section environment">
        <div class="section-header">
          <h4>环境指标</h4>
          <el-tag size="small" type="info">{{ environmentMetrics.length }}</el-tag>
        </div>
        <div class="section-help">
          <span>描述训练环境条件的指标</span>
        </div>
        <draggable
          v-model="environmentMetrics"
          group="metrics"
          v-bind="dragOptions"
          class="drop-zone"
          :class="{ 
            'drag-over': dragState.environment,
            'show-drop-zone': showDropZones 
          }"
          @start="onDragStart"
          @end="onDragEnd"
          @change="(event) => onMetricMoved(event, 'environment')"
        >
          <template #item="{ element }">
            <metric-card 
              :key="element.id" 
              :metric="element" 
              :role="'environment'"
              @role-change="changeMetricRole"
              @remove="removeMetricFromRole"
            />
          </template>
        </draggable>
        <div v-if="environmentMetrics.length === 0" class="empty-zone">
          <el-icon><Plus /></el-icon>
          <span>拖拽环境指标到此区域</span>
        </div>
      </div>
      
      <!-- 度量指标区域 -->
      <div class="role-section measurement">
        <div class="section-header">
          <h4>度量指标</h4>
          <el-tag size="small" type="success">{{ measurementMetrics.length }}</el-tag>
          <span class="required-badge" v-if="measurementMetrics.length === 0">必需</span>
        </div>
        <div class="section-help">
          <span>用于量化训练表现的核心指标</span>
        </div>
        <draggable
          v-model="measurementMetrics"
          group="metrics"
          v-bind="dragOptions"
          class="drop-zone"
          :class="{ 
            'drag-over': dragState.measurement, 
            'required': measurementMetrics.length === 0,
            'show-drop-zone': showDropZones 
          }"
          @start="onDragStart"
          @end="onDragEnd"
          @change="(event) => onMetricMoved(event, 'measurement')"
        >
          <template #item="{ element }">
            <metric-card 
              :key="element.id" 
              :metric="element" 
              :role="'measurement'"
              @role-change="changeMetricRole"
              @remove="removeMetricFromRole"
            />
          </template>
        </draggable>
        <div v-if="measurementMetrics.length === 0" class="empty-zone required">
          <el-icon><Plus /></el-icon>
          <span>至少拖拽一个度量指标到此区域</span>
        </div>
      </div>
      
      <!-- 变化指标区域 -->
      <div class="role-section variation">
        <div class="section-header">
          <h4>变化指标</h4>
          <el-tag size="small" type="warning">{{ variationMetrics.length }}</el-tag>
        </div>
        <div class="section-help">
          <span>跟踪训练过程中的变化趋势</span>
        </div>
        <draggable
          v-model="variationMetrics"
          group="metrics"
          v-bind="dragOptions"
          class="drop-zone"
          :class="{ 
            'drag-over': dragState.variation,
            'show-drop-zone': showDropZones 
          }"
          @start="onDragStart"
          @end="onDragEnd"
          @change="(event) => onMetricMoved(event, 'variation')"
        >
          <template #item="{ element }">
            <metric-card 
              :key="element.id" 
              :metric="element" 
              :role="'variation'"
              @role-change="changeMetricRole"
              @remove="removeMetricFromRole"
            />
          </template>
        </draggable>
        <div v-if="variationMetrics.length === 0" class="empty-zone">
          <el-icon><Plus /></el-icon>
          <span>拖拽变化指标到此区域</span>
        </div>
      </div>
    </div>
    
    <!-- 可用指标池 -->
    <div class="available-metrics-pool">
      <div class="pool-header">
        <h4>可用指标</h4>
        <div class="pool-controls">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索指标..."
            size="small"
            prefix-icon="Search"
            clearable
            style="width: 200px"
          />
          <el-select
            v-model="categoryFilter"
            placeholder="筛选分类"
            size="small"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="category in metricCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </div>
      </div>
      
      <!-- 键盘导航提示 -->
      <div class="keyboard-navigation-hint" v-if="keyboardNavigationActive || selectedMetrics.length > 0">
        <div class="keyboard-shortcuts">
          <div class="shortcut-item">
            <span class="shortcut-key">↑↓</span>
            <span>导航</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Space</span>
            <span>选择</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">1</span>
            <span>环境</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">2</span>
            <span>度量</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">3</span>
            <span>变化</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Esc</span>
            <span>清除</span>
          </div>
        </div>
      </div>
      
      <!-- 批量操作控件 -->
      <div class="batch-operations" v-if="filteredUnassignedMetrics.length > 0">
        <div class="batch-selection">
          <el-checkbox
            :model-value="selectedMetrics.length === filteredUnassignedMetrics.length && filteredUnassignedMetrics.length > 0"
            :indeterminate="selectedMetrics.length > 0 && selectedMetrics.length < filteredUnassignedMetrics.length"
            @change="handleSelectAll"
          >
            全选 ({{ selectedMetrics.length }}/{{ filteredUnassignedMetrics.length }})
          </el-checkbox>
          
          <el-button
            v-if="selectedMetrics.length > 0"
            size="small"
            @click="batchOperations.clearSelection"
          >
            清除选择
          </el-button>
        </div>
        
        <div class="batch-actions" v-if="selectedMetrics.length > 0">
          <span class="batch-label">批量分配到:</span>
          <el-button-group size="small">
            <el-button 
              type="info"
              :loading="isBatchLoading"
              :disabled="isBatchLoading"
              @click="batchOperations.assignToRole('environment')"
            >
              环境 ({{ selectedMetrics.length }})
            </el-button>
            <el-button 
              type="success"
              :loading="isBatchLoading"
              :disabled="isBatchLoading"
              @click="batchOperations.assignToRole('measurement')"
            >
              度量 ({{ selectedMetrics.length }})
            </el-button>
            <el-button 
              type="warning"
              :loading="isBatchLoading"
              :disabled="isBatchLoading"
              @click="batchOperations.assignToRole('variation')"
            >
              变化 ({{ selectedMetrics.length }})
            </el-button>
          </el-button-group>
        </div>
      </div>
      
      <div class="metrics-grid">
        <draggable
          v-model="filteredUnassignedMetrics"
          group="metrics"
          v-bind="dragOptions"
          class="metrics-list"
          @start="onDragStart"
          @end="onDragEnd"
        >
          <template #item="{ element, index }">
            <metric-card 
              :key="element.id" 
              :metric="element" 
              :role="null"
              :selected="selectedMetrics.includes(element.id)"
              :focused="keyboardNavigationActive && currentFocusIndex === index"
              @role-change="changeMetricRole"
              @add-to-role="addMetricToRole"
              @selection-change="toggleMetricSelection"
            />
          </template>
        </draggable>
      </div>
    </div>
    
    <!-- 验证状态 -->
    <div class="validation-status" v-if="validationErrors.length > 0">
      <el-alert
        type="warning"
        show-icon
        :closable="false"
      >
        <template #title>
          <span>配置验证</span>
        </template>
        <ul class="error-list">
          <li v-for="error in validationErrors" :key="error">{{ error }}</li>
        </ul>
      </el-alert>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="resetAssignments" :disabled="!hasAssignments">
        重置分配
      </el-button>
      <el-button 
        type="primary" 
        @click="confirmAssignments"
        :disabled="!isValidConfiguration"
      >
        确认分配 ({{ totalAssignedMetrics }})
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { Plus, Search, QuestionFilled } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import MetricCard from './MetricCard.vue'
import { useTemplateBuilderStore } from '@/stores/templateBuilder'
import type { Metric, MetricRole } from '@/types'

interface Props {
  availableMetrics: Metric[]
  initialAssignments?: Record<number, MetricRole>
}

interface Emits {
  (e: 'assignments-changed', assignments: Record<number, MetricRole>): void
  (e: 'validation-changed', isValid: boolean, errors: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const templateBuilderStore = useTemplateBuilderStore()

// 搜索和筛选
const searchKeyword = ref('')
const categoryFilter = ref('')

// 拖拽状态
const dragState = reactive({
  environment: false,
  measurement: false,
  variation: false
})

// 拖拽视觉反馈状态
const showDropZones = ref(false)
const selectedMetrics = ref<number[]>([])

// 键盘导航状态
const currentFocusIndex = ref(-1)
const keyboardNavigationActive = ref(false)

// 指标分组
const environmentMetrics = ref<Metric[]>([])
const measurementMetrics = ref<Metric[]>([])
const variationMetrics = ref<Metric[]>([])

// 计算属性
const assignedMetricIds = computed(() => {
  const ids = new Set<number>()
  environmentMetrics.value.forEach(m => ids.add(m.id))
  measurementMetrics.value.forEach(m => ids.add(m.id))
  variationMetrics.value.forEach(m => ids.add(m.id))
  return ids
})

const unassignedMetrics = computed(() => {
  return props.availableMetrics.filter(metric => !assignedMetricIds.value.has(metric.id))
})

const filteredUnassignedMetrics = computed(() => {
  let metrics = unassignedMetrics.value
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    metrics = metrics.filter(metric => 
      metric.name.toLowerCase().includes(keyword) ||
      metric.display_name?.toLowerCase().includes(keyword) ||
      metric.description?.toLowerCase().includes(keyword)
    )
  }
  
  // 分类筛选
  if (categoryFilter.value) {
    metrics = metrics.filter(metric => metric.category === categoryFilter.value)
  }
  
  return metrics
})

const metricCategories = computed(() => {
  const categories = new Set<string>()
  props.availableMetrics.forEach(metric => {
    if (metric.category) {
      categories.add(metric.category)
    }
  })
  return Array.from(categories)
})

const totalAssignedMetrics = computed(() => {
  return environmentMetrics.value.length + measurementMetrics.value.length + variationMetrics.value.length
})

const hasAssignments = computed(() => totalAssignedMetrics.value > 0)

const validationErrors = computed(() => {
  const errors: string[] = []
  
  if (measurementMetrics.value.length === 0) {
    errors.push('度量指标区至少需要一个指标才能继续')
  }
  
  return errors
})

const isValidConfiguration = computed(() => validationErrors.value.length === 0)

const currentAssignments = computed(() => {
  const assignments: Record<number, MetricRole> = {}
  
  environmentMetrics.value.forEach(metric => {
    assignments[metric.id] = 'environment'
  })
  
  measurementMetrics.value.forEach(metric => {
    assignments[metric.id] = 'measurement'
  })
  
  variationMetrics.value.forEach(metric => {
    assignments[metric.id] = 'variation'
  })
  
  return assignments
})

// 拖拽配置选项
const dragOptions = {
  animation: 200,
  ghostClass: 'ghost-card',
  dragClass: 'dragging-card', 
  chosenClass: 'chosen-card',
  onStart: (evt: any) => {
    document.body.classList.add('is-dragging')
    showDropZones.value = true
  },
  onEnd: (evt: any) => {
    document.body.classList.remove('is-dragging')
    showDropZones.value = false
  }
}

// 批量操作状态
const isBatchLoading = ref(false)

// 批量操作功能
const batchOperations = {
  selectAll: () => {
    selectedMetrics.value = filteredUnassignedMetrics.value.map(m => m.id)
  },
  clearSelection: () => {
    selectedMetrics.value = []
  },
  assignToRole: async (role: MetricRole) => {
    // 添加空值检查
    if (!selectedMetrics.value || selectedMetrics.value.length === 0) {
      templateBuilderStore.showNotification('请先选择要分配的指标', 'warning')
      return
    }
    
    // 添加loading状态
    isBatchLoading.value = true
    const errors: string[] = []
    
    try {
      // 使用Promise.allSettled处理部分失败
      const results = await Promise.allSettled(
        selectedMetrics.value.map(metricId => {
          return new Promise<void>((resolve, reject) => {
            try {
              addMetricToRole(metricId, role)
              resolve()
            } catch (error) {
              reject(error)
            }
          })
        })
      )
      
      // 统计结果
      const succeeded = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      
      if (succeeded > 0) {
        templateBuilderStore.showNotification(`成功分配${succeeded}个指标到${role}角色`, 'success')
      }
      if (failed > 0) {
        templateBuilderStore.showNotification(`${failed}个指标分配失败`, 'error')
      }
      
      // 清理选中状态
      selectedMetrics.value = []
      
    } catch (error) {
      console.error('批量分配错误:', error)
      templateBuilderStore.showNotification('批量分配失败，请重试', 'error')
    } finally {
      isBatchLoading.value = false
    }
  }
}

// 方法
const onDragStart = () => {
  // 使用新的dragOptions配置
}

const onDragEnd = () => {
  dragState.environment = false
  dragState.measurement = false
  dragState.variation = false
}

const onMetricMoved = (event: any, role: MetricRole) => {
  // 处理指标移动事件
  emitChanges()
}

const changeMetricRole = (metricId: number, fromRole: MetricRole | null, toRole: MetricRole) => {
  const metric = findMetricById(metricId)
  if (!metric) return
  
  // 从原角色移除
  if (fromRole) {
    removeMetricFromRoleArray(metric, fromRole)
  }
  
  // 添加到新角色
  addMetricToRoleArray(metric, toRole)
  
  emitChanges()
}

const addMetricToRole = (metricId: number, role: MetricRole) => {
  const metric = findMetricById(metricId)
  if (!metric) return
  
  addMetricToRoleArray(metric, role)
  emitChanges()
}

const removeMetricFromRole = (metricId: number, role: MetricRole) => {
  const metric = findMetricById(metricId)
  if (!metric) return
  
  removeMetricFromRoleArray(metric, role)
  emitChanges()
}

const addMetricToRoleArray = (metric: Metric, role: MetricRole) => {
  switch (role) {
    case 'environment':
      if (!environmentMetrics.value.find(m => m.id === metric.id)) {
        environmentMetrics.value.push(metric)
      }
      break
    case 'measurement':
      if (!measurementMetrics.value.find(m => m.id === metric.id)) {
        measurementMetrics.value.push(metric)
      }
      break
    case 'variation':
      if (!variationMetrics.value.find(m => m.id === metric.id)) {
        variationMetrics.value.push(metric)
      }
      break
  }
}

const removeMetricFromRoleArray = (metric: Metric, role: MetricRole) => {
  switch (role) {
    case 'environment':
      const envIndex = environmentMetrics.value.findIndex(m => m.id === metric.id)
      if (envIndex !== -1) {
        environmentMetrics.value.splice(envIndex, 1)
      }
      break
    case 'measurement':
      const measIndex = measurementMetrics.value.findIndex(m => m.id === metric.id)
      if (measIndex !== -1) {
        measurementMetrics.value.splice(measIndex, 1)
      }
      break
    case 'variation':
      const varIndex = variationMetrics.value.findIndex(m => m.id === metric.id)
      if (varIndex !== -1) {
        variationMetrics.value.splice(varIndex, 1)
      }
      break
  }
}

const findMetricById = (id: number): Metric | undefined => {
  return props.availableMetrics.find(metric => metric.id === id)
}

const resetAssignments = () => {
  environmentMetrics.value = []
  measurementMetrics.value = []
  variationMetrics.value = []
  emitChanges()
}

const confirmAssignments = () => {
  if (isValidConfiguration.value) {
    templateBuilderStore.setMetricAssignments(currentAssignments.value)
    emit('assignments-changed', currentAssignments.value)
  }
}

const emitChanges = () => {
  emit('assignments-changed', currentAssignments.value)
  emit('validation-changed', isValidConfiguration.value, validationErrors.value)
}

// 批量选择处理
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedMetrics.value = filteredUnassignedMetrics.value.map(m => m.id)
  } else {
    selectedMetrics.value = []
  }
}

const toggleMetricSelection = (metricId: number) => {
  const index = selectedMetrics.value.indexOf(metricId)
  if (index === -1) {
    selectedMetrics.value.push(metricId)
  } else {
    selectedMetrics.value.splice(index, 1)
  }
}

// 键盘导航功能
const handleKeyDown = (event: KeyboardEvent) => {
  if (filteredUnassignedMetrics.value.length === 0) return
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      keyboardNavigationActive.value = true
      currentFocusIndex.value = Math.min(
        currentFocusIndex.value + 1,
        filteredUnassignedMetrics.value.length - 1
      )
      break
      
    case 'ArrowUp':
      event.preventDefault()
      keyboardNavigationActive.value = true
      currentFocusIndex.value = Math.max(currentFocusIndex.value - 1, 0)
      break
      
    case 'ArrowRight':
    case 'ArrowLeft':
      event.preventDefault()
      // Navigate between role sections when focused metric is assigned
      break
      
    case ' ':
    case 'Space':
      event.preventDefault()
      if (currentFocusIndex.value >= 0) {
        const metric = filteredUnassignedMetrics.value[currentFocusIndex.value]
        toggleMetricSelection(metric.id)
      }
      break
      
    case 'Enter':
      event.preventDefault()
      if (currentFocusIndex.value >= 0 && selectedMetrics.value.length > 0) {
        // Show role assignment options
        showRoleAssignmentMenu.value = true
      }
      break
      
    case 'Escape':
      event.preventDefault()
      selectedMetrics.value = []
      currentFocusIndex.value = -1
      keyboardNavigationActive.value = false
      break
      
    case '1':
      event.preventDefault()
      if (selectedMetrics.value.length > 0) {
        batchOperations.assignToRole('environment')
      }
      break
      
    case '2':
      event.preventDefault()
      if (selectedMetrics.value.length > 0) {
        batchOperations.assignToRole('measurement')
      }
      break
      
    case '3':
      event.preventDefault()
      if (selectedMetrics.value.length > 0) {
        batchOperations.assignToRole('variation')
      }
      break
  }
}

const showRoleAssignmentMenu = ref(false)

// 初始化
const initializeAssignments = () => {
  if (props.initialAssignments) {
    Object.entries(props.initialAssignments).forEach(([metricIdStr, role]) => {
      const metricId = parseInt(metricIdStr)
      const metric = findMetricById(metricId)
      if (metric) {
        addMetricToRoleArray(metric, role)
      }
    })
  }
}

// 监听器
watch(() => props.initialAssignments, initializeAssignments, { immediate: true })

watch([environmentMetrics, measurementMetrics, variationMetrics], () => {
  emitChanges()
}, { deep: true })

onMounted(() => {
  initializeAssignments()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.metric-role-assigner {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.assigner-header {
  margin-bottom: 24px;
}

.assigner-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.help-text {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.role-sections {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.role-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  min-height: 300px;
}

.role-section.environment {
  border-left: 4px solid #17a2b8;
}

.role-section.measurement {
  border-left: 4px solid #28a745;
}

.role-section.variation {
  border-left: 4px solid #ffc107;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.required-badge {
  background: #f56565;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.section-help {
  margin-bottom: 16px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.drop-zone {
  min-height: 200px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  padding: 8px;
  transition: all 0.3s;
}

.drop-zone.drag-over {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.drop-zone.required {
  border-color: #f56565;
}

.empty-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #c0c4cc;
  font-size: 14px;
}

.empty-zone.required {
  color: #f56565;
}

.empty-zone .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.available-metrics-pool {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.pool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.pool-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.pool-controls {
  display: flex;
  gap: 12px;
}

.metrics-grid {
  min-height: 120px;
}

.metrics-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.validation-status {
  margin-bottom: 24px;
}

.error-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 4px;
  color: #e6a23c;
}

.drop-zone.show-drop-zone {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-style: solid;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(64, 158, 255, 0.1); }
  100% { box-shadow: 0 0 0 0 rgba(64, 158, 255, 0); }
}

/* 拖拽状态样式 */
:global(.ghost-card) {
  opacity: 0.5;
  background: #c8ebfb;
  border: 2px dashed #409eff;
}

:global(.dragging-card) {
  transform: rotate(5deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

:global(.chosen-card) {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

:global(.is-dragging) {
  cursor: grabbing !important;
}

:global(.is-dragging *) {
  cursor: grabbing !important;
}

/* 键盘导航样式 */
.metric-card.keyboard-focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

.keyboard-navigation-hint {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #1e40af;
}

.keyboard-shortcuts {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.shortcut-key {
  background: #e5e7eb;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
  font-weight: 600;
}

/* 批量操作样式 */
.batch-operations {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.batch-selection {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.batch-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.batch-actions .el-button-group .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 1200px) {
  .role-sections {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .metrics-list {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>