import request from '@/utils/request'

// ============ 动作相关 API ============

/**
 * 获取动作列表（基于模板数据）
 * @param {Object} params - 查询参数
 * @param {number} params.offset - 偏移量
 * @param {number} params.limit - 限制记录数
 * @param {string} params.search - 动作名称搜索
 * @param {string} params.category - 动作分类
 * @param {string} params.difficulty - 难度等级
 * @param {string} params.visibility - 可见性
 */
export function getExercises(params) {
  return request({
    url: '/api/v1/exercises',
    method: 'get',
    params
  }).then(response => {
    // 新的 exercises API 直接返回正确格式的数据，无需转换
    return response
  })
}

/**
 * 获取动作模板列表（用于构建训练日）
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制记录数，默认100
 * @param {string} params.search - 模板名称搜索
 * @param {string} params.category - 模板分类
 * @param {boolean} params.is_active - 是否激活，默认true
 */
export function getExerciseTemplates(params = {}) {
  const defaultParams = {
    limit: 100,
    is_active: true,
    ...params
  }
  
  return request({
    url: '/api/v1/templates',
    method: 'get',
    params: defaultParams
  }).then(response => {
    // 转换为训练日构建器所需的格式，兼容新的training_config结构
    if (response.data && Array.isArray(response.data)) {
      response.data = response.data.map(template => {
        // 从新的training_config结构中提取参数
        const structureParams = template.training_config?.structure_params || {}
        
        return {
          id: template.id,
          exercise_name: template.exercise_name,
          description: template.description,
          target_params: {
            sets: structureParams.sets || template.default_sets || 3,
            reps: structureParams.reps_per_set || template.default_reps || 10,
            weight: template.default_weight || 0,
            rest_time: template.default_rest_time || 90
          },
          measurement_granularity: structureParams.granularity || template.measurement_granularity || 'set',
          training_config: template.training_config, // 保留完整的配置结构
          metrics_assignment: template.metrics_assignment, // 保留指标分配
          category: template.category,
          difficulty_level: template.difficulty_level,
          target_muscles: template.target_muscles || [],
          equipment_required: template.equipment_required || [],
          is_compound: template.is_compound || false,
          estimated_duration: template.estimated_duration || 300, // 默认5分钟
          visibility: template.visibility,
          applicable_specialties: template.applicable_specialties,
          age_groups: template.age_groups,
          gender: template.gender
        }
      })
    }
    return response
  })
}

/**
 * 获取单个动作详情（基于模板数据）
 * @param {number} id - 动作ID
 */
export function getExercise(id) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'get'
  }).then(response => {
    // 新的 exercises API 直接返回正确格式的数据，无需转换
    return response
  })
}

/**
 * 获取动作完整详情（增强版）
 * @param {number} id - 动作ID
 */
export function getExerciseDetail(id) {
  return request({
    url: `/api/v1/exercises/${id}/detail`,
    method: 'get'
  })
}

/**
 * 获取动作相关的训练模板
 * @param {number} exerciseId - 动作ID
 * @param {number} limit - 限制数量
 */
export function getExerciseTemplatesByExercise(exerciseId, limit = 10) {
  return request({
    url: `/api/v1/exercises/${exerciseId}/templates`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取动作评论列表
 * @param {number} exerciseId - 动作ID
 * @param {number} limit - 限制数量
 */
export function getExerciseComments(exerciseId, limit = 20) {
  return request({
    url: `/api/v1/exercises/${exerciseId}/comments`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 添加动作评论
 * @param {number} exerciseId - 动作ID
 * @param {Object} commentData - 评论数据
 * @param {string} commentData.user_name - 用户名
 * @param {string} commentData.content - 评论内容
 * @param {number} commentData.rating - 评分1-5
 * @param {number} commentData.user_id - 用户ID(可选)
 */
export function addExerciseComment(exerciseId, commentData) {
  return request({
    url: `/api/v1/exercises/${exerciseId}/comments`,
    method: 'post',
    data: commentData
  })
}

/**
 * 创建新动作
 * @param {Object} data - 动作数据
 */
export function createExercise(data) {
  return request({
    url: '/api/v1/exercises',
    method: 'post',
    data: data
  }).then(response => {
    // 新的 exercises API 直接返回正确格式的数据，无需转换
    return response
  })
}

/**
 * 更新动作
 * @param {number} id - 动作ID
 * @param {Object} data - 更新数据
 */
export function updateExercise(id, data) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'put',
    data: data
  }).then(response => {
    // 新的 exercises API 直接返回正确格式的数据，无需转换
    return response
  })
}

/**
 * 删除动作
 * @param {number} id - 动作ID
 */
export function deleteExercise(id) {
  return request({
    url: `/api/v1/exercises/${id}`,
    method: 'delete'
  })
}

/**
 * 获取动作统计信息
 */
export function getExerciseStats() {
  return request({
    url: '/api/v1/exercises/stats',
    method: 'get'
  })
}

/**
 * 获取模板统计信息
 */
export function getTemplateStats() {
  return request({
    url: '/api/v1/templates/stats',
    method: 'get'
  })
}

// ============ 标签相关 API ============

/**
 * 获取标签列表
 * @param {Object} params - 查询参数
 * @param {number} params.category_id - 标签分类ID
 * @param {string} params.name - 标签名称搜索
 */
export function getTags(params) {
  return request({
    url: '/api/v1/templates/tags/',
    method: 'get',
    params
  })
}

/**
 * 创建新标签
 * @param {Object} data - 标签数据
 */
export function createTag(data) {
  return request({
    url: '/api/v1/templates/tags/',
    method: 'post',
    data
  })
}

/**
 * 获取分类列表（原标签分类）
 */
export function getCategories() {
  return request({
    url: '/api/v1/categories',
    method: 'get'
  })
}

/**
 * 获取标签分类列表（保持向后兼容）
 * @deprecated 请使用 getCategories() 替代
 */
export function getTagCategories() {
  return getCategories()
}

// ============ 指标相关 API ============

/**
 * 获取指标列表
 * @param {Object} params - 查询参数
 * @param {number} params.category_id - 指标分类ID
 */
export function getMetrics(params) {
  return request({
    url: '/api/v1/metrics',
    method: 'get',
    params
  })
}

/**
 * 创建新指标
 * @param {Object} data - 指标数据
 */
export function createMetric(data) {
  return request({
    url: '/api/v1/metrics',
    method: 'post',
    data
  })
}

/**
 * 更新指标
 * @param {number} id - 指标ID
 * @param {Object} data - 更新数据
 */
export function updateMetric(id, data) {
  return request({
    url: `/api/v1/metrics/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除指标
 * @param {number} id - 指标ID
 */
export function deleteMetric(id) {
  return request({
    url: `/api/v1/metrics/${id}`,
    method: 'delete'
  })
}

/**
 * 获取指标分类列表
 */
export function getMetricCategories() {
  return request({
    url: '/api/v1/metrics/categories',
    method: 'get'
  })
}

/**
 * 创建指标分类
 * @param {Object} data - 分类数据
 * @param {string} data.name - 分类名称
 * @param {string} data.description - 分类描述
 */
export function createMetricCategory(data) {
  return request({
    url: '/api/v1/metrics/categories',
    method: 'post',
    data
  })
}

/**
 * 获取单个指标分类详情
 * @param {number} id - 分类ID
 */
export function getMetricCategory(id) {
  return request({
    url: `/api/v1/metrics/categories/${id}`,
    method: 'get'
  })
}

/**
 * 更新指标分类
 * @param {number} id - 分类ID
 * @param {Object} data - 更新的分类数据
 * @param {string} data.name - 分类名称
 * @param {string} data.description - 分类描述
 * @param {boolean} data.is_active - 是否激活
 */
export function updateMetricCategory(id, data) {
  return request({
    url: `/api/v1/metrics/categories/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除指标分类
 * @param {number} id - 分类ID
 */
export function deleteMetricCategory(id) {
  return request({
    url: `/api/v1/metrics/categories/${id}`,
    method: 'delete'
  })
}

// ============ 训练日计划相关 API ============

/**
 * 获取训练日模板列表（新API，支持多模板关联）
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认50
 * @param {string} params.sport_type - 专项类型筛选
 */
export function getDailyPlanTemplates(params = {}) {
  return request({
    url: '/api/v1/daily-templates/list',
    method: 'get',
    params: {
      limit: 50,
      ...params
    }
  })
}

/**
 * 获取训练日模板的关联动作和指标（核心多模板API）
 * @param {number} planId - 训练日模板ID
 */
export function getDailyPlanExercises(planId) {
  return request({
    url: `/api/v1/daily-templates/${planId}/exercises`,
    method: 'get'
  })
}

/**
 * 获取训练日模板详情
 * @param {number} planId - 训练日模板ID
 */
export function getDailyPlanTemplate(planId) {
  return request({
    url: `/api/v1/daily-templates/${planId}`,
    method: 'get'
  })
}

/**
 * 获取最近的训练日计划
 * @param {number} limit - 限制数量
 */
export function getRecentDailyPlans(limit = 10) {
  return request({
    url: '/api/v1/daily-plans',
    method: 'get',
    params: {
      isTemplate: 'false', // 只获取实际计划
      status: 'active',
      size: limit,
      // 按更新时间排序
      sort: 'updated_at',
      order: 'desc'
    }
  })
}

/**
 * 获取单个训练日计划详情
 * @param {number} id - 计划ID
 */
export function getDailyPlan(id) {
  return request({
    url: `/api/v1/daily-plans/${id}`,
    method: 'get'
  })
}

// ============ 动作模板相关 API ============

/**
 * 获取动作的模板列表
 * @param {number} exerciseId - 动作ID
 */
export function getExerciseTemplatesByExerciseId(exerciseId) {
  return request({
    url: `/api/v1/templates/${exerciseId}/templates`,
    method: 'get'
  })
}

/**
 * 创建动作模板
 * @param {Object} data - 模板数据
 */
export function createExerciseTemplate(data) {
  return request({
    url: '/api/v1/templates/templates/',
    method: 'post',
    data
  })
}

/**
 * 获取模板详情
 * @param {number} templateId - 模板ID
 */
export function getExerciseTemplate(templateId) {
  return request({
    url: `/api/v1/templates/${templateId}`,
    method: 'get'
  })
}

/**
 * 更新模板
 * @param {number} templateId - 模板ID
 * @param {Object} data - 更新数据
 */
export function updateExerciseTemplate(templateId, data) {
  return request({
    url: `/api/v1/templates/templates/${templateId}`,
    method: 'put',
    data
  })
}

/**
 * 删除模板
 * @param {number} templateId - 模板ID
 */
export function deleteExerciseTemplate(templateId) {
  return request({
    url: `/api/v1/templates/templates/${templateId}`,
    method: 'delete'
  })
}

// ============ 训练日模板 API ============

/**
 * 创建训练日模板
 * @param {Object} templateData - 训练日模板数据
 * @param {string} templateData.name - 模板名称
 * @param {Array} templateData.components - 训练组件列表
 * @param {number} templateData.total_duration - 总时长（分钟）
 * @param {string} templateData.trainingType - 训练类型
 * @param {string} templateData.targetGroup - 目标人群
 * @param {string} templateData.visibility - 可见性
 */
export function createDailyTemplate(templateData) {
  return request({
    url: '/api/v1/templates',
    method: 'post',
    data: templateData
  })
}

/**
 * 更新训练日模板
 * @param {number} templateId - 模板ID
 * @param {Object} templateData - 训练日模板数据
 */
export function updateDailyTemplate(templateId, templateData) {
  return request({
    url: `/api/v1/templates/${templateId}`,
    method: 'put',
    data: templateData
  })
}

/**
 * 获取训练日模板列表
 * @param {Object} params - 查询参数
 */
export function getDailyTemplates(params = {}) {
  return request({
    url: '/api/v1/templates',
    method: 'get',
    params
  })
}

/**
 * 获取单个训练日模板详情
 * @param {number} templateId - 模板ID
 */
export function getDailyTemplate(templateId) {
  return request({
    url: `/api/v1/daily-templates/${templateId}`,
    method: 'get'
  })
}

/**
 * 获取训练日模板详情（更明确的命名）
 * @param {number} planId - 训练日模板ID
 */
export function getDailyPlanTemplateDetail(planId) {
  return request({
    url: `/api/v1/daily-templates/${planId}`,
    method: 'get'
  })
}

/**
 * 删除训练日模板
 * @param {number} templateId - 模板ID
 */
export function deleteDailyTemplate(templateId) {
  return request({
    url: `/api/v1/templates/${templateId}`,
    method: 'delete'
  })
}

// ============ 批量操作 API ============

/**
 * 批量删除动作
 * @param {Array} ids - 动作ID数组
 */
export function batchDeleteExercises(ids) {
  return request({
    url: '/api/v1/templates/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 批量更新动作标签
 * @param {Array} exercises - 动作更新数据数组
 */
export function batchUpdateExerciseTags(exercises) {
  return request({
    url: '/api/v1/templates/batch/tags',
    method: 'put',
    data: { exercises }
  })
}

/**
 * 导入动作数据
 * @param {FormData} formData - 包含文件的表单数据
 */
export function importExercises(formData) {
  return request({
    url: '/api/v1/templates/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出动作数据
 * @param {Object} params - 导出参数
 * @param {string} params.format - 导出格式 (excel, csv, json)
 * @param {Array} params.exercise_ids - 要导出的动作ID数组（可选）
 */
export function exportExercises(params) {
  return request({
    url: '/api/v1/templates/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ============ 搜索和推荐 API ============

/**
 * 智能搜索动作
 * @param {string} query - 搜索关键词
 * @param {Object} filters - 额外筛选条件
 */
export function searchExercises(query, filters = {}) {
  return request({
    url: '/api/v1/templates/search',
    method: 'get',
    params: {
      q: query,
      ...filters
    }
  })
}

/**
 * 获取推荐动作
 * @param {Object} params - 推荐参数
 * @param {number} params.exercise_id - 基于的动作ID
 * @param {Array} params.tag_ids - 基于的标签ID数组
 * @param {string} params.difficulty_level - 难度等级
 * @param {number} params.limit - 推荐数量限制
 */
export function getRecommendedExercises(params) {
  return request({
    url: '/api/v1/templates/recommendations',
    method: 'get',
    params
  })
}

// ============ 数据验证和工具 API ============

/**
 * 验证动作名称是否可用
 * @param {string} name - 动作名称
 * @param {number} excludeId - 排除的动作ID（用于编辑时验证）
 */
export function validateExerciseName(name, excludeId = null) {
  return request({
    url: '/api/v1/templates/validate-name',
    method: 'post',
    data: { name, exclude_id: excludeId }
  })
}

/**
 * 获取动作分类选项
 */
export function getExerciseCategories() {
  return request({
    url: '/api/v1/exercises/categories',
    method: 'get'
  })
}

/**
 * 获取常用标签组合
 */
export function getPopularTagCombinations() {
  return request({
    url: '/api/v1/templates/tags/popular-combinations',
    method: 'get'
  })
}

/**
 * 分析动作使用趋势
 * @param {Object} params - 分析参数
 * @param {string} params.period - 时间段 (week, month, quarter, year)
 * @param {Array} params.exercise_ids - 动作ID数组（可选）
 */
export function getExerciseUsageTrends(params) {
  return request({
    url: '/api/v1/templates/analytics/trends',
    method: 'get',
    params
  })
}

export default {
  // 动作管理
  getExercises,
  getExercise,
  createExercise,
  updateExercise,
  deleteExercise,
  getExerciseStats,
  getTemplateStats,
  
  // 分类管理
  getTags,
  createTag,
  getCategories,
  getTagCategories,
  
  // 指标管理
  getMetrics,
  createMetric,
  updateMetric,
  deleteMetric,
  getMetricCategories,
  createMetricCategory,
  getMetricCategory,
  updateMetricCategory,
  deleteMetricCategory,
  
  // 模板管理
  getExerciseTemplates,
  getExerciseTemplatesByExerciseId,
  createExerciseTemplate,
  getExerciseTemplate,
  updateExerciseTemplate,
  deleteExerciseTemplate,
  
  // 批量操作
  batchDeleteExercises,
  batchUpdateExerciseTags,
  importExercises,
  exportExercises,
  
  // 搜索推荐
  searchExercises,
  getRecommendedExercises,
  
  // 工具方法
  validateExerciseName,
  getExerciseCategories,
  getPopularTagCombinations,
  getExerciseUsageTrends
}