<template>
  <div class="page-header">
    <el-breadcrumb separator="/" v-if="breadcrumb && breadcrumb.length > 0">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumb" 
        :key="index"
        :to="item.path ? { path: item.path } : undefined"
      >
        {{ item.label }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    
    <h1 class="page-title">{{ title }}</h1>
    
    <div class="page-actions" v-if="$slots.actions">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface BreadcrumbItem {
  label: string
  path?: string
}

interface Props {
  title: string
  breadcrumb?: BreadcrumbItem[]
}

defineProps<Props>()
</script>

<style scoped>
.page-header {
  background: white;
  padding: 16px 24px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin: 12px 0 0 0;
}

.page-actions {
  margin-top: 16px;
}

:deep(.el-breadcrumb) {
  font-size: 14px;
}
</style>