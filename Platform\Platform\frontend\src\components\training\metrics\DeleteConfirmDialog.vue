<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="确认删除"
    width="400px"
    :before-close="handleClose"
  >
    <div class="delete-dialog-content">
      <div class="warning-icon">
        <el-icon color="#f56c6c" size="48"><warning-filled /></el-icon>
      </div>
      <div class="warning-text">
        <p>确定要删除指标 <strong>"{{ metricName }}"</strong> 吗？</p>
        <p class="warning-note">此操作不可撤销，删除后相关数据将无法恢复。</p>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">
          取消
        </el-button>
        <el-button
          type="danger"
          @click="handleConfirm"
          :loading="loading"
        >
          确认删除
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { WarningFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  metricName: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const handleClose = () => {
  emit('update:modelValue', false)
  emit('cancel')
}

const handleConfirm = () => {
  emit('confirm')
}
</script>

<style lang="scss" scoped>
.delete-dialog-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  padding: 16px 0;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
  
  p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #303133;
  }
  
  .warning-note {
    font-size: 12px;
    color: #909399;
  }
  
  strong {
    color: #f56c6c;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>