<template>
  <div class="muscle-anatomy-pro">
    <div class="anatomy-header">
      <h4>{{ exerciseName }} - 专业肌肉解剖图</h4>
      <div class="anatomy-controls">
        <!-- 性别切换 -->
        <div class="gender-toggle">
          <el-button-group size="small">
            <el-button 
              :type="currentGender === 'male' ? 'primary' : 'default'"
              @click="currentGender = 'male'"
              class="gender-button male-button"
            >
              <el-icon><Male /></el-icon>
              <span class="gender-symbol">♂</span>
              男性
            </el-button>
            <el-button 
              :type="currentGender === 'female' ? 'primary' : 'default'"
              @click="currentGender = 'female'"
              class="gender-button female-button"
            >
              <el-icon><Female /></el-icon>
              <span class="gender-symbol">♀</span>
              女性
            </el-button>
          </el-button-group>
        </div>

        
        <!-- 图例 -->
        <div class="anatomy-legend">
          <div class="legend-item">
            <div class="legend-color primary" :style="{ backgroundColor: primaryColor }"></div>
            <span>主要肌群</span>
          </div>
          <div class="legend-item">
            <div class="legend-color secondary" :style="{ backgroundColor: secondaryColor }"></div>
            <span>辅助肌群</span>
          </div>
          <div class="legend-item">
            <div class="legend-color stabilizer" :style="{ backgroundColor: stabilizerColor }"></div>
            <span>稳定肌群</span>
          </div>
        </div>
      </div>
    </div>

    <div class="anatomy-body">
      <!-- 专业解剖图 -->
      <div class="anatomy-diagram-container">
        <HumanMuscleAnatomy 
          :gender="currentGender"
          :selected-primary-muscle-groups="primaryMuscles"
          :selected-secondary-muscle-groups="secondaryMuscles"
          :primary-highlight-color="primaryColor"
          :secondary-highlight-color="secondaryColor"
          :primary-opacity="0.8"
          :secondary-opacity="0.6"
          default-muscle-color="#f5f5f5"
          background-color="#fafbfc"
          class="human-anatomy-chart"
        />
      </div>

      <!-- 激活肌肉列表或无数据提示 -->
      <div class="muscles-info-container">
        <!-- 有激活肌肉时显示肌肉列表 -->
        <div v-if="activatedMusclesList.length > 0" class="activated-muscles">
          <h5>激活的肌肉群</h5>
          <div class="muscles-summary">
            <div class="summary-stats">
              <el-statistic title="总肌肉群" :value="activatedMusclesList.length" />
              <el-statistic title="主要肌群" :value="primaryMusclesCount" />
              <el-statistic title="辅助肌群" :value="secondaryMusclesCount" />
              <el-statistic title="稳定肌群" :value="stabilizerMusclesCount" />
            </div>
          </div>
          
          <div class="muscles-grid">
            <div 
              v-for="muscle in activatedMusclesList"
              :key="muscle.name"
              class="muscle-item"
              :class="muscle.type"
            >
              <div class="muscle-indicator" :class="muscle.type"></div>
              <div class="muscle-content">
                <span class="muscle-name">{{ muscle.displayName }}</span>
                <span class="muscle-type">{{ getMuscleTypeLabel(muscle.type) }}</span>
              </div>
              <div class="muscle-intensity">
                <el-progress 
                  :percentage="getIntensityPercentage(muscle.type)" 
                  :color="getMuscleColor(muscle.type)"
                  :stroke-width="4"
                  :show-text="false"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无激活肌肉时的提示 -->
        <div v-else class="no-muscles-activated">
          <el-icon class="no-muscles-icon"><Warning /></el-icon>
          <div class="no-muscles-text">
            <h5>暂无肌肉群数据</h5>
            <p>该动作的肌肉群激活信息正在完善中</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { Warning, Male, Female } from '@element-plus/icons-vue'
import { HumanMuscleAnatomy } from '@lucawahlen/vue-human-muscle-anatomy'

// Props
const props = defineProps({
  muscleGroups: {
    type: Object,
    default: () => ({})
  },
  exerciseName: {
    type: String,
    default: '动作'
  },
  interactive: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['muscle-selected', 'gender-changed'])

// 响应式数据
const currentGender = ref('male')
const primaryColor = '#e74c3c'  // 主要肌群颜色
const secondaryColor = '#f39c12'  // 辅助肌群颜色  
const stabilizerColor = '#27ae60'  // 稳定肌群颜色

// 肌肉群映射 - 将数据库中的肌肉群名称映射到库支持的名称
const muscleMapping = {
  // 数据库中的英文名称映射
  'quadriceps': 'quads',
  'hamstrings': 'hamstrings', 
  'glutes': 'glutes',
  'calves': 'calves',
  'chest': 'chest',
  'lats': 'lats',
  'traps': 'traps',
  'triceps': 'triceps',
  'biceps': 'biceps',
  'abs': 'abs',
  'obliques': 'obliques',
  'shoulders': 'shoulders',
  'forearms': 'forearms',
  
  // 中文到英文映射（备用）
  '胸肌': 'chest',
  '胸大肌': 'chest',
  '背阔肌': 'lats',
  '背肌': 'lats',
  '斜方肌': 'traps',
  '肱三头肌': 'triceps',
  '肱二头肌': 'biceps',
  '股四头肌': 'quads',
  '腘绳肌': 'hamstrings',
  '小腿': 'calves',
  '小腿肌群': 'calves',
  '腹肌': 'abs',
  '腹斜肌': 'obliques',
  '臀大肌': 'glutes',
  '臀肌': 'glutes',
  '肩膀': 'shoulders',
  '三角肌': 'shoulders',
  '前臂': 'forearms'
}

// 计算属性
const primaryMuscles = computed(() => {
  const muscles = props.muscleGroups?.primary || []
  return muscles.map(muscle => muscleMapping[muscle] || muscle).filter(Boolean)
})

const secondaryMuscles = computed(() => {
  const muscles = props.muscleGroups?.secondary || []
  return muscles.map(muscle => muscleMapping[muscle] || muscle).filter(Boolean)
})

const stabilizerMuscles = computed(() => {
  const muscles = props.muscleGroups?.stabilizer || []
  return muscles.map(muscle => muscleMapping[muscle] || muscle).filter(Boolean)
})

const activatedMusclesList = computed(() => {
  const result = []
  
  // 处理主要肌群
  if (props.muscleGroups?.primary && Array.isArray(props.muscleGroups.primary)) {
    props.muscleGroups.primary.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscle,
        type: 'primary',
        intensity: 100
      })
    })
  }
  
  // 处理辅助肌群
  if (props.muscleGroups?.secondary && Array.isArray(props.muscleGroups.secondary)) {
    props.muscleGroups.secondary.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscle,
        type: 'secondary',
        intensity: 70
      })
    })
  }
  
  // 处理稳定肌群
  if (props.muscleGroups?.stabilizer && Array.isArray(props.muscleGroups.stabilizer)) {
    props.muscleGroups.stabilizer.forEach(muscle => {
      result.push({
        name: muscle,
        displayName: muscle,
        type: 'stabilizer',
        intensity: 50
      })
    })
  }
  
  return result
})

const primaryMusclesCount = computed(() => {
  return activatedMusclesList.value.filter(m => m.type === 'primary').length
})

const secondaryMusclesCount = computed(() => {
  return activatedMusclesList.value.filter(m => m.type === 'secondary').length
})

const stabilizerMusclesCount = computed(() => {
  return activatedMusclesList.value.filter(m => m.type === 'stabilizer').length
})

// 方法
const getMuscleTypeLabel = (type) => {
  const typeMap = {
    primary: '主要',
    secondary: '辅助',
    stabilizer: '稳定'
  }
  return typeMap[type] || type
}

const getIntensityPercentage = (type) => {
  const intensityMap = {
    primary: 100,
    secondary: 70,
    stabilizer: 50
  }
  return intensityMap[type] || 0
}

const getMuscleColor = (type) => {
  const colorMap = {
    primary: primaryColor,
    secondary: secondaryColor,
    stabilizer: stabilizerColor
  }
  return colorMap[type] || '#95a5a6'
}

// 监听性别变化
const handleGenderChange = () => {
  emit('gender-changed', currentGender.value)
}

// 加粗轮廓线条并增强性别特征的函数
const enhanceOutlines = () => {
  // 增加多次尝试机制
  let attempts = 0
  const maxAttempts = 5
  
  const tryEnhancement = () => {
    attempts++
    console.log(`🔍 第 ${attempts} 次尝试增强SVG元素...`)
    
    // 尝试多种选择器
    const selectors = [
      '.human-anatomy-chart svg *',
      '.human-anatomy-chart svg path', 
      '.muscle-anatomy-pro svg *',
      'svg *',
      '.anatomy-diagram-container svg *'
    ]
    
    let foundElements = []
    let svgContainer = null
    
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector)
      console.log(`选择器 "${selector}": 找到 ${elements.length} 个元素`)
      
      if (elements.length > 0) {
        foundElements = elements
        // 优先查找人体解剖图的SVG
        svgContainer = document.querySelector('.human-anatomy-chart svg') || document.querySelector('svg')
        break
      }
    }
    
    if (foundElements.length === 0) {
      console.log('❌ 未找到SVG元素，尝试查找所有SVG...')
      const allSvgs = document.querySelectorAll('svg')
      console.log(`页面中总共有 ${allSvgs.length} 个SVG元素`)
      
      // 找到最大的SVG（很可能是人体图）
      let largestSvg = null
      let maxSize = 0
      
      allSvgs.forEach((svg, index) => {
        const rect = svg.getBoundingClientRect()
        const size = rect.width * rect.height
        console.log(`SVG ${index + 1}: ${svg.className || 'no class'}, 尺寸: ${rect.width}x${rect.height}, 子元素数: ${svg.children.length}`)
        
        if (size > maxSize && svg.children.length > 10) { // 人体图通常有很多子元素
          maxSize = size
          largestSvg = svg
        }
        
        const svgChildren = svg.querySelectorAll('*')
        if (svgChildren.length > foundElements.length) {
          foundElements = svgChildren
          svgContainer = svg
        }
      })
      
      // 如果找到了最大的SVG，优先使用它
      if (largestSvg) {
        svgContainer = largestSvg
        foundElements = largestSvg.querySelectorAll('*')
        console.log(`✅ 选择最大的SVG作为人体图: ${foundElements.length} 个元素`)
      }
    }
    
    // 如果找到了足够的元素，进行增强
    if (foundElements.length > 10 && svgContainer) {
      // 应用样式
      let enhancedCount = 0
      foundElements.forEach(el => {
        if (el.tagName === 'path' || el.tagName === 'line' || el.tagName === 'polygon' || 
            el.tagName === 'polyline' || el.tagName === 'circle' || el.tagName === 'ellipse') {
          
          // 检查是否已经是性别特征元素，如果是则跳过
          if (el.closest('#gender-features')) {
            return
          }
          
          // 设置固定的基础轮廓样式，不受性别切换影响
          el.style.strokeWidth = '2.5px'
          el.style.stroke = '#2c3e50'
          el.style.strokeLinecap = 'round'
          el.style.strokeLinejoin = 'round'
          
          // 如果是高亮的肌肉，使用更粗的轮廓
          const fill = el.getAttribute('fill') || el.style.fill
          if (fill && (fill.includes('#e74c3c') || fill.includes('#f39c12') || fill.includes('#27ae60'))) {
            el.style.strokeWidth = '3px'
          }
          
          enhancedCount++
        }
      })
      
      console.log(`🎨 成功为 ${enhancedCount} 个SVG元素应用加粗轮廓`)
      
      // 性别特征已通过原生 SVG 路径实现，无需额外增强
      return true // 成功
    } else if (attempts < maxAttempts) {
      // 如果没找到足够元素且未达到最大尝试次数，继续尝试
      console.log(`⏳ 未找到足够的SVG元素，${1000 * attempts}ms后重试...`)
      setTimeout(tryEnhancement, 1000 * attempts)
    } else {
      console.log('❌ 达到最大尝试次数，无法找到人体SVG图')
    }
  }
  
  // 立即开始第一次尝试
  setTimeout(tryEnhancement, 1000)
}

// 增强性别特征的函数
const enhanceGenderFeatures = (svgContainer) => {
  console.log('👫 开始增强性别特征...')
  
  try {
    // 获取SVG尺寸信息 - 使用实际渲染尺寸而不是viewBox
    const svgRect = svgContainer.getBoundingClientRect()
    const viewBox = svgContainer.getAttribute('viewBox') || `0 0 ${svgRect.width} ${svgRect.height}`
    const [vbX, vbY, vbWidth, vbHeight] = viewBox.split(' ').map(Number)
    
    // 使用实际显示尺寸，这样坐标会在可见区域内
    const svgWidth = svgRect.width || 339
    const svgHeight = svgRect.height || 339
    
    console.log(`SVG尺寸信息: viewBox=${viewBox}, 计算尺寸=${svgWidth}x${svgHeight}`)
    
    // 创建性别特征组
    let genderGroup = svgContainer.querySelector('#gender-features')
    if (!genderGroup) {
      genderGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g')
      genderGroup.id = 'gender-features'
      genderGroup.style.zIndex = '1000' // 确保在顶层
      svgContainer.appendChild(genderGroup)
      console.log('✅ 创建了新的性别特征组')
    } else {
      genderGroup.innerHTML = ''
      console.log('✅ 清空了现有的性别特征组')
    }
    
    const currentGenderValue = currentGender.value
    console.log(`为 ${currentGenderValue} 添加性别特征`)
    
    if (currentGenderValue === 'female') {
      // 女性特征：简洁的长头发轮廓
      
      // 1. 女性符号（左上角）
      const femaleSymbol = document.createElementNS('http://www.w3.org/2000/svg', 'text')
      femaleSymbol.setAttribute('x', '10')
      femaleSymbol.setAttribute('y', '25')
      femaleSymbol.setAttribute('fill', '#FF69B4')
      femaleSymbol.setAttribute('font-size', '20px')
      femaleSymbol.setAttribute('font-weight', 'bold')
      femaleSymbol.setAttribute('font-family', 'Arial, sans-serif')
      femaleSymbol.textContent = '♀ 女性'
      genderGroup.appendChild(femaleSymbol)
      
      // 2. 简洁的女性头发轮廓 - 仅头顶和颈部
      const femaleHair = document.createElementNS('http://www.w3.org/2000/svg', 'path')
      const femaleHairPath = `M ${svgWidth * 0.35} ${svgHeight * 0.08}
                            Q ${svgWidth * 0.30} ${svgHeight * 0.05} ${svgWidth * 0.40} ${svgHeight * 0.03}
                            Q ${svgWidth * 0.50} ${svgHeight * 0.02} ${svgWidth * 0.60} ${svgHeight * 0.03}
                            Q ${svgWidth * 0.70} ${svgHeight * 0.05} ${svgWidth * 0.65} ${svgHeight * 0.08}
                            L ${svgWidth * 0.65} ${svgHeight * 0.15}
                            L ${svgWidth * 0.35} ${svgHeight * 0.15}
                            Z`
      femaleHair.setAttribute('d', femaleHairPath)
      femaleHair.setAttribute('fill', '#2F4F4F')
      femaleHair.setAttribute('stroke', '#1C3A3A')
      femaleHair.setAttribute('stroke-width', '1')
      femaleHair.setAttribute('opacity', '0.7')
      genderGroup.appendChild(femaleHair)
      console.log('✅ 添加了简洁的女性头发轮廓')
      
    } else if (currentGenderValue === 'male') {
      // 男性特征：短发和胡须造型
      
      // 1. 男性符号（左上角）
      const maleSymbol = document.createElementNS('http://www.w3.org/2000/svg', 'text')
      maleSymbol.setAttribute('x', '10')
      maleSymbol.setAttribute('y', '25')
      maleSymbol.setAttribute('fill', '#4169E1')
      maleSymbol.setAttribute('font-size', '20px')
      maleSymbol.setAttribute('font-weight', 'bold')
      maleSymbol.setAttribute('font-family', 'Arial, sans-serif')
      maleSymbol.textContent = '♂ 男性'
      genderGroup.appendChild(maleSymbol)
      
      // 2. 简洁的男性短发轮廓 - 仅头顶
      const maleHair = document.createElementNS('http://www.w3.org/2000/svg', 'path')
      const maleHairPath = `M ${svgWidth * 0.40} ${svgHeight * 0.06}
                          Q ${svgWidth * 0.50} ${svgHeight * 0.04} ${svgWidth * 0.60} ${svgHeight * 0.06}
                          L ${svgWidth * 0.60} ${svgHeight * 0.08}
                          L ${svgWidth * 0.40} ${svgHeight * 0.08}
                          Z`
      maleHair.setAttribute('d', maleHairPath)
      maleHair.setAttribute('fill', '#2F4F4F')
      maleHair.setAttribute('stroke', '#1C3A3A')
      maleHair.setAttribute('stroke-width', '1')
      maleHair.setAttribute('opacity', '0.7')
      genderGroup.appendChild(maleHair)
      console.log('✅ 添加了简洁的男性短发轮廓')
    }
    
    console.log('✅ 性别特征增强完成')
    
  } catch (error) {
    console.error('性别特征增强失败:', error)
  }
}

// 组件挂载后加粗轮廓

onMounted(() => {
  enhanceOutlines()
})

// 监听性别切换，重新应用样式
watch(currentGender, () => {
  nextTick(() => {
    console.log(`🔄 性别切换到: ${currentGender.value}`)
    enhanceOutlines()
    
    // 性别特征已通过组件内的原生 SVG 路径实现
  })
})
</script>

<style scoped>
.muscle-anatomy-pro {
  background: #fff;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.anatomy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.anatomy-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.anatomy-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.gender-toggle, 
.view-toggle {
  display: flex;
  align-items: center;
}

/* 增强性别按钮和视图按钮的视觉区分 */
.gender-button,
.view-button {
  position: relative;
  padding: 8px 16px !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.gender-symbol {
  font-size: 1.2em;
  font-weight: bold;
  margin: 0 4px;
}

.male-button {
  border-color: #4169E1 !important;
}

.male-button.el-button--primary {
  background: linear-gradient(135deg, #4169E1, #6495ED) !important;
  border-color: #4169E1 !important;
  box-shadow: 0 2px 8px rgba(65, 105, 225, 0.3);
}

.male-button .gender-symbol {
  color: #4169E1;
}

.male-button.el-button--primary .gender-symbol {
  color: white;
}

.female-button {
  border-color: #FF69B4 !important;
}

.female-button.el-button--primary {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1) !important;
  border-color: #FF69B4 !important;
  box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
}

.female-button .gender-symbol {
  color: #FF69B4;
}

.female-button.el-button--primary .gender-symbol {
  color: white;
}

/* 性别按钮悬停效果 */
.male-button:hover:not(.el-button--primary) {
  background-color: rgba(65, 105, 225, 0.1) !important;
  border-color: #4169E1 !important;
  transform: translateY(-1px);
}

.female-button:hover:not(.el-button--primary) {
  background-color: rgba(255, 105, 180, 0.1) !important;
  border-color: #FF69B4 !important;
  transform: translateY(-1px);
}

/* 视图按钮样式 */
.view-button {
  border-color: #52c41a !important;
}

.view-button.el-button--primary {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  border-color: #52c41a !important;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.view-button:hover:not(.el-button--primary) {
  background-color: rgba(82, 196, 26, 0.1) !important;
  border-color: #52c41a !important;
  transform: translateY(-1px);
}

.anatomy-legend {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.legend-color {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #ecf0f1;
}

.anatomy-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.anatomy-diagram-container {
  display: flex;
  justify-content: center;
  background: #fafbfc;
  border-radius: 16px;
  padding: 2rem;
  min-height: 500px;
}

.human-anatomy-chart {
  max-width: 350px;
  width: 100%;
  height: auto;
}

/* 专门针对人体解剖图的轮廓加粗样式 */
.human-anatomy-chart :deep(svg) {
  /* 确保SVG显示清晰 */
  shape-rendering: geometricPrecision;
  /* 为所有SVG元素设置基础样式 */
  stroke-width: 2.5px;
  stroke: #2c3e50;
}

/* 更强的优先级样式 */
.muscle-anatomy-pro .human-anatomy-chart :deep(svg *) {
  stroke-width: 2.5px !important;
  stroke: #2c3e50 !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

.muscle-anatomy-pro .human-anatomy-chart :deep(path),
.muscle-anatomy-pro .human-anatomy-chart :deep(line),
.muscle-anatomy-pro .human-anatomy-chart :deep(polyline),
.muscle-anatomy-pro .human-anatomy-chart :deep(polygon),
.muscle-anatomy-pro .human-anatomy-chart :deep(circle),
.muscle-anatomy-pro .human-anatomy-chart :deep(ellipse) {
  /* 加粗所有轮廓线条 */
  stroke-width: 3px !important;
  stroke: #2c3e50 !important;
  /* 确保轮廓清晰可见 */
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

/* 对于未高亮的肌肉部分，使用中等粗细的边框 */
.muscle-anatomy-pro .human-anatomy-chart :deep([fill="#f5f5f5"]) {
  stroke: #7f8c8d !important;
  stroke-width: 2px !important;
}

/* 对于高亮的肌肉部分，使用更粗的边框 */
.muscle-anatomy-pro .human-anatomy-chart :deep([fill*="#e74c3c"]),
.muscle-anatomy-pro .human-anatomy-chart :deep([fill*="#f39c12"]),
.muscle-anatomy-pro .human-anatomy-chart :deep([fill*="#27ae60"]) {
  stroke: #2c3e50 !important;
  stroke-width: 3.5px !important;
}

/* 人体主体轮廓线条特别加粗 */
.muscle-anatomy-pro .human-anatomy-chart :deep(.body-outline),
.muscle-anatomy-pro .human-anatomy-chart :deep(.main-outline),
.muscle-anatomy-pro .human-anatomy-chart :deep([class*="outline"]) {
  stroke-width: 4px !important;
  stroke: #2c3e50 !important;
}

/* 全局SVG样式覆盖 */
.muscle-anatomy-pro svg,
.muscle-anatomy-pro svg * {
  stroke-width: 3px !important;
  stroke: #2c3e50 !important;
}

/* 肌肉信息容器 - 统一左右布局 */
.muscles-info-container {
  background: #fafbfc;
  border-radius: 16px;
  padding: 2rem;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.activated-muscles {
  width: 100%;
  height: 100%;
}

.activated-muscles h5 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.muscles-summary {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #ecf0f1;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
}

.muscles-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.muscle-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #ecf0f1;
  transition: all 0.2s ease;
}

.muscle-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.muscle-indicator {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  flex-shrink: 0;
}

.muscle-indicator.primary {
  background-color: #e74c3c;
}

.muscle-indicator.secondary {
  background-color: #f39c12;
}

.muscle-indicator.stabilizer {
  background-color: #27ae60;
}

.muscle-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.muscle-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.95rem;
}

.muscle-type {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.muscle-intensity {
  width: 80px;
  flex-shrink: 0;
}

.no-muscles-activated {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  padding: 4rem;
  color: #7f8c8d;
  text-align: center;
  width: 100%;
  height: 100%;
}

.no-muscles-icon {
  font-size: 2.5rem;
  color: #bdc3c7;
}

.no-muscles-text h5 {
  margin: 0 0 0.5rem 0;
  color: #34495e;
}

.no-muscles-text p {
  margin: 0;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .anatomy-body {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .anatomy-diagram-container {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .anatomy-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .anatomy-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .anatomy-legend {
    width: 100%;
    justify-content: space-around;
  }
  
  .muscle-anatomy-pro {
    padding: 1.5rem;
  }
  
  .muscles-summary {
    padding: 1rem;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>