<template>
  <div class="training-config-selector" :class="selectorClasses">
    <!-- 性别选择 -->
    <div v-if="showGender" class="config-section gender-section">
      <div v-if="options?.display?.showLabels !== false" class="section-label">
        <span class="label-text">性别</span>
        <span v-if="isRequired('gender')" class="required-mark">*</span>
      </div>
      
      <!-- 性别选择 - Select模式 -->
      <el-select 
        v-if="genderDisplayMode === 'select'"
        v-model="localConfig.gender"
        :placeholder="genderPlaceholder"
        :disabled="disabled || readonly"
        :size="size"
        @change="handleGenderChange"
        clearable
      >
        <el-option
          v-for="option in filteredGenderOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
        >
          <span class="option-content">
            <span v-if="option.icon" class="option-icon">{{ option.icon }}</span>
            <span class="option-label">{{ option.label }}</span>
            <span v-if="options?.display?.showDescriptions && option.description" class="option-desc">
              {{ option.description }}
            </span>
          </span>
        </el-option>
      </el-select>
      
      <!-- 性别选择 - Radio模式 -->
      <el-radio-group 
        v-else-if="genderDisplayMode === 'radio'"
        v-model="localConfig.gender"
        :disabled="disabled || readonly"
        :size="size"
        @change="handleGenderChange"
      >
        <el-radio
          v-for="option in filteredGenderOptions"
          :key="option.value"
          :label="option.value"
          :disabled="option.disabled"
        >
          <span class="radio-content">
            <span v-if="option.icon" class="option-icon">{{ option.icon }}</span>
            {{ option.label }}
          </span>
        </el-radio>
      </el-radio-group>
      
      <!-- 性别选择 - Button Group模式 -->
      <el-button-group v-else-if="genderDisplayMode === 'button-group'">
        <el-button
          v-for="option in filteredGenderOptions"
          :key="option.value"
          :type="localConfig.gender === option.value ? 'primary' : 'default'"
          :disabled="disabled || readonly || option.disabled"
          :size="size"
          @click="handleGenderSelect(option.value)"
        >
          <span v-if="option.icon" class="option-icon">{{ option.icon }}</span>
          {{ option.label }}
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 等级选择 -->
    <div v-if="showLevel" class="config-section level-section">
      <div v-if="options?.display?.showLabels !== false" class="section-label">
        <span class="label-text">运动员等级</span>
        <span v-if="isRequired('level')" class="required-mark">*</span>
      </div>
      
      <el-select
        v-model="localConfig.level"
        :placeholder="levelPlaceholder"
        :disabled="disabled || readonly"
        :size="size"
        @change="handleLevelChange"
        clearable
      >
        <el-option
          v-for="option in filteredLevelOptions"
          :key="option.value"
          :label="options?.display?.compact ? option.shortLabel || option.label : option.label"
          :value="option.value"
          :disabled="option.disabled"
        >
          <div class="level-option-content">
            <span class="level-label">{{ option.label }}</span>
            <span v-if="options?.display?.showDescriptions && option.description" class="level-desc">
              {{ option.description }}
            </span>
          </div>
        </el-option>
      </el-select>
    </div>
    
    <!-- 专项选择 -->
    <div v-if="showSpecialty" class="config-section specialty-section">
      <div v-if="options?.display?.showLabels !== false" class="section-label">
        <span class="label-text">专项分类</span>
        <span v-if="isRequired('specialty')" class="required-mark">*</span>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="specialtyLoading" class="loading-state">
        <el-skeleton :rows="2" animated />
      </div>
      
      <!-- 专项选择 - 多选模式 -->
      <el-select
        v-else-if="specialtySelectionMode === 'multiple'"
        v-model="localConfig.specialty"
        :placeholder="specialtyPlaceholder"
        :disabled="disabled || readonly || !localConfig.gender || !availableSpecialties.length"
        :size="size"
        multiple
        :max-collapse-tags="8"
        collapse-tags-tooltip
        @change="handleSpecialtyChange"
        @focus="handleSpecialtyFocus"
      >
        <!-- 先显示7个大类作为主要选项 -->
        <el-option
          v-for="category in availableCategories"
          :key="`category-${category.id}`"
          :label="category.name"
          :value="`CATEGORY:${category.code}`"
          class="category-main-option"
        >
          <div class="specialty-option-content category-main-level">
            <span class="specialty-name">
              <i class="el-icon-folder"></i>
              <strong>{{ category.name }}</strong>
            </span>
            <span class="specialty-desc">
              {{ category.events.length }} 个项目
            </span>
          </div>
        </el-option>
        
        <!-- 分隔线 -->
        <el-divider class="specialty-divider">具体专项</el-divider>
        
        <!-- 然后按分组显示具体专项 -->
        <el-option-group
          v-for="category in availableCategories"
          :key="`group-${category.id}`"
          :label="category.name"
        >
          <el-option
            v-for="event in category.events"
            :key="event.id"
            :label="event.name"
            :value="event.code"
            class="event-option"
          >
            <div class="specialty-option-content event-level">
              <span class="specialty-name">{{ event.name }}</span>
              <span v-if="options?.display?.showDescriptions && event.description" class="specialty-desc">
                {{ event.description }}
              </span>
            </div>
          </el-option>
        </el-option-group>
      </el-select>
      
      <!-- 专项选择 - 单选模式 -->
      <el-select
        v-else
        v-model="selectedSpecialtySingle"
        :placeholder="specialtyPlaceholder"
        :disabled="disabled || readonly || !localConfig.gender || !availableSpecialties.length"
        :size="size"
        @change="handleSpecialtySingleChange"
        @focus="handleSpecialtyFocus"
        clearable
      >
        <!-- 先显示7个大类作为主要选项 -->
        <el-option
          v-for="category in availableCategories"
          :key="`category-${category.id}`"
          :label="category.name"
          :value="`CATEGORY:${category.code}`"
          class="category-main-option"
        >
          <div class="specialty-option-content category-main-level">
            <span class="specialty-name">
              <i class="el-icon-folder"></i>
              <strong>{{ category.name }}</strong>
            </span>
            <span class="specialty-desc">
              {{ category.events.length }} 个项目
            </span>
          </div>
        </el-option>
        
        <!-- 分隔线 -->
        <el-divider class="specialty-divider">具体专项</el-divider>
        
        <!-- 然后按分组显示具体专项 -->
        <el-option-group
          v-for="category in availableCategories"
          :key="`group-${category.id}`"
          :label="category.name"
        >
          <el-option
            v-for="event in category.events"
            :key="event.id"
            :label="event.name"
            :value="event.code"
            class="event-option"
          >
            <div class="specialty-option-content event-level">
              <span class="specialty-name">{{ event.name }}</span>
              <span v-if="options?.display?.showDescriptions && event.description" class="specialty-desc">
                {{ event.description }}
              </span>
            </div>
          </el-option>
        </el-option-group>
      </el-select>
      
      <!-- 无可用专项提示 -->
      <div v-if="!specialtyLoading && !availableSpecialties.length" class="no-specialty-hint">
        <el-alert
          title="暂无可用专项"
          :description="localConfig.gender ? `当前性别 ${getGenderText(localConfig.gender)} 暂无对应专项` : '请先选择性别'"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <!-- 年龄分组 -->
    <div v-if="showAge" class="config-section age-section">
      <div v-if="options?.display?.showLabels !== false" class="section-label">
        <span class="label-text">年龄分组</span>
        <span v-if="isRequired('ageCategory')" class="required-mark">*</span>
      </div>
      
      <el-select
        v-model="localConfig.ageCategory"
        :placeholder="agePlaceholder"
        :disabled="disabled || readonly"
        :size="size"
        @change="handleAgeChange"
        clearable
      >
        <el-option
          v-for="option in filteredAgeOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
        >
          <div class="age-option-content">
            <span class="age-label">{{ option.label }}</span>
            <span v-if="option.ageRange" class="age-range">({{ option.ageRange }})</span>
            <span v-if="options?.display?.showDescriptions && option.description" class="age-desc">
              {{ option.description }}
            </span>
          </div>
        </el-option>
      </el-select>
    </div>
    
    <!-- 配置预览 -->
    <div v-if="showPreview" class="config-preview">
      <el-divider content-position="left">
        <span class="preview-title">当前配置</span>
      </el-divider>
      <div class="preview-content">
        <el-tag v-if="localConfig.gender" type="primary" size="small">
          {{ getGenderText(localConfig.gender) }}
        </el-tag>
        <el-tag v-if="localConfig.level" type="success" size="small">
          {{ getLevelText(localConfig.level) }}
        </el-tag>
        <el-tag 
          v-if="localConfig.specialty && localConfig.specialty.length > 0" 
          type="warning" 
          size="small"
        >
          专项: {{ localConfig.specialty.length }}项
        </el-tag>
        <el-tag v-if="localConfig.ageCategory" type="info" size="small">
          {{ getAgeText(localConfig.ageCategory) }}
        </el-tag>
        <span v-if="!hasAnyConfig" class="no-config">暂无配置</span>
      </div>
    </div>
    
    <!-- 验证错误提示 -->
    <div v-if="validationErrors.length > 0" class="validation-errors">
      <el-alert
        v-for="(error, index) in validationErrors"
        :key="index"
        :title="error"
        type="error"
        :closable="false"
        show-icon
        class="error-item"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  TrainingConfig,
  TrainingConfigSelectorOptions,
  TrainingConfigSelectorProps,
  TrainingConfigEvents,
  SpecialtyCategory,
  SpecialtyEvent,
  GenderType,
  AthleteLevel,
  AgeCategory,
  GenderOption,
  LevelOption,
  AgeOption
} from '@/types/training-config'
import { 
  GENDER_OPTIONS, 
  LEVEL_OPTIONS, 
  AGE_OPTIONS 
} from '@/types/training-config'
import { 
  getSpecialtyCategories,
  getSpecialtiesByGender, 
  validateTrainingConfig,
  formatConfigForDisplay
} from '@/api/training-config'

// Props定义
const props = withDefaults(defineProps<TrainingConfigSelectorProps>(), {
  options: () => ({}),
  disabled: false,
  readonly: false,
  size: 'default'
})

// Events定义
const emit = defineEmits<TrainingConfigEvents>()

// 响应式数据
const localConfig = ref<TrainingConfig>({ ...props.modelValue })
const availableCategories = ref<SpecialtyCategory[]>([])
const availableSpecialties = ref<SpecialtyEvent[]>([])
const specialtyLoading = ref(false)
const validationErrors = ref<string[]>([])

// 单选专项的临时值
const selectedSpecialtySingle = ref<string>('')

// 计算属性 - 功能开关
const showGender = computed(() => props.options?.features?.showGender !== false)
const showSpecialty = computed(() => props.options?.features?.showSpecialty !== false)
const showLevel = computed(() => props.options?.features?.showLevel !== false)
const showAge = computed(() => props.options?.features?.showAge !== false)
const showPreview = computed(() => props.options?.features?.showCustom === true)

// 计算属性 - 显示模式
const genderDisplayMode = computed(() => props.options?.display?.mode || 'select')
const specialtySelectionMode = computed(() => props.options?.selection?.specialtyMode || 'multiple')

// 计算属性 - CSS类
const selectorClasses = computed(() => ({
  [`layout-${props.options?.display?.layout || 'vertical'}`]: true,
  'compact': props.options?.display?.compact,
  'disabled': props.disabled,
  'readonly': props.readonly,
  [`size-${props.size}`]: true
}))

// 计算属性 - 过滤选项
const filteredGenderOptions = computed(() => {
  let options = [...GENDER_OPTIONS]
  
  if (props.options?.filters?.genderFilter) {
    options = options.filter(opt => props.options?.filters?.genderFilter?.includes(opt.value))
  }
  
  return options
})

const filteredLevelOptions = computed(() => {
  let options = [...LEVEL_OPTIONS].sort((a, b) => a.order - b.order)
  
  if (props.options?.filters?.levelFilter) {
    options = options.filter(opt => props.options?.filters?.levelFilter?.includes(opt.value))
  }
  
  return options
})

const filteredAgeOptions = computed(() => {
  let options = [...AGE_OPTIONS]
  
  if (props.options?.filters?.ageFilter) {
    options = options.filter(opt => props.options?.filters?.ageFilter?.includes(opt.value))
  }
  
  return options
})

// 计算属性 - 占位符文本
const genderPlaceholder = computed(() => {
  return isRequired('gender') ? '请选择性别 *' : '请选择性别'
})

const levelPlaceholder = computed(() => {
  return isRequired('level') ? '请选择运动员等级 *' : '请选择运动员等级'
})

const specialtyPlaceholder = computed(() => {
  return isRequired('specialty') ? '请选择专项 *' : '请选择专项'
})

const agePlaceholder = computed(() => {
  return isRequired('ageCategory') ? '请选择年龄分组 *' : '请选择年龄分组'
})

// 计算属性 - 配置状态
const hasAnyConfig = computed(() => {
  return !!(localConfig.value.gender || 
           localConfig.value.level || 
           localConfig.value.ageCategory ||
           (localConfig.value.specialty && localConfig.value.specialty.length > 0))
})

// 工具函数
const isRequired = (field: keyof TrainingConfig): boolean => {
  return props.options?.validation?.required?.includes(field) || 
         props.options?.selection?.required?.[field] === true
}

const getGenderText = (gender: GenderType): string => {
  const option = GENDER_OPTIONS.find(opt => opt.value === gender)
  return option?.label || gender
}

const getLevelText = (level: AthleteLevel): string => {
  const option = LEVEL_OPTIONS.find(opt => opt.value === level)
  return option?.label || level
}

const getAgeText = (age: AgeCategory): string => {
  const option = AGE_OPTIONS.find(opt => opt.value === age)
  return option?.label || age
}

// 事件处理函数
const handleGenderChange = async (value: GenderType) => {
  localConfig.value.gender = value
  
  // 重新加载专项数据
  if (showSpecialty.value) {
    await loadSpecialtiesByGender(value)
    
    // 清空不兼容的专项选择
    if (localConfig.value.specialty && localConfig.value.specialty.length > 0) {
      const availableCodes = availableSpecialties.value.map(s => s.code)
      localConfig.value.specialty = localConfig.value.specialty.filter(code => 
        availableCodes.includes(code)
      )
    }
  }
  
  emitChange('gender')
}

const handleGenderSelect = (value: GenderType) => {
  localConfig.value.gender = localConfig.value.gender === value ? undefined : value
  handleGenderChange(localConfig.value.gender!)
}

const handleLevelChange = (value: AthleteLevel) => {
  localConfig.value.level = value
  emitChange('level')
}

const handleSpecialtyChange = (values: string[]) => {
  // 处理分类选择和具体项目选择
  const expandedValues: string[] = []
  
  values.forEach(value => {
    if (value.startsWith('CATEGORY:')) {
      // 如果选择了分类，展开为该分类下的所有项目
      const categoryCode = value.replace('CATEGORY:', '')
      const category = availableCategories.value.find(cat => cat.code === categoryCode)
      if (category) {
        // 根据当前性别筛选该分类下的可用项目
        const categoryEvents = category.events.filter(event => {
          if (!localConfig.value.gender || localConfig.value.gender === 'ALL') {
            return true
          }
          return event.genderSupport.includes(localConfig.value.gender!)
        })
        expandedValues.push(...categoryEvents.map(e => e.code))
      }
    } else {
      // 具体项目直接添加
      expandedValues.push(value)
    }
  })
  
  // 去重
  localConfig.value.specialty = [...new Set(expandedValues)]
  emitChange('specialty')
}

const handleSpecialtySingleChange = (value: string) => {
  if (!value) {
    localConfig.value.specialty = []
    selectedSpecialtySingle.value = ''
  } else if (value.startsWith('CATEGORY:')) {
    // 如果选择了分类，展开为该分类下的所有项目
    const categoryCode = value.replace('CATEGORY:', '')
    const category = availableCategories.value.find(cat => cat.code === categoryCode)
    if (category) {
      // 根据当前性别筛选该分类下的可用项目
      const categoryEvents = category.events.filter(event => {
        if (!localConfig.value.gender || localConfig.value.gender === 'ALL') {
          return true
        }
        return event.genderSupport.includes(localConfig.value.gender!)
      })
      localConfig.value.specialty = categoryEvents.map(e => e.code)
    }
  } else {
    localConfig.value.specialty = [value]
  }
  
  selectedSpecialtySingle.value = value
  emitChange('specialty')
}

const handleAgeChange = (value: AgeCategory) => {
  localConfig.value.ageCategory = value
  emitChange('ageCategory')
}

const handleSpecialtyFocus = () => {
  // 当用户点击专项选择框时，检查是否已选择性别
  if (!localConfig.value.gender) {
    ElMessage.warning('请先选择性别后再选择专项')
  }
}

// 业务逻辑函数
const loadSpecialtyCategories = async () => {
  try {
    specialtyLoading.value = true
    const categories = await getSpecialtyCategories()
    availableCategories.value = categories
    
    // 提取所有事件
    availableSpecialties.value = categories.flatMap(cat => cat.events)
    
  } catch (error) {
    console.error('加载专项分类失败:', error)
    ElMessage.error('加载专项分类失败')
  } finally {
    specialtyLoading.value = false
  }
}

const loadSpecialtiesByGender = async (gender?: GenderType) => {
  if (!gender) {
    availableSpecialties.value = []
    availableCategories.value = []
    return
  }
  
  try {
    specialtyLoading.value = true
    const specialties = await getSpecialtiesByGender(gender)
    availableSpecialties.value = specialties
    
    // 重新组织分类
    const categoriesMap = new Map<string, SpecialtyCategory>()
    specialties.forEach(specialty => {
      if (!categoriesMap.has(specialty.category)) {
        // 从原始分类中找到完整信息
        const originalCategory = availableCategories.value.find(cat => cat.code === specialty.category)
        categoriesMap.set(specialty.category, {
          id: specialty.category,
          name: originalCategory?.name || specialty.category,
          code: specialty.category,
          description: originalCategory?.description,
          events: []
        })
      }
      categoriesMap.get(specialty.category)!.events.push(specialty)
    })
    
    availableCategories.value = Array.from(categoriesMap.values())
    
  } catch (error) {
    console.error('根据性别加载专项失败:', error)
    availableSpecialties.value = []
    availableCategories.value = []
  } finally {
    specialtyLoading.value = false
  }
}

const validateConfig = async () => {
  try {
    const result = await validateTrainingConfig(localConfig.value)
    
    // 自定义验证
    if (props.options?.validation?.custom) {
      const customError = props.options.validation.custom(localConfig.value)
      if (customError) {
        result.errors.push(customError)
        result.isValid = false
      }
    }
    
    validationErrors.value = result.errors
    emit('validate', result.isValid, result.errors)
    
    return result.isValid
  } catch (error) {
    console.error('配置验证失败:', error)
    validationErrors.value = ['配置验证失败']
    emit('validate', false, ['配置验证失败'])
    return false
  }
}

const emitChange = async (field: keyof TrainingConfig) => {
  emit('update:modelValue', { ...localConfig.value })
  emit('update:config', { ...localConfig.value })
  emit('change', { ...localConfig.value }, field)
  
  // 自动验证
  if (props.options?.validation) {
    await validateConfig()
  }
}

// 公开方法
const reset = () => {
  localConfig.value = { ...props.options?.defaults } || {}
  selectedSpecialtySingle.value = ''
  validationErrors.value = []
  emit('reset')
}

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    localConfig.value = { ...newValue }
    
    // 同步单选专项值
    if (newValue && newValue.specialty && newValue.specialty.length > 0) {
      selectedSpecialtySingle.value = newValue.specialty[0]
    } else {
      selectedSpecialtySingle.value = ''
    }
  },
  { deep: true, immediate: true }
)

// 监听性别变化，自动加载对应专项
watch(
  () => localConfig.value.gender,
  async (newGender) => {
    if (showSpecialty.value && newGender) {
      await loadSpecialtiesByGender(newGender)
    }
  }
)

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    localConfig.value = { ...newValue }
  }
}, { deep: true })

// 生命周期
onMounted(async () => {
  // 初始化默认值
  if (props.options?.defaults) {
    Object.assign(localConfig.value, props.options.defaults)
  }
  
  // 加载专项数据
  if (showSpecialty.value) {
    // 先加载所有专项分类作为默认选项
    await loadSpecialtyCategories()
    
    // 如果已有性别选择，根据性别筛选专项
    if (localConfig.value.gender) {
      await loadSpecialtiesByGender(localConfig.value.gender)
    }
    // 如果没有性别选择，保持显示所有专项
  }
  
  // 初始化单选专项值
  if (localConfig.value.specialty && localConfig.value.specialty.length > 0) {
    selectedSpecialtySingle.value = localConfig.value.specialty[0]
  }
  
  // 初始验证
  if (props.options?.validation) {
    await validateConfig()
  }
})

// 暴露方法给父组件
defineExpose({
  reset,
  validate: validateConfig,
  getFormattedConfig: () => formatConfigForDisplay(localConfig.value)
})
</script>

<style scoped lang="scss">
.training-config-selector {
  .config-section {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    // 确保下拉框有足够的宽度（增加宽度以适应专项等较长文本）
    .el-select {
      min-width: 320px;  // 从140px增加到320px，确保所有文本完整显示
      width: 100%;
      max-width: 100%;  // 确保不超出容器
    }
  }
  
  .section-label {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    
    .required-mark {
      color: #f56c6c;
      margin-left: 2px;
    }
  }
  
  // 选项内容样式
  .option-content {
    display: flex;
    align-items: center;
    
    .option-icon {
      margin-right: 6px;
      font-size: 16px;
    }
    
    .option-desc {
      margin-left: auto;
      font-size: 12px;
      color: #909399;
    }
  }
  
  .radio-content {
    display: flex;
    align-items: center;
    
    .option-icon {
      margin-right: 6px;
      font-size: 14px;
    }
  }
  
  // 等级选项样式
  .level-option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .level-desc {
      font-size: 12px;
      color: #909399;
    }
  }
  
  // 专项选项样式
  .specialty-option-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .specialty-name {
      display: flex;
      align-items: center;
      gap: 8px;
      
      i {
        color: #409EFF;
        font-size: 16px;
      }
      
      strong {
        font-weight: 600;
      }
    }
    
    .specialty-desc {
      font-size: 11px;
      color: #909399;
      margin-left: auto;
    }
    
    // 主要大类选项样式（7个专项大类）
    &.category-main-level {
      background: linear-gradient(135deg, #f6f8fb 0%, #f0f2f5 100%);
      padding: 10px 12px;
      border-radius: 6px;
      margin: 4px 0;
      border: 1px solid #e4e7ed;
      transition: all 0.3s;
      
      &:hover {
        background: linear-gradient(135deg, #ecf5ff 0%, #e6f7ff 100%);
        border-color: #409EFF;
      }
      
      .specialty-name {
        color: #303133;
        font-weight: 600;
        font-size: 14px;
      }
      
      .specialty-desc {
        color: #606266;
        font-size: 12px;
        font-weight: 500;
      }
    }
    
    // 原有的大类选项样式（分组内的标题）
    &.category-level {
      background-color: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      margin: 2px 0;
      
      .specialty-name {
        color: #303133;
        font-weight: 600;
      }
      
      .specialty-desc {
        color: #606266;
        font-size: 12px;
      }
    }
    
    // 小类选项样式（具体专项）
    &.event-level {
      padding-left: 24px;
      
      .specialty-name {
        color: #606266;
        font-size: 13px;
      }
      
      .specialty-desc {
        font-size: 11px;
        color: #909399;
      }
    }
  }
  
  // 专项分隔线样式
  .specialty-divider {
    margin: 12px 0;
    
    :deep(.el-divider__text) {
      background-color: #fff;
      padding: 0 12px;
      color: #909399;
      font-size: 12px;
      font-weight: 500;
    }
  }
  
  // 选项hover效果
  .el-select-dropdown__item {
    // 大类主选项的特殊效果
    &.category-main-option {
      padding: 0 !important;
      
      &:hover {
        background-color: transparent !important;
        
        .category-main-level {
          background: linear-gradient(135deg, #ecf5ff 0%, #e6f7ff 100%);
          border-color: #409EFF;
        }
      }
    }
    
    &.category-option:hover {
      background-color: #e6f7ff;
    }
    
    &.event-option {
      padding-left: 24px;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
  
  // 年龄选项样式
  .age-option-content {
    display: flex;
    align-items: center;
    
    .age-range {
      margin-left: 8px;
      font-size: 12px;
      color: #909399;
    }
    
    .age-desc {
      margin-left: auto;
      font-size: 11px;
      color: #c0c4cc;
    }
  }
  
  // 加载状态
  .loading-state {
    padding: 12px;
  }
  
  // 无专项提示
  .no-specialty-hint {
    margin-top: 8px;
  }
  
  // 配置预览
  .config-preview {
    margin-top: 16px;
    padding-top: 16px;
    
    .preview-title {
      font-size: 13px;
      color: #606266;
    }
    
    .preview-content {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .no-config {
        color: #c0c4cc;
        font-size: 13px;
        font-style: italic;
      }
    }
  }
  
  // 验证错误
  .validation-errors {
    margin-top: 12px;
    
    .error-item {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  // 布局样式
  &.layout-horizontal {
    .config-section {
      display: inline-block;
      margin-right: 24px;
      margin-bottom: 12px;
      vertical-align: top;
      
      &:last-child {
        margin-right: 0;
      }
      
      // 水平布局时设置固定宽度避免过窄
      .el-select {
        min-width: 320px;  // 从240px增加到320px，与基础布局保持一致
        width: 320px;
      }
    }
  }
  
  &.layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    
    .config-section {
      margin-bottom: 0;
    }
  }
  
  // 紧凑模式
  &.compact {
    .config-section {
      margin-bottom: 12px;
      
      // 紧凑模式下也要保证足够宽度
      .el-select {
        min-width: 280px;  // 从220px增加到280px，确保专项文本完整显示
        width: 100%;
      }
    }
    
    .section-label {
      margin-bottom: 6px;
      font-size: 13px;
    }
  }
  
  // 禁用状态
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.readonly {
    .el-select,
    .el-radio-group,
    .el-button-group {
      pointer-events: none;
    }
  }
  
  // 尺寸变化
  &.size-small {
    .section-label {
      font-size: 13px;
    }
  }
  
  &.size-large {
    .config-section {
      margin-bottom: 24px;
    }
    
    .section-label {
      font-size: 15px;
      margin-bottom: 10px;
    }
  }
}
</style>