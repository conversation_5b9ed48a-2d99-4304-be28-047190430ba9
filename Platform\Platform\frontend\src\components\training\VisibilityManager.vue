<template>
  <div class="visibility-manager">
    <!-- 模板可见性设置 -->
    <el-form-item label="模板可见性" prop="visibility">
      <el-radio-group v-model="localVisibility" @change="onVisibilityChange" class="visibility-options">
        <el-radio value="private" class="visibility-option">
          <div class="option-content">
            <el-icon class="option-icon"><Lock /></el-icon>
            <div class="option-text">
              <span class="option-title">私有</span>
              <span class="option-description">仅自己可见</span>
            </div>
          </div>
        </el-radio>
        
        <el-radio value="organization" class="visibility-option">
          <div class="option-content">
            <el-icon class="option-icon"><Office /></el-icon>
            <div class="option-text">
              <span class="option-title">组织内</span>
              <span class="option-description">组织成员可见</span>
            </div>
          </div>
        </el-radio>
        
        <el-radio value="public" class="visibility-option">
          <div class="option-content">
            <el-icon class="option-icon"><Globe /></el-icon>
            <div class="option-text">
              <span class="option-title">公开</span>
              <span class="option-description">所有用户可见</span>
            </div>
          </div>
        </el-radio>
      </el-radio-group>
    </el-form-item>
    
    <!-- 组织内分享设置 -->
    <el-collapse v-if="localVisibility === 'organization'" class="sharing-settings">
      <el-collapse-item title="分享设置" name="sharing">
        <template #title>
          <el-icon><Share /></el-icon>
          <span class="collapse-title">分享设置</span>
        </template>
        
        <div class="setting-group">
          <el-checkbox 
            v-model="localSharingSettings.allowCopy" 
            @change="onSharingChange"
            class="setting-item"
          >
            <div class="checkbox-content">
              <span class="checkbox-title">允许复制</span>
              <span class="checkbox-description">其他用户可以复制此模板</span>
            </div>
          </el-checkbox>
          
          <el-checkbox 
            v-model="localSharingSettings.allowModify" 
            @change="onSharingChange"
            class="setting-item"
          >
            <div class="checkbox-content">
              <span class="checkbox-title">允许修改</span>
              <span class="checkbox-description">其他用户可以编辑此模板</span>
            </div>
          </el-checkbox>
          
          <el-checkbox 
            v-model="localSharingSettings.requireAttribution" 
            @change="onSharingChange"
            class="setting-item"
          >
            <div class="checkbox-content">
              <span class="checkbox-title">要求署名</span>
              <span class="checkbox-description">使用时需要标注原作者</span>
            </div>
          </el-checkbox>
        </div>
        
        <!-- 分享链接 -->
        <div v-if="shareUrl" class="share-link-section">
          <el-form-item label="分享链接">
            <el-input 
              v-model="shareUrl" 
              readonly 
              class="share-url-input"
            >
              <template #append>
                <el-button @click="copyShareUrl" :icon="DocumentCopy">
                  复制
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </div>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 公开设置 -->
    <el-collapse v-if="localVisibility === 'public'" class="public-settings">
      <el-collapse-item title="公开设置" name="public">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span class="collapse-title">公开设置</span>
        </template>
        
        <div class="setting-group">
          <!-- 公开标签 -->
          <el-form-item label="标签">
            <el-input 
              v-model="localPublicSettings.tags" 
              @change="onPublicChange"
              placeholder="添加标签，用逗号分隔"
              maxlength="200"
              show-word-limit
              class="tags-input"
            >
              <template #prepend>
                <el-icon><Tag /></el-icon>
              </template>
            </el-input>
            <div class="tag-suggestions">
              <el-tag 
                v-for="tag in suggestedTags" 
                :key="tag"
                @click="addSuggestedTag(tag)"
                class="suggested-tag"
                size="small"
                type="info"
              >
                + {{ tag }}
              </el-tag>
            </div>
          </el-form-item>
          
          <!-- 互动设置 -->
          <div class="interaction-settings">
            <el-checkbox 
              v-model="localPublicSettings.allowComments" 
              @change="onPublicChange"
              class="setting-item"
            >
              <div class="checkbox-content">
                <span class="checkbox-title">允许评论</span>
                <span class="checkbox-description">其他用户可以对此模板发表评论</span>
              </div>
            </el-checkbox>
            
            <el-checkbox 
              v-model="localPublicSettings.allowRating" 
              @change="onPublicChange"
              class="setting-item"
            >
              <div class="checkbox-content">
                <span class="checkbox-title">允许评分</span>
                <span class="checkbox-description">其他用户可以对此模板进行评分</span>
              </div>
            </el-checkbox>
            
            <el-checkbox 
              v-model="localPublicSettings.allowFork" 
              @change="onPublicChange"
              class="setting-item"
            >
              <div class="checkbox-content">
                <span class="checkbox-title">允许派生</span>
                <span class="checkbox-description">其他用户可以基于此模板创建新版本</span>
              </div>
            </el-checkbox>
          </div>
          
          <!-- 许可证选择 -->
          <el-form-item label="许可证">
            <el-select 
              v-model="localPublicSettings.license" 
              @change="onPublicChange"
              placeholder="选择许可证类型"
              class="license-select"
            >
              <el-option label="CC BY 4.0 - 署名" value="cc-by-4.0" />
              <el-option label="CC BY-SA 4.0 - 署名-相同方式共享" value="cc-by-sa-4.0" />
              <el-option label="CC BY-NC 4.0 - 署名-非商业性使用" value="cc-by-nc-4.0" />
              <el-option label="MIT License - 开放源代码许可证" value="mit" />
              <el-option label="自定义许可证" value="custom" />
            </el-select>
          </el-form-item>
          
          <!-- 自定义许可证文本 -->
          <el-form-item v-if="localPublicSettings.license === 'custom'" label="许可证内容">
            <el-input 
              v-model="localPublicSettings.customLicense" 
              @change="onPublicChange"
              type="textarea" 
              :rows="4"
              placeholder="请输入自定义许可证内容"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 权限预览 -->
    <div class="permissions-preview">
      <h4 class="preview-title">
        <el-icon><View /></el-icon>
        权限预览
      </h4>
      <div class="permission-items">
        <div 
          v-for="permission in computedPermissions" 
          :key="permission.key"
          class="permission-item"
          :class="{ 'permission-granted': permission.granted }"
        >
          <el-icon :class="permission.granted ? 'text-success' : 'text-info'">
            <Check v-if="permission.granted" />
            <Close v-else />
          </el-icon>
          <span class="permission-text">{{ permission.text }}</span>
        </div>
      </div>
    </div>
    
    <!-- 影响提示 -->
    <el-alert
      v-if="visibilityWarning"
      :title="visibilityWarning.title"
      :type="visibilityWarning.type"
      :description="visibilityWarning.description"
      show-icon
      :closable="false"
      class="visibility-warning"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Lock,
  Office,
  Globe,
  Share,
  Setting,
  Tag,
  DocumentCopy,
  View,
  Check,
  Close
} from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue?: string
  sharingSettings?: SharingSettings
  publicSettings?: PublicSettings
  templateId?: number
  disabled?: boolean
}

interface SharingSettings {
  allowCopy: boolean
  allowModify: boolean
  requireAttribution: boolean
}

interface PublicSettings {
  tags: string
  allowComments: boolean
  allowRating: boolean
  allowFork: boolean
  license: string
  customLicense?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 'private',
  sharingSettings: () => ({
    allowCopy: false,
    allowModify: false,
    requireAttribution: true
  }),
  publicSettings: () => ({
    tags: '',
    allowComments: true,
    allowRating: true,
    allowFork: true,
    license: 'cc-by-4.0',
    customLicense: ''
  }),
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:sharingSettings': [value: SharingSettings]
  'update:publicSettings': [value: PublicSettings]
  'visibilityChange': [visibility: string]
}>()

// Local state
const localVisibility = ref(props.modelValue)
const localSharingSettings = ref<SharingSettings>({ ...props.sharingSettings })
const localPublicSettings = ref<PublicSettings>({ ...props.publicSettings })

// 分享链接
const shareUrl = computed(() => {
  if (!props.templateId || localVisibility.value !== 'organization') {
    return ''
  }
  return `${window.location.origin}/templates/share/${props.templateId}`
})

// 建议的标签
const suggestedTags = ref(['力量训练', '有氧运动', '速度训练', '耐力训练', '恢复训练', '技术训练'])

// 权限预览
const computedPermissions = computed(() => {
  const visibility = localVisibility.value
  const sharing = localSharingSettings.value
  const publicSettings = localPublicSettings.value
  
  return [
    {
      key: 'view',
      text: '其他用户可以查看',
      granted: visibility !== 'private'
    },
    {
      key: 'copy',
      text: '其他用户可以复制',
      granted: visibility === 'public' || (visibility === 'organization' && sharing.allowCopy)
    },
    {
      key: 'modify',
      text: '其他用户可以修改',
      granted: visibility === 'organization' && sharing.allowModify
    },
    {
      key: 'comment',
      text: '其他用户可以评论',
      granted: visibility === 'public' && publicSettings.allowComments
    },
    {
      key: 'rate',
      text: '其他用户可以评分',
      granted: visibility === 'public' && publicSettings.allowRating
    },
    {
      key: 'fork',
      text: '其他用户可以派生',
      granted: visibility === 'public' && publicSettings.allowFork
    }
  ]
})

// 可见性警告
const visibilityWarning = computed(() => {
  const visibility = localVisibility.value
  
  switch (visibility) {
    case 'organization':
      return {
        title: '组织内可见',
        type: 'warning' as const,
        description: '此模板将对您所在组织的所有成员可见。请确保内容适合组织内分享。'
      }
    case 'public':
      return {
        title: '公开可见',
        type: 'error' as const,
        description: '此模板将对所有用户公开可见。一旦公开，您将无法完全撤回。请谨慎操作。'
      }
    default:
      return null
  }
})

// Watch props changes
watch(() => props.modelValue, (newVal) => {
  localVisibility.value = newVal
})

watch(() => props.sharingSettings, (newVal) => {
  localSharingSettings.value = { ...newVal }
}, { deep: true })

watch(() => props.publicSettings, (newVal) => {
  localPublicSettings.value = { ...newVal }
}, { deep: true })

// Event handlers
const onVisibilityChange = (value: string) => {
  emit('update:modelValue', value)
  emit('visibilityChange', value)
  
  // 自动调整设置
  if (value === 'public') {
    localPublicSettings.value.allowComments = true
    localPublicSettings.value.allowRating = true
    onPublicChange()
  }
}

const onSharingChange = () => {
  emit('update:sharingSettings', localSharingSettings.value)
}

const onPublicChange = () => {
  emit('update:publicSettings', localPublicSettings.value)
}

const copyShareUrl = async () => {
  try {
    await navigator.clipboard.writeText(shareUrl.value)
    ElMessage.success('分享链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const addSuggestedTag = (tag: string) => {
  const currentTags = localPublicSettings.value.tags
  if (!currentTags.includes(tag)) {
    localPublicSettings.value.tags = currentTags 
      ? `${currentTags}, ${tag}` 
      : tag
    onPublicChange()
  }
}
</script>

<style lang="scss" scoped>
.visibility-manager {
  .visibility-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .visibility-option {
    margin-right: 0;
    margin-bottom: 0;
    
    :deep(.el-radio__label) {
      width: 100%;
      padding-left: 8px;
    }
  }
  
  .option-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .option-icon {
    font-size: 20px;
    color: var(--el-text-color-regular);
  }
  
  .option-text {
    display: flex;
    flex-direction: column;
    
    .option-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 1.4;
    }
    
    .option-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.2;
    }
  }
  
  .sharing-settings,
  .public-settings {
    margin-top: 16px;
    
    :deep(.el-collapse-item__header) {
      background-color: var(--el-fill-color-lighter);
      padding: 12px 16px;
      border-radius: 6px;
    }
    
    :deep(.el-collapse-item__content) {
      padding: 16px 0;
    }
  }
  
  .collapse-title {
    margin-left: 8px;
    font-weight: 500;
  }
  
  .setting-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .setting-item {
    margin-right: 0;
    
    :deep(.el-checkbox__label) {
      width: 100%;
    }
  }
  
  .checkbox-content {
    display: flex;
    flex-direction: column;
    
    .checkbox-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 1.4;
    }
    
    .checkbox-description {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.2;
      margin-top: 2px;
    }
  }
  
  .share-link-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
  
  .share-url-input {
    :deep(.el-input__inner) {
      font-family: monospace;
      font-size: 12px;
    }
  }
  
  .tags-input {
    :deep(.el-input-group__prepend) {
      background-color: var(--el-fill-color-light);
    }
  }
  
  .tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
  }
  
  .suggested-tag {
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: white;
    }
  }
  
  .interaction-settings {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .license-select {
    width: 100%;
  }
  
  .permissions-preview {
    margin-top: 24px;
    padding: 16px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;
  }
  
  .preview-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .permission-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    
    &.permission-granted {
      .permission-text {
        color: var(--el-color-success);
      }
    }
    
    .permission-text {
      color: var(--el-text-color-regular);
    }
  }
  
  .visibility-warning {
    margin-top: 16px;
  }
  
  .text-success {
    color: var(--el-color-success);
  }
  
  .text-info {
    color: var(--el-text-color-regular);
  }
}
</style>