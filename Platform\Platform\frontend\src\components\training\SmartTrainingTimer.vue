<!--
Smart Training Timer - 智能训练倒计时器
支持灵活的时间控制、补救功能和各种训练场景
-->

<template>
  <div class="smart-training-timer" :class="timerModeClass">
    <!-- 主要显示区域 -->
    <div class="timer-display">
      <div class="timer-status">
        <h3>{{ currentPhaseText }}</h3>
        <span class="exercise-info">{{ currentExercise.name }} - 第{{ currentSet }}组</span>
      </div>

      <!-- 倒计时显示 -->
      <div class="countdown-display">
        <div class="time-circle">
          <svg class="progress-ring" :width="120" :height="120">
            <circle
              class="progress-ring-circle"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
              cx="60"
              cy="60"
              r="52"
              fill="transparent"
              stroke="currentColor"
              stroke-width="8"
            />
          </svg>
          <div class="time-text">
            {{ formatTime(remainingTime) }}
          </div>
        </div>
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicators">
        <div class="phase-indicator" :class="currentPhase">
          <span v-if="currentPhase === 'exercise'">🏃 执行中</span>
          <span v-else-if="currentPhase === 'rest'">💤 休息中</span>
          <span v-else-if="currentPhase === 'overtime'">⏰ 超时休息</span>
          <span v-else>⏸️ 暂停中</span>
        </div>
        
        <!-- 超时提醒 -->
        <div v-if="isOvertime" class="overtime-alert">
          <el-alert
            title="休息时间已超过预设"
            type="warning"
            :description="`已超时 ${formatTime(overtimeSeconds)} 秒`"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>

    <!-- 控制按钮区域 -->
    <div class="timer-controls">
      <!-- 主要操作按钮 -->
      <div class="primary-controls">
        <el-button
          v-if="timerState === 'stopped'"
          type="primary"
          size="large"
          @click="startTimer"
          :loading="isTransitioning"
        >
          <el-icon><VideoPlay /></el-icon>
          开始{{ currentPhase === 'exercise' ? '训练' : '休息' }}
        </el-button>

        <el-button
          v-else-if="timerState === 'running'"
          type="warning"
          size="large"
          @click="pauseTimer"
        >
          <el-icon><VideoPause /></el-icon>
          暂停
        </el-button>

        <el-button
          v-else-if="timerState === 'paused'"
          type="primary"
          size="large"
          @click="resumeTimer"
        >
          <el-icon><VideoPlay /></el-icon>
          继续
        </el-button>

        <!-- 完成当前阶段 -->
        <el-button
          v-if="currentPhase === 'exercise'"
          type="success"
          size="large"
          @click="completeCurrentExercise"
        >
          <el-icon><Check /></el-icon>
          完成本组
        </el-button>

        <el-button
          v-else-if="currentPhase === 'rest' || isOvertime"
          type="success"
          size="large"
          @click="completeCurrentRest"
        >
          <el-icon><Check /></el-icon>
          结束休息
        </el-button>
      </div>

      <!-- 补救和调整按钮 -->
      <div class="rescue-controls">
        <el-button-group>
          <!-- 时间调整 -->
          <el-button 
            size="small" 
            @click="adjustTime(-30)"
            :disabled="timerState !== 'running'"
          >
            -30s
          </el-button>
          <el-button 
            size="small" 
            @click="adjustTime(30)"
            :disabled="timerState !== 'running'"
          >
            +30s
          </el-button>
          <el-button 
            size="small" 
            @click="adjustTime(60)"
            :disabled="timerState !== 'running'"
          >
            +1分
          </el-button>
        </el-button-group>

        <!-- 误操作补救 -->
        <el-button 
          v-if="canUndo"
          type="info"
          size="small"
          @click="undoLastAction"
        >
          <el-icon><RefreshLeft /></el-icon>
          撤销
        </el-button>

        <!-- 重新开始 -->
        <el-button 
          type="danger"
          size="small"
          @click="showRestartDialog = true"
        >
          <el-icon><Refresh /></el-icon>
          重新开始
        </el-button>

        <!-- 跳过当前阶段 -->
        <el-button 
          size="small"
          @click="skipCurrentPhase"
        >
          <el-icon><DArrowRight /></el-icon>
          跳过
        </el-button>
      </div>
    </div>

    <!-- 训练进度信息 -->
    <div class="training-progress">
      <div class="exercise-details">
        <span>目标: {{ currentExercise.targetSets }}组 x {{ currentExercise.targetReps }}次</span>
        <span>休息: {{ currentExercise.targetRestTime }}秒</span>
      </div>
      <el-progress 
        :percentage="exerciseProgress" 
        :format="formatProgress"
        :stroke-width="6"
      />
    </div>

    <!-- 确认对话框 -->
    <el-dialog
      v-model="showRestartDialog"
      title="重新开始确认"
      width="400px"
      center
    >
      <p>确定要重新开始当前{{ currentPhase === 'exercise' ? '训练' : '休息' }}吗？</p>
      <p style="color: #f56c6c; font-size: 12px;">此操作将清除当前进度数据</p>
      
      <template #footer>
        <el-button @click="showRestartDialog = false">取消</el-button>
        <el-button type="danger" @click="restartCurrentPhase">确认重新开始</el-button>
      </template>
    </el-dialog>

    <!-- 手动时间设置对话框 -->
    <el-dialog
      v-model="showTimeAdjustDialog"
      title="手动设置时间"
      width="350px"
      center
    >
      <el-form>
        <el-form-item label="设置时间（秒）">
          <el-input-number
            v-model="manualTime"
            :min="1"
            :max="3600"
            :step="30"
            controls-position="right"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showTimeAdjustDialog = false">取消</el-button>
        <el-button type="primary" @click="setManualTime">确认设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

// Props
interface Props {
  currentExercise: {
    name: string
    targetSets: number
    targetReps: number
    targetRestTime: number
    targetExerciseTime?: number
  }
  currentSet: number
}

const props = defineProps<Props>()

// 倒计时状态
const timerState = ref<'stopped' | 'running' | 'paused'>('stopped')
const currentPhase = ref<'exercise' | 'rest' | 'overtime'>('exercise')
const remainingTime = ref(0)
const originalTime = ref(0)
const overtimeSeconds = ref(0)
const isTransitioning = ref(false)

// 历史操作记录（用于撤销）
const actionHistory = ref<Array<{
  action: string
  timestamp: number
  previousState: any
}>>([])

// 对话框控制
const showRestartDialog = ref(false)
const showTimeAdjustDialog = ref(false)
const manualTime = ref(60)

// 计时器
let timerInterval: NodeJS.Timeout | null = null

// 计算属性
const currentPhaseText = computed(() => {
  switch (currentPhase.value) {
    case 'exercise':
      return '训练执行'
    case 'rest':
      return '组间休息'
    case 'overtime':
      return '超时休息'
    default:
      return '准备就绪'
  }
})

const timerModeClass = computed(() => ({
  'exercise-mode': currentPhase.value === 'exercise',
  'rest-mode': currentPhase.value === 'rest',
  'overtime-mode': currentPhase.value === 'overtime',
  'paused-mode': timerState.value === 'paused'
}))

const isOvertime = computed(() => currentPhase.value === 'overtime')

const canUndo = computed(() => actionHistory.value.length > 0)

const exerciseProgress = computed(() => {
  if (currentPhase.value === 'exercise') {
    return ((props.currentSet - 1) / props.currentExercise.targetSets) * 100
  } else {
    return (props.currentSet / props.currentExercise.targetSets) * 100
  }
})

// 进度环计算
const circumference = computed(() => 2 * Math.PI * 52)
const progressOffset = computed(() => {
  const progress = originalTime.value > 0 ? (originalTime.value - remainingTime.value) / originalTime.value : 0
  return circumference.value * (1 - progress)
})

// 方法
const startTimer = () => {
  saveActionHistory('start_timer')
  
  if (currentPhase.value === 'exercise') {
    remainingTime.value = props.currentExercise.targetExerciseTime || 120
  } else {
    remainingTime.value = props.currentExercise.targetRestTime
  }
  
  originalTime.value = remainingTime.value
  timerState.value = 'running'
  startCountdown()
}

const pauseTimer = () => {
  saveActionHistory('pause_timer')
  timerState.value = 'paused'
  stopCountdown()
}

const resumeTimer = () => {
  saveActionHistory('resume_timer')
  timerState.value = 'running'
  startCountdown()
}

const startCountdown = () => {
  if (timerInterval) clearInterval(timerInterval)
  
  timerInterval = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      handleTimerFinished()
    }
  }, 1000)
}

const stopCountdown = () => {
  if (timerInterval) {
    clearInterval(timerInterval)
    timerInterval = null
  }
}

const handleTimerFinished = () => {
  stopCountdown()
  timerState.value = 'stopped'
  
  if (currentPhase.value === 'exercise') {
    // 训练时间结束，提醒用户
    ElNotification({
      title: '训练时间结束',
      message: '请确认是否完成本组训练',
      type: 'success',
      duration: 0,
      showClose: true
    })
    playNotificationSound()
  } else {
    // 休息时间结束，进入超时模式
    currentPhase.value = 'overtime'
    startOvertimeCounter()
    ElNotification({
      title: '休息时间结束',
      message: '可以开始下一组训练了',
      type: 'warning',
      duration: 5000
    })
    playNotificationSound()
  }
}

const startOvertimeCounter = () => {
  overtimeSeconds.value = 0
  timerInterval = setInterval(() => {
    overtimeSeconds.value++
  }, 1000)
}

const completeCurrentExercise = () => {
  saveActionHistory('complete_exercise')
  stopCountdown()
  
  // 记录实际数据
  emit('exerciseCompleted', {
    set: props.currentSet,
    actualTime: originalTime.value - remainingTime.value,
    completedAt: new Date()
  })
  
  // 切换到休息阶段
  currentPhase.value = 'rest'
  timerState.value = 'stopped'
  
  ElMessage.success('本组训练完成！')
}

const completeCurrentRest = () => {
  saveActionHistory('complete_rest')
  stopCountdown()
  
  // 记录休息数据
  const actualRestTime = currentPhase.value === 'overtime' 
    ? props.currentExercise.targetRestTime + overtimeSeconds.value
    : props.currentExercise.targetRestTime - remainingTime.value
    
  emit('restCompleted', {
    set: props.currentSet,
    actualRestTime,
    wasOvertime: currentPhase.value === 'overtime',
    completedAt: new Date()
  })
  
  // 切换到下一组或下一个动作
  currentPhase.value = 'exercise'
  timerState.value = 'stopped'
  overtimeSeconds.value = 0
  
  ElMessage.success('休息结束，准备下一组！')
}

const adjustTime = (seconds: number) => {
  saveActionHistory('adjust_time', { adjustment: seconds })
  
  const newTime = remainingTime.value + seconds
  if (newTime >= 0) {
    remainingTime.value = newTime
    ElMessage.info(`时间${seconds > 0 ? '增加' : '减少'}了 ${Math.abs(seconds)} 秒`)
  }
}

const skipCurrentPhase = () => {
  saveActionHistory('skip_phase')
  
  ElMessageBox.confirm(
    `确定跳过当前${currentPhaseText.value}吗？`,
    '跳过确认',
    {
      type: 'warning'
    }
  ).then(() => {
    if (currentPhase.value === 'exercise') {
      completeCurrentExercise()
    } else {
      completeCurrentRest()
    }
  })
}

const undoLastAction = () => {
  if (actionHistory.value.length === 0) return
  
  const lastAction = actionHistory.value.pop()!
  
  // 根据不同操作类型恢复状态
  switch (lastAction.action) {
    case 'start_timer':
      timerState.value = 'stopped'
      stopCountdown()
      break
    case 'pause_timer':
      timerState.value = 'running'
      startCountdown()
      break
    case 'adjust_time':
      remainingTime.value -= lastAction.previousState.adjustment
      break
    // ... 其他撤销逻辑
  }
  
  ElMessage.info('已撤销上次操作')
}

const restartCurrentPhase = () => {
  saveActionHistory('restart_phase')
  
  stopCountdown()
  timerState.value = 'stopped'
  remainingTime.value = 0
  overtimeSeconds.value = 0
  
  if (currentPhase.value === 'overtime') {
    currentPhase.value = 'rest'
  }
  
  showRestartDialog.value = false
  ElMessage.info('已重新开始')
}

const setManualTime = () => {
  saveActionHistory('set_manual_time')
  
  remainingTime.value = manualTime.value
  originalTime.value = manualTime.value
  showTimeAdjustDialog.value = false
  
  ElMessage.success(`时间已设置为 ${manualTime.value} 秒`)
}

const saveActionHistory = (action: string, data?: any) => {
  actionHistory.value.push({
    action,
    timestamp: Date.now(),
    previousState: data || {}
  })
  
  // 保持历史记录在合理范围内
  if (actionHistory.value.length > 10) {
    actionHistory.value.shift()
  }
}

const playNotificationSound = () => {
  // 播放提示音
  const audio = new Audio('/sounds/timer-notification.mp3')
  audio.play().catch(() => {
    // 如果无法播放音频，使用浏览器默认提示音
    console.log('Timer finished!')
  })
}

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatProgress = (percentage: number) => {
  return `${props.currentSet}/${props.currentExercise.targetSets}组`
}

// Events
const emit = defineEmits<{
  exerciseCompleted: [data: any]
  restCompleted: [data: any]
}>()

// 生命周期
onUnmounted(() => {
  stopCountdown()
})

// 监听键盘快捷键
const handleKeyPress = (event: KeyboardEvent) => {
  switch (event.code) {
    case 'Space':
      event.preventDefault()
      if (timerState.value === 'running') {
        pauseTimer()
      } else if (timerState.value === 'paused') {
        resumeTimer()
      } else {
        startTimer()
      }
      break
    case 'Enter':
      event.preventDefault()
      if (currentPhase.value === 'exercise') {
        completeCurrentExercise()
      } else {
        completeCurrentRest()
      }
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyPress)
})
</script>

<style scoped>
.smart-training-timer {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.exercise-mode {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.rest-mode {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.overtime-mode {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d45087;
}

.paused-mode {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #667eea;
}

.timer-display {
  text-align: center;
  margin-bottom: 30px;
}

.timer-status h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
}

.exercise-info {
  font-size: 14px;
  opacity: 0.8;
}

.countdown-display {
  margin: 30px 0;
  position: relative;
}

.time-circle {
  position: relative;
  display: inline-block;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.35s;
}

.time-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.status-indicators {
  margin: 20px 0;
}

.phase-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.overtime-alert {
  margin-top: 10px;
}

.timer-controls {
  margin: 30px 0;
}

.primary-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 15px;
}

.rescue-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.training-progress {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.exercise-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  opacity: 0.8;
}

/* 响应式 */
@media (max-width: 480px) {
  .smart-training-timer {
    margin: 10px;
    padding: 15px;
  }
  
  .primary-controls {
    flex-direction: column;
  }
  
  .rescue-controls {
    flex-direction: column;
  }
}
</style>