<!--
  时间指标显示控制开关组件
  Time Metrics Toggle Component
-->

<template>
  <div class="time-metrics-toggle" data-testid="time-metrics-toggle">
    <div class="toggle-container">
      <!-- 开关主体 -->
      <el-switch
        v-model="showTimeMetrics"
        :active-text="activeText"
        :inactive-text="inactiveText"
        size="small"
        :inline-prompt="false"
        @change="handleToggleChange"
        data-testid="time-metrics-switch"
      />
      
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="{ active: showTimeMetrics }">
        <el-icon class="status-icon">
          <Clock v-if="showTimeMetrics" />
          <Hide v-else />
        </el-icon>
      </div>
      
      <!-- 统计信息（可选显示） -->
      <div v-if="showStats && totalMetrics > 0" class="metrics-stats">
        <el-tag 
          :type="showTimeMetrics ? 'success' : 'info'" 
          size="small"
          effect="plain"
        >
          {{ getStatsText() }}
        </el-tag>
      </div>
    </div>
    
    <!-- 帮助提示（可选） -->
    <el-tooltip
      v-if="showTooltip"
      :content="tooltipContent"
      placement="top"
      :show-arrow="false"
    >
      <el-icon class="help-icon">
        <QuestionFilled />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Clock, Hide, QuestionFilled } from '@element-plus/icons-vue'
import { useUnifiedMetricsState } from '@/composables/useUnifiedMetricsState'

interface Props {
  // 自定义激活/非激活状态文案
  activeText?: string
  inactiveText?: string
  
  // 是否显示统计信息
  showStats?: boolean
  
  // 是否显示帮助提示
  showTooltip?: boolean
  
  // 指标总数（用于显示统计）
  totalMetrics?: number
  
  // 时间指标数量（用于显示统计）
  timeMetricsCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  activeText: '是',
  inactiveText: '否',
  showStats: false,
  showTooltip: true,
  totalMetrics: 0,
  timeMetricsCount: 9
})

const emit = defineEmits<{
  change: [value: boolean]
}>()

// 使用统一指标状态管理
const { showTimeMetrics, TIME_METRIC_IDS, toggleTimeMetrics } = useUnifiedMetricsState()

// 计算属性
const tooltipContent = computed(() => {
  if (showTimeMetrics.value) {
    return `已开启时间指标统计，显示 ${props.timeMetricsCount} 个时间相关指标`
  } else {
    return `已关闭时间指标统计，隐藏 ${props.timeMetricsCount} 个时间相关指标`
  }
})

// 获取统计文本
const getStatsText = () => {
  if (showTimeMetrics.value) {
    return `显示时间指标 ${props.timeMetricsCount}/${props.totalMetrics}`
  } else {
    return `隐藏时间指标 ${props.timeMetricsCount}/${props.totalMetrics}`
  }
}

// 处理开关状态变化
const handleToggleChange = (value: boolean) => {
  console.log(`时间指标开关状态变化: ${value}`)
  // 使用统一状态管理的切换方法
  // toggleTimeMetrics会自动处理状态更新和时间指标清理
  emit('change', value)
}
</script>

<style scoped>
.time-metrics-toggle {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.time-metrics-toggle:hover {
  background: var(--el-bg-color);
  border-color: var(--el-color-primary-light-7);
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  display: flex;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background: var(--el-color-info-light-8);
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: var(--el-color-success-light-8);
}

.status-icon {
  font-size: 14px;
  color: var(--el-color-info);
  margin: auto;
  transition: color 0.3s ease;
}

.status-indicator.active .status-icon {
  color: var(--el-color-success);
}

.metrics-stats {
  margin-left: 4px;
}

.help-icon {
  font-size: 16px;
  color: var(--el-color-info-light-3);
  cursor: help;
  transition: color 0.3s ease;
}

.help-icon:hover {
  color: var(--el-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-metrics-toggle {
    padding: 2px 6px;
    gap: 4px;
  }
  
  .toggle-container {
    gap: 4px;
  }
  
  .status-indicator {
    width: 18px;
    height: 18px;
  }
  
  .status-icon {
    font-size: 12px;
  }
  
  .metrics-stats {
    display: none; /* 移动端隐藏统计信息 */
  }
}

/* 主题适配 */
.athletics-orange-theme .time-metrics-toggle {
  border-color: var(--orange-border, #f4a261);
}

.athletics-orange-theme .time-metrics-toggle:hover {
  border-color: var(--orange-primary, #e76f51);
  background: var(--orange-bg-light, #fef7f0);
}

.athletics-orange-theme .status-indicator.active {
  background: var(--orange-success-bg, #e8f5e8);
}

.athletics-orange-theme .status-indicator.active .status-icon {
  color: var(--orange-success, #52c41a);
}
</style>