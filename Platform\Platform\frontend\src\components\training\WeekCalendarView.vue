<template>
  <div class="week-calendar-view">
    <!-- 周历头部 -->
    <div class="calendar-header" :class="{ 'first-cycle-header': isFirstCycle }">
      <div class="week-info">
        <h4>
          第 {{ currentWeek + 1 }} 小周期 ({{ formatCycleRange() }})
          <el-tag v-if="isFirstCycle" type="primary" size="small" class="cycle-badge">
            <el-icon><Edit /></el-icon>
            可编辑模板
          </el-tag>
          <el-tag v-else type="info" size="small" class="cycle-badge">
            <el-icon><Lock /></el-icon>
            自动同步
          </el-tag>
        </h4>
        <div class="week-stats">
          <span class="stat-item">
            <el-icon><TrendCharts /></el-icon>
            平均负荷: {{ getAverageLoad() }}
          </span>
          <span class="stat-item">
            <el-icon><Clock /></el-icon>
            训练天数: {{ getTrainingDays() }}/{{ props.cycle.trainingDays || 7 }}
          </span>
        </div>
      </div>
    </div>

    <!-- 周历网格 -->
    <div class="calendar-body" :style="{ gridTemplateColumns: `repeat(${weekDays.length}, 1fr)` }">
      <!-- 星期头部 -->
      <div class="weekdays" :style="{ gridTemplateColumns: `repeat(${weekDays.length}, 1fr)` }">
        <div 
          v-for="day in weekDays" 
          :key="day.name"
          class="weekday-header"
        >
          <span class="day-name">{{ day.name }}</span>
          <span class="day-abbr">{{ day.abbr }}</span>
        </div>
      </div>

      <!-- 日期格子 -->
      <div 
        v-for="(day, index) in weekData" 
        :key="day.date"
        class="day-cell"
        :class="{
          'drag-over': dragOverDate === day.date && isFirstCycle,
          'has-plan': day.dailyPlan,
          'is-rest-day': day.isRestDay,
          'is-weekend': isWeekend(index),
          'read-only': !isFirstCycle
        }"
        @dragover.prevent="isFirstCycle && handleDragOver($event, day.date)"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="isFirstCycle && handleDrop($event, day.date)"
      >
        <div class="day-header">
          <span class="day-number">{{ formatDayNumber(day.date) }}</span>
          <div class="day-actions" v-if="isFirstCycle">
            <el-dropdown @command="(cmd) => handleDayCommand(cmd, day.date)">
              <el-icon class="more-icon"><More /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="rest">
                    <el-icon><Coffee /></el-icon>
                    设置休息日
                  </el-dropdown-item>
                  <el-dropdown-item command="add">
                    <el-icon><Plus /></el-icon>
                    添加训练
                  </el-dropdown-item>
                  <el-dropdown-item command="note">
                    <el-icon><Document /></el-icon>
                    添加备注
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="lock-indicator" v-else>
            <el-icon class="lock-icon" :size="14"><Lock /></el-icon>
          </div>
        </div>

        <div class="day-content">
          <!-- 训练计划卡片 -->
          <div v-if="day.dailyPlan && !day.isRestDay" class="plan-card">
            <div class="plan-header">
              <h5 class="plan-name">{{ day.dailyPlan.name }}</h5>
              <div class="plan-badges">
                <span 
                  class="intensity-badge"
                  :class="`intensity-${getIntensityLevel(day.dailyPlan.intensity)}`"
                >
                  {{ day.dailyPlan.intensity }}%
                </span>
              </div>
            </div>
            <div class="plan-details">
              <span class="plan-duration">{{ day.dailyPlan.duration }}分钟</span>
              <span class="plan-category">{{ day.dailyPlan.category }}</span>
            </div>
            <div class="plan-progress" v-if="day.completion">
              <div 
                class="progress-bar"
                :style="{ width: `${day.completion}%` }"
              ></div>
            </div>
          </div>

          <!-- 休息日卡片 -->
          <div v-else-if="day.isRestDay" class="rest-card">
            <el-icon class="rest-icon"><Coffee /></el-icon>
            <span class="rest-text">休息日</span>
          </div>

          <!-- 空白卡片 -->
          <div v-else class="empty-card">
            <div class="drop-zone" v-if="isFirstCycle">
              <el-icon class="drop-icon"><Plus /></el-icon>
              <span class="drop-text">拖拽训练日到此处</span>
            </div>
            <div class="sync-zone" v-else>
              <el-icon class="sync-icon"><Lock /></el-icon>
              <span class="sync-text">将从第1周期自动同步</span>
            </div>
          </div>

          <!-- 备注 -->
          <div v-if="day.notes" class="day-notes">
            <small>{{ day.notes }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- 负荷热力图 -->
    <div class="load-heatmap">
      <div class="heatmap-header">
        <h5>本周负荷分布</h5>
      </div>
      <div class="heatmap-bar">
        <div 
          v-for="(day, index) in weekData" 
          :key="day.date"
          class="heatmap-cell"
          :class="`load-${getLoadLevel(day.dailyPlan?.intensity || 0)}`"
          :title="`${formatDayNumber(day.date)}: ${day.dailyPlan?.intensity || 0}%`"
        >
          <div class="heatmap-value">{{ day.dailyPlan?.intensity || 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElTag } from 'element-plus'
import { 
  TrendCharts, Clock, More, Coffee, Plus, Document, Edit, Lock
} from '@element-plus/icons-vue'

// Props
interface Props {
  cycle: {
    startDate: string
    endDate: string
    trainingDays?: number
    cycleRepeats?: number
    assignments: Array<{
      date: string
      dailyPlanId?: string
      isRestDay: boolean
      notes?: string
      weekOfCycle?: number
      dayOfCycle?: number
      isFirstCycle?: boolean
    }>
  }
  currentWeek: number
  dailyPlansData?: Map<string, any>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'plan-dropped': [data: { date: string, plan: any }]
  'plan-removed': [date: string]
  'plan-edited': [data: { date: string, plan: any }]
}>()

// 响应式数据
const dragOverDate = ref<string | null>(null)

// 计算是否为第一周期
const isFirstCycle = computed(() => {
  return props.currentWeek === 0
})

// 星期配置
const allWeekDays = [
  { name: '周一', abbr: 'M' },
  { name: '周二', abbr: 'T' },
  { name: '周三', abbr: 'W' },
  { name: '周四', abbr: 'T' },
  { name: '周五', abbr: 'F' },
  { name: '周六', abbr: 'S' },
  { name: '周日', abbr: 'S' }
]

// 动态生成周期天数配置
const weekDays = computed(() => {
  const cycleLength = props.cycle.trainingDays || 7
  const cycleDays = []
  
  for (let i = 0; i < cycleLength; i++) {
    if (i < allWeekDays.length) {
      cycleDays.push(allWeekDays[i])
    } else {
      // 如果超过7天，使用数字标识
      cycleDays.push({ name: `第${i+1}天`, abbr: `${i+1}` })
    }
  }
  
  return cycleDays
})

// 计算属性
const weekData = computed(() => {
  const startDate = new Date(props.cycle.startDate)
  const cycleLength = props.cycle.trainingDays || 7
  const cycleStart = new Date(startDate)
  cycleStart.setDate(startDate.getDate() + props.currentWeek * cycleLength)
  
  const cycleDays = []
  for (let i = 0; i < cycleLength; i++) {
    const currentDate = new Date(cycleStart)
    currentDate.setDate(cycleStart.getDate() + i)
    const dateString = currentDate.toISOString().split('T')[0]
    
    // 查找对应的任务分配
    const assignment = props.cycle.assignments.find(a => a.date === dateString)
    
    cycleDays.push({
      date: dateString,
      dailyPlan: assignment?.dailyPlanId ? getRealDailyPlan(assignment.dailyPlanId) : null,
      isRestDay: assignment?.isRestDay || false,
      notes: assignment?.notes || '',
      completion: Math.floor(Math.random() * 100) // 模拟完成度
    })
  }
  
  return cycleDays
})

// 工具方法
const getRealDailyPlan = (planId: string) => {
  // 从父组件传递的真实数据中获取训练日计划
  if (props.dailyPlansData && props.dailyPlansData.has(planId)) {
    return props.dailyPlansData.get(planId)
  }
  
  // 如果没有找到真实数据，返回null
  return null
}

const formatCycleRange = () => {
  if (weekData.value.length === 0) return ''
  const start = new Date(weekData.value[0].date)
  const end = new Date(weekData.value[weekData.value.length - 1].date)
  return `${start.getMonth() + 1}/${start.getDate()} - ${end.getMonth() + 1}/${end.getDate()}`
}

const formatDayNumber = (dateString: string) => {
  return new Date(dateString).getDate()
}

const getAverageLoad = () => {
  const loads = weekData.value
    .filter(day => day.dailyPlan && !day.isRestDay)
    .map(day => day.dailyPlan?.intensity || 0)
  
  if (loads.length === 0) return 0
  return Math.round(loads.reduce((sum, load) => sum + load, 0) / loads.length)
}

const getTrainingDays = () => {
  return weekData.value.filter(day => day.dailyPlan && !day.isRestDay).length
}

const isWeekend = (dayIndex: number) => {
  return dayIndex === 5 || dayIndex === 6 // 周六、周日
}

const getIntensityLevel = (intensity: number) => {
  if (intensity >= 80) return 'high'
  if (intensity >= 60) return 'medium'
  if (intensity >= 40) return 'low'
  return 'minimal'
}

const getLoadLevel = (intensity: number) => {
  if (intensity >= 80) return 'high'
  if (intensity >= 60) return 'medium'  
  if (intensity >= 40) return 'low'
  return 'none'
}

// 拖拽处理
const handleDragOver = (event: DragEvent, date: string) => {
  // 只有第一周期才允许拖拽
  if (!isFirstCycle.value) {
    event.preventDefault()
    event.dataTransfer!.dropEffect = 'none'
    return
  }
  dragOverDate.value = date
}

const handleDragLeave = () => {
  dragOverDate.value = null
}

const handleDrop = (event: DragEvent, date: string) => {
  dragOverDate.value = null
  
  // 只有第一周期才允许放置
  if (!isFirstCycle.value) {
    ElMessage.warning({
      message: '只能在第1周期编辑训练计划，其他周期会自动同步',
      duration: 3000
    })
    return
  }
  
  try {
    const planData = JSON.parse(event.dataTransfer?.getData('text/plain') || '{}')
    emit('plan-dropped', { date, plan: planData })
  } catch (error) {
    ElMessage.error('拖拽数据解析失败')
  }
}

// 日期操作
const handleDayCommand = (command: string, date: string) => {
  // 只有第一周期才允许操作
  if (!isFirstCycle.value) {
    ElMessage.warning({
      message: '只能在第1周期进行编辑操作',
      duration: 2000
    })
    return
  }
  
  switch (command) {
    case 'rest':
      // 设置为休息日逻辑
      ElMessage.success(`已将 ${formatDayNumber(date)} 号设置为休息日`)
      break
    case 'add':
      // 添加训练逻辑
      ElMessage.info('打开训练选择器...')
      break
    case 'note':
      // 添加备注逻辑
      ElMessage.info('添加备注...')
      break
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.week-calendar-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: system-ui, -apple-system, sans-serif;
}

.calendar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.week-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.week-info h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.week-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.calendar-body {
  flex: 1;
  display: grid;
  /* grid-template-columns 通过 :style 动态设置，移除硬编码 */
  grid-template-rows: auto 1fr;
  gap: 1px;
  background: #f0f2f5;
  padding: 1px;
}

.weekdays {
  grid-column: 1 / -1;
  display: grid;
  /* grid-template-columns 通过 :style 动态设置，移除硬编码 */
  background: #fafafa;
}

.weekday-header {
  padding: 12px;
  text-align: center;
  background: white;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
}

.day-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.day-abbr {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.day-cell {
  background: white;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
}

.day-cell.drag-over {
  background: #f0f9ff;
  transform: scale(1.02);
}

.day-cell.is-weekend {
  background: #fafbfc;
}

.day-cell.has-plan {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.day-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.more-icon {
  cursor: pointer;
  color: #c0c4cc;
  transition: color 0.2s;
}

.more-icon:hover {
  color: #409eff;
}

.day-content {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.plan-card {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.plan-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.intensity-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
}

.plan-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  opacity: 0.9;
}

.plan-progress {
  margin-top: 8px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: white;
  transition: width 0.3s ease;
}

.rest-card {
  background: linear-gradient(135deg, #95de64, #b7eb8f);
  border: 2px dashed #52c41a;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  color: #389e0d;
}

.rest-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.rest-text {
  display: block;
  font-weight: 500;
}

.empty-card {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.empty-card:hover {
  border-color: #409eff;
}

.drop-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.drop-icon {
  font-size: 20px;
  color: #409eff;
}

.drop-text {
  font-size: 12px;
}

.day-notes {
  margin-top: 8px;
  padding: 6px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 11px;
  color: #606266;
}

.load-heatmap {
  padding: 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
}

.heatmap-header h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.heatmap-bar {
  display: flex;
  gap: 2px;
  height: 20px;
}

.heatmap-cell {
  flex: 1;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: 500;
  position: relative;
}

.heatmap-cell.load-none {
  background: #f0f2f5;
  color: #8c8c8c;
}

.heatmap-cell.load-low {
  background: #95de64;
}

.heatmap-cell.load-medium {
  background: #ffc53d;
}

.heatmap-cell.load-high {
  background: #ff7875;
}

/* 第一周期特殊样式 */
.first-cycle-header {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 4px solid #409eff;
}

.cycle-badge {
  margin-left: 12px;
  vertical-align: middle;
}

.cycle-badge .el-icon {
  margin-right: 4px;
}

/* 只读状态样式 */
.day-cell.read-only {
  opacity: 0.85;
  background: #fafbfc;
  cursor: not-allowed;
}

.day-cell.read-only .empty-card {
  border-style: dotted;
  border-color: #d0d0d0;
  background: #f8f9fa;
}

.day-cell.read-only .plan-card {
  filter: grayscale(15%);
  opacity: 0.9;
}

.lock-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  color: #909399;
  cursor: help;
}

.sync-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.sync-icon {
  font-size: 20px;
  color: #909399;
}

.sync-text {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .day-cell {
    min-height: 100px;
  }
}

@media (max-width: 768px) {
  .calendar-body {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }
  
  .weekdays {
    display: none;
  }
  
  .day-cell {
    margin-bottom: 8px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }
  
  .week-stats {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .week-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .week-info h4 {
    font-size: 16px;
  }
}
</style>