<template>
  <div class="quick-actions-horizontal">
    <h2 class="section-title">快速操作</h2>
    <div class="actions-row">
      <button class="action-btn-horizontal" data-testid="create-metric-btn" @click="handleCreateMetric">
        <el-icon class="action-icon-small"><Plus /></el-icon>
        <span>创建指标</span>
      </button>
      <button class="action-btn-horizontal" @click="handleCreateCategory">
        <el-icon class="action-icon-small"><FolderAdd /></el-icon>
        <span>创建分类</span>
      </button>
      <button class="action-btn-horizontal" @click="handleBatchImport">
        <el-icon class="action-icon-small"><Upload /></el-icon>
        <span>导入指标</span>
      </button>
      <button class="action-btn-horizontal" @click="handleExportMetrics">
        <el-icon class="action-icon-small"><Download /></el-icon>
        <span>导出数据</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { Plus, Upload, Download, FolderAdd } from '@element-plus/icons-vue'

const emit = defineEmits([
  'createMetric',
  'createCategory',
  'batchImport', 
  'exportMetrics'
])

const handleCreateMetric = () => {
  emit('createMetric')
}

const handleCreateCategory = () => {
  emit('createCategory')
}

const handleBatchImport = () => {
  emit('batchImport')
}

const handleExportMetrics = () => {
  emit('exportMetrics')
}
</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';
</style>