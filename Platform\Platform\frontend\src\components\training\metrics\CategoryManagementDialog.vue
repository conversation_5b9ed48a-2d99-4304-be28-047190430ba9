<template>
  <el-dialog
    v-model="visible"
    title="分类管理"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="category-management-dialog"
  >
    <div class="management-content">
      <!-- 操作栏 -->
      <div class="action-bar">
        <el-button 
          type="primary" 
          icon="Plus" 
          @click="handleCreateCategory"
          :disabled="loading"
        >
          创建分类
        </el-button>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索分类名称"
          clearable
          class="search-input"
          prefix-icon="Search"
          @input="handleSearch"
        />
      </div>

      <!-- 分类列表 -->
      <div class="categories-table" v-loading="loading">
        <el-table
          :data="filteredCategories"
          stripe
          :empty-text="searchKeyword ? '未找到匹配的分类' : '暂无分类数据'"
          @sort-change="handleSortChange"
        >
          <el-table-column 
            prop="id" 
            label="ID" 
            width="80" 
            sortable="custom"
          />
          <el-table-column 
            prop="name" 
            label="分类名称" 
            min-width="200"
            sortable="custom"
          >
            <template #default="{ row }">
              <div class="category-name" :style="{ paddingLeft: getCategoryIndent(row.level) }">
                <span v-if="row.level > 1" class="hierarchy-indicator">└─</span>
                <span class="name-text">{{ row.name }}</span>
                <el-tag 
                  v-if="row.level > 1" 
                  type="info" 
                  size="small"
                  class="level-tag"
                >
                  L{{ row.level }}
                </el-tag>
                <el-tag 
                  v-if="!row.is_active" 
                  type="danger" 
                  size="small"
                  class="status-tag"
                >
                  已禁用
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column 
            prop="description" 
            label="分类描述" 
            min-width="200"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.description || '暂无描述' }}
            </template>
          </el-table-column>
          <el-table-column 
            prop="metrics_count" 
            label="指标数量" 
            width="100" 
            sortable="custom"
            align="center"
          >
            <template #default="{ row }">
              <el-tag type="info" size="small">
                {{ row.metrics_count || 0 }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="created_at" 
            label="创建时间" 
            width="180"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column 
            label="操作" 
            width="200" 
            fixed="right"
          >
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  link 
                  size="small" 
                  icon="Edit"
                  @click="handleEditCategory(row)"
                  :disabled="loading"
                >
                  编辑
                </el-button>
                <el-button 
                  type="danger" 
                  link 
                  size="small" 
                  icon="Delete"
                  @click="handleDeleteCategory(row)"
                  :disabled="loading || row.metrics_count > 0"
                  :title="row.metrics_count > 0 ? '该分类下还有指标，无法删除' : '删除分类'"
                >
                  删除
                </el-button>
                <el-button 
                  :type="row.is_active ? 'warning' : 'success'" 
                  link 
                  size="small"
                  :icon="row.is_active ? 'Hide' : 'View'"
                  @click="handleToggleStatus(row)"
                  :disabled="loading"
                >
                  {{ row.is_active ? '禁用' : '启用' }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper" v-if="total > pageSize">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 分类表单对话框 -->
    <CategoryFormDialog
      v-model="showFormDialog"
      :category-data="selectedCategory"
      :is-editing="isEditingCategory"
      :loading="formLoading"
      :categories="categories"
      @submit="handleFormSubmit"
      @close="handleFormClose"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Search, Hide, View } from '@element-plus/icons-vue'
import CategoryFormDialog from './CategoryFormDialog.vue'
import { getMetricCategories, createMetricCategory, updateMetricCategory, deleteMetricCategory } from '@/api/training'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:modelValue',
  'refresh'
])

// 响应式数据
const loading = ref(false)
const formLoading = ref(false)
const searchKeyword = ref('')
const categories = ref([])
const selectedCategory = ref({})
const showFormDialog = ref(false)
const isEditingCategory = ref(false)

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 排序数据
const sortField = ref('id')
const sortOrder = ref('asc')

// 计算属性
const visible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const filteredCategories = computed(() => {
  let filtered = categories.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(category => 
      category.name.toLowerCase().includes(keyword) ||
      (category.description && category.description.toLowerCase().includes(keyword))
    )
  }

  // 排序
  filtered.sort((a, b) => {
    const fieldA = a[sortField.value]
    const fieldB = b[sortField.value]
    
    let result = 0
    if (fieldA < fieldB) result = -1
    else if (fieldA > fieldB) result = 1
    
    return sortOrder.value === 'desc' ? -result : result
  })

  return filtered
})

// 监听对话框显示状态
watch(() => props.modelValue, (newVisible) => {
  if (newVisible) {
    loadCategories()
  }
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载分类列表
const loadCategories = async () => {
  try {
    loading.value = true
    const response = await getMetricCategories()
    
    if (response.data?.categories) {
      categories.value = response.data.categories
      total.value = response.data.total || categories.value.length
    } else {
      console.warn('API响应格式异常:', response)
      categories.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败，请稍后重试')
    categories.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortField.value = prop || 'id'
  sortOrder.value = order === 'descending' ? 'desc' : 'asc'
}

// 计算分类层级缩进
const getCategoryIndent = (level) => {
  return `${(level - 1) * 20}px`
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 创建分类
const handleCreateCategory = () => {
  selectedCategory.value = {}
  isEditingCategory.value = false
  showFormDialog.value = true
}

// 编辑分类
const handleEditCategory = (category) => {
  selectedCategory.value = { ...category }
  isEditingCategory.value = true
  showFormDialog.value = true
}

// 删除分类
const handleDeleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true
    const response = await deleteMetricCategory(category.id)
    
    if (response.code === 200) {
      ElMessage.success('删除分类成功')
      await loadCategories()
      emit('refresh')
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error(error.message || '删除分类失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 切换状态
const handleToggleStatus = async (category) => {
  const newStatus = !category.is_active
  const actionText = newStatus ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}分类 "${category.name}" 吗？`,
      `确认${actionText}`,
      {
        confirmButtonText: `确定${actionText}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    const response = await updateMetricCategory(category.id, {
      name: category.name,
      description: category.description,
      is_active: newStatus
    })
    
    if (response.code === 200) {
      ElMessage.success(`${actionText}分类成功`)
      category.is_active = newStatus
      emit('refresh')
    } else {
      throw new Error(response.message || `${actionText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${actionText}分类失败:`, error)
      ElMessage.error(error.message || `${actionText}分类失败，请稍后重试`)
    }
  } finally {
    loading.value = false
  }
}

// 表单提交处理
const handleFormSubmit = async (formData) => {
  try {
    formLoading.value = true
    let response

    if (isEditingCategory.value) {
      // 更新分类
      response = await updateMetricCategory(selectedCategory.value.id, formData)
    } else {
      // 创建分类
      response = await createMetricCategory(formData)
    }

    if (response.code === 200) {
      ElMessage.success(isEditingCategory.value ? '更新分类成功' : '创建分类成功')
      showFormDialog.value = false
      await loadCategories()
      emit('refresh')
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error(error.message || '操作失败，请稍后重试')
  } finally {
    formLoading.value = false
  }
}

// 表单关闭处理
const handleFormClose = () => {
  showFormDialog.value = false
  selectedCategory.value = {}
}

// 关闭对话框
const handleClose = () => {
  if (loading.value || formLoading.value) return
  
  visible.value = false
  searchKeyword.value = ''
  currentPage.value = 1
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.modelValue) {
    loadCategories()
  }
})

// 暴露组件方法
defineExpose({
  loadCategories,
  handleClose
})
</script>

<style lang="scss" scoped>
.category-management-dialog {
  .management-content {
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      gap: 16px;
      
      .search-input {
        width: 280px;
        flex-shrink: 0;
      }
    }
    
    .categories-table {
      .category-name {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .hierarchy-indicator {
          color: #909399;
          font-size: 12px;
          margin-right: 4px;
        }
        
        .name-text {
          font-weight: 500;
        }
        
        .level-tag {
          flex-shrink: 0;
          margin-left: 4px;
        }
        
        .status-tag {
          flex-shrink: 0;
        }
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-start;
      }
      
      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
      }
    }
  }
}

// Element Plus 样式重写
:deep(.el-dialog) {
  border-radius: 12px;
  
  .el-dialog__header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding-top: 20px;
  }
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header {
    th {
      background-color: #f8f9fa;
      color: #606266;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

:deep(.el-pagination) {
  .el-pagination__sizes {
    .el-select {
      margin-right: 10px;
    }
  }
  
  .el-pagination__jump {
    margin-left: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-management-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2vh auto;
    }
    
    .management-content {
      .action-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        .search-input {
          width: 100%;
        }
      }
    }
    
    :deep(.el-table) {
      .el-table__header,
      .el-table__body {
        th, td {
          padding: 8px 4px;
          font-size: 13px;
        }
      }
      
      .action-buttons {
        flex-direction: column;
        gap: 4px;
      }
    }
    
    :deep(.el-pagination) {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
    }
  }
}

@media (max-width: 480px) {
  .category-management-dialog {
    :deep(.el-table) {
      .el-table__header,
      .el-table__body {
        th, td {
          padding: 6px 2px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>