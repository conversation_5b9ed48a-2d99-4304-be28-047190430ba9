<template>
  <div class="metrics-table-section">
    <div class="table-header">
      <div class="table-title">
        <h3>指标列表</h3>
        <span class="total-count">共 {{ totalMetrics }} 个指标</span>
      </div>
    </div>

    <!-- 指标性质筛选器 -->
    <div class="metric-type-filter">
      <span class="filter-label">指标性质：</span>
      <el-checkbox-group v-model="selectedMetricTypes" @change="handleMetricTypeChange">
        <el-checkbox label="normal">普通指标</el-checkbox>
        <el-checkbox label="neutral">基准指标</el-checkbox>
        <el-checkbox label="target">目标指标</el-checkbox>
        <el-checkbox label="actual">实际指标</el-checkbox>
      </el-checkbox-group>
    </div>

    <div class="table-container">
      <el-table
        :data="paginatedMetrics"
        :loading="loading"
        stripe
        class="metrics-table"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="name"
          label="指标名称"
          width="180"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="metric-name-cell">
              <span class="metric-name clickable" @click="handleView(row)">{{ row.name }}</span>
              <!-- 指标类型标识 -->
              <el-tag v-if="getMetricTypeTag(row)" 
                      :type="getMetricTypeTag(row).type" 
                      size="small" 
                      :class="getMetricTypeTag(row).class">
                <i :class="getMetricTypeTag(row).icon"></i>
                {{ getMetricTypeTag(row).text }}
              </el-tag>
              <span class="metric-code">{{ row.code }}</span>
            </div>
          </template>
        </el-table-column>
        
        
        <el-table-column
          prop="category_name"
          label="分类"
          width="140"
          :filters="categoryFilters"
          :filter-method="filterByCategory"
        >
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category_name)" size="small">
              {{ row.category_name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="data_type"
          label="数据类型"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getDataTypeTagType(row.data_type)" size="small">
              {{ getDataTypeLabel(row.data_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="metric_role"
          label="指标角色"
          width="130"
          :filters="[
            { text: '普通指标', value: 'normal' },
            { text: '基准指标', value: 'neutral' },
            { text: '目标指标', value: 'target' },
            { text: '实际指标', value: 'actual' }
          ]"
          :filter-method="filterByMetricRole"
        >
          <template #default="{ row }">
            <el-tag 
              :type="getMetricRoleTagType(row.metric_role)" 
              size="small"
              :class="getMetricRoleTagClass(row.metric_role)"
            >
              <i :class="getMetricRoleIcon(row.metric_role)"></i>
              {{ getMetricRoleLabel(row.metric_role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="unit"
          label="单位"
          width="100"
          show-overflow-tooltip
        />
        
        <el-table-column
          prop="is_active"
          label="状态"
          width="100"
          :filters="[
            { text: '启用', value: true },
            { text: '禁用', value: false }
          ]"
          :filter-method="filterByStatus"
        >
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="140"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button 
                type="primary" 
                :icon="Edit" 
                @click="handleEdit(row)"
                title="编辑"
              />
              <el-button 
                type="danger" 
                :icon="Delete"
                @click="handleDelete(row)"
                title="删除"
              />
            </el-button-group>
          </template>
        </el-table-column>
        
      </el-table>
    </div>
    
    <!-- 批量操作区域 -->
    <div v-if="selectedMetrics.length > 0" class="batch-operations">
      <div class="selected-info">
        已选择 {{ selectedMetrics.length }} 个指标
      </div>
      <div class="batch-actions">
        <el-button 
          type="primary" 
          size="small"
          @click="handleBatchEdit"
        >
          批量编辑
        </el-button>
        <el-button 
          type="warning"
          size="small" 
          @click="handleBatchDuplicate"
        >
          批量复制
        </el-button>
        <el-button 
          type="info"
          size="small"
          @click="handleBatchExport"
        >
          导出选中
        </el-button>
        <el-button 
          type="danger" 
          size="small"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, toRefs, computed } from 'vue'
import { Grid, View, Edit, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  paginatedMetrics: {
    type: Array,
    required: true
  },
  totalMetrics: {
    type: Number,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  categories: {
    type: Array,
    required: true
  },
  selectedMetricTypes: {
    type: Array,
    default: () => ['normal', 'neutral']
  }
})

const emit = defineEmits([
  'selectionChange',
  'sortChange',
  'statusChange',
  'edit',
  'view',
  'duplicate',
  'delete',
  'batchEdit',
  'batchDelete',
  'batchDuplicate',
  'batchExport',
  'updateSelectedMetricTypes'
])

// 选中的指标
const selectedMetrics = ref([])

const { 
  paginatedMetrics, 
  totalMetrics, 
  loading, 
  categories,
  selectedMetricTypes
} = toRefs(props)

// 指标性质筛选状态现在通过props传入，不需要本地状态

const categoryFilters = computed(() => {
  return categories.value.map(cat => ({
    text: cat.name,
    value: cat.name
  }))
})

// 获取指标类型
const getMetricType = (metric) => {
  if (metric.is_neutral) return 'baseline'
  if (metric.name && metric.name.startsWith('目标')) return 'target'
  if (metric.name && metric.name.startsWith('实际')) return 'actual'
  return 'normal'
}

// 筛选现在在composable中处理，这里不需要本地筛选逻辑

const handleViewModeChange = (mode) => {
  emit('viewModeChange', mode)
}

const handleSelectionChange = (selection) => {
  selectedMetrics.value = selection
  emit('selectionChange', selection)
}

const handleSortChange = (sortInfo) => {
  emit('sortChange', sortInfo)
}

const handleStatusChange = (row) => {
  emit('statusChange', row)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleView = (row) => {
  emit('view', row)
}

const handleDuplicate = (row) => {
  emit('duplicate', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const filterByCategory = (value, row) => {
  return row.category_name === value
}

const filterByStatus = (value, row) => {
  return row.is_active === value
}

const getCategoryTagType = (categoryName) => {
  const typeMap = {
    '基础指标': 'primary',
    '性能指标': 'success', 
    '技术指标': 'warning',
    '综合指标': 'info'
  }
  return typeMap[categoryName] || ''
}

const getDataTypeTagType = (dataType) => {
  const typeMap = {
    'number': 'primary',
    'percentage': 'success',
    'time': 'warning',
    'text': 'info'
  }
  return typeMap[dataType] || ''
}

const getDataTypeLabel = (dataType) => {
  const labelMap = {
    'number': '数值',
    'percentage': '百分比',
    'time': '时间',
    'text': '文本'
  }
  return labelMap[dataType] || dataType
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 批量操作处理方法
const handleBatchEdit = () => {
  emit('batchEdit', selectedMetrics.value)
}

const handleBatchDelete = () => {
  emit('batchDelete', selectedMetrics.value)
}

const handleBatchDuplicate = () => {
  emit('batchDuplicate', selectedMetrics.value)
}

const handleBatchExport = () => {
  emit('batchExport', selectedMetrics.value)
}

// 指标角色相关方法
const getMetricRoleLabel = (role) => {
  const roleLabels = {
    'normal': '普通指标',
    'neutral': '基准指标',
    'target': '目标指标',
    'actual': '实际指标'
  }
  return roleLabels[role] || '普通指标'
}

const getMetricRoleTagType = (role) => {
  const tagTypes = {
    'normal': 'info',
    'neutral': 'success',
    'target': 'primary',
    'actual': 'warning'
  }
  return tagTypes[role] || 'info'
}

const getMetricRoleTagClass = (role) => {
  return `metric-role-${role}`
}

const getMetricRoleIcon = (role) => {
  const icons = {
    'normal': 'el-icon-document',
    'neutral': 'el-icon-data-line',
    'target': 'el-icon-aim',
    'actual': 'el-icon-finished'
  }
  return icons[role] || 'el-icon-document'
}

// 指标角色筛选方法
const filterByMetricRole = (value, row) => {
  return row.metric_role === value
}

// 获取指标类型标签 - 支持新的 metric_role 字段
const getMetricTypeTag = (row) => {
  // 优先使用新的 metric_role 字段
  const role = row.metric_role || getMetricType(row)
  
  const tagConfig = {
    'neutral': {
      type: 'success',
      text: '基准',
      icon: 'el-icon-data-line',
      class: 'neutral-tag'
    },
    'target': {
      type: 'primary',
      text: '目标',
      icon: 'el-icon-aim',
      class: 'target-tag'
    },
    'actual': {
      type: 'warning',
      text: '实际',
      icon: 'el-icon-finished',
      class: 'actual-tag'
    },
    'normal': null  // 普通指标不显示标签
  }
  
  return tagConfig[role] || null
}

// 处理指标性质筛选变化
const handleMetricTypeChange = (types) => {
  emit('updateSelectedMetricTypes', types)
  console.log('Selected metric types:', types)
}

</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';

.metric-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .metric-name {
    font-weight: 500 !important;
    color: #000000 !important;
    cursor: pointer;
    
    &:hover {
      color: #409eff !important;
    }
  }
  
  .neutral-tag,
  .target-tag,
  .actual-tag {
    align-self: flex-start;
    margin: 2px 0;
    
    i {
      margin-right: 4px;
    }
  }
  
  .neutral-tag {
    background-color: #f0f9ff;
    border-color: #67c23a;
    color: #67c23a;
  }
  
  .target-tag {
    background-color: #ecf5ff;
    border-color: #409eff;
    color: #409eff;
  }
  
  .actual-tag {
    background-color: #fdf6ec;
    border-color: #e6a23c;
    color: #e6a23c;
  }
  
  .metric-code {
    font-size: 12px !important;
    color: #606266 !important;  /* 更深的灰色，提高可读性 */
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

.clickable {
  cursor: pointer;
}

// 指标角色标签样式 - 增强对比度和可读性
:deep(.metric-role-normal) {
  background-color: #f4f4f5 !important;
  border-color: #606266 !important;  /* 更深的灰色边框 */
  color: #303133 !important; /* 加深颜色提高对比度 */
  font-weight: 500 !important;
  
  i {
    margin-right: 4px;
  }
}

:deep(.metric-role-neutral) {
  background-color: #f0f9ff !important;
  border-color: #67c23a !important;
  color: #409540 !important; /* 加深颜色提高对比度 */
  font-weight: 500 !important;
  
  i {
    margin-right: 4px;
  }
}

:deep(.metric-role-target) {
  background-color: #ecf5ff !important;
  border-color: #409eff !important;
  color: #2563eb !important; /* 加深颜色提高对比度 */
  font-weight: 500 !important;
  
  i {
    margin-right: 4px;
  }
}

:deep(.metric-role-actual) {
  background-color: #fdf6ec !important;
  border-color: #e6a23c !important;
  color: #d97706 !important; /* 加深颜色提高对比度 */
  font-weight: 500 !important;
  
  i {
    margin-right: 4px;
  }
}

.metric-type-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  
  .filter-label {
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
  }
  
  .el-checkbox-group {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
  }
  
}

/* 强制覆盖可能的全局样式冲突 */
.metrics-table-section {
  /* 确保表格内文字颜色正确显示，但不影响标签颜色 */
  :deep(.el-table) {
    width: 100% !important;
    
    .cell {
      color: #000000 !important;
      
      /* 但是标签元素需要保持自己的颜色 */
      .el-tag {
        color: inherit !important;
      }
    }
    
    .el-table__body-wrapper {
      .el-table__body {
        .el-table__row {
          .el-table__cell {
            color: #000000 !important;
            
            .cell {
              color: #000000 !important;
              
              /* 但是标签元素需要保持自己的颜色 */
              .el-tag {
                color: inherit !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>