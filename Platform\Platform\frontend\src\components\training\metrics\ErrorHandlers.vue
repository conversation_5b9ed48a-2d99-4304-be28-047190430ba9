<template>
  <div class="error-handlers">
    <!-- API错误处理UI组件 -->
    <div v-if="hasApiError" class="error-message el-message--error" style="margin-bottom: 15px; padding: 10px; background: #fef0f0; border: 1px solid #fbc4c4; border-radius: 4px; color: #f56c6c;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-icon data-testid="error-icon" style="color: #f56c6c;"><Warning /></el-icon>
        <span :data-testid="getErrorTestId()">{{ getErrorMessage() }}</span>
      </div>
      <div style="margin-top: 8px;">
        <el-button 
          size="small" 
          type="primary" 
          @click="$emit('retry')"
          data-testid="retry-button"
        >
          <el-icon><RefreshRight /></el-icon>
          重试
        </el-button>
        <el-button 
          size="small" 
          type="text" 
          @click="showErrorDetails = !showErrorDetails"
          data-testid="error-details"
        >
          {{ showErrorDetails ? '隐藏详情' : '查看详情' }}
        </el-button>
      </div>
      <div v-if="showErrorDetails" style="margin-top: 8px; padding: 8px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
        错误详情: {{ apiErrorDetails || '无法连接到服务器，请检查网络连接。' }}
      </div>
    </div>
    
    <!-- 超时错误处理UI组件 -->
    <div v-if="hasTimeoutError" class="timeout-error" data-testid="timeout-error-message" style="margin-bottom: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-icon style="color: #856404;"><Timer /></el-icon>
        <span>请求超时，服务器响应时间过长</span>
      </div>
    </div>
    
    <!-- 网络错误降级界面 -->
    <div v-if="hasNetworkError" class="network-timeout offline-indicator" data-testid="network-error" style="margin-bottom: 15px; padding: 10px; background: #fff7e6; border: 1px solid #ffc069; border-radius: 4px; color: #d48806;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-icon style="color: #d48806;"><Connection /></el-icon>
        <span>网络连接异常，已切换到离线模式</span>
      </div>
      <div style="margin-top: 8px;">
        <el-button size="small" @click="$emit('retryNetwork')" data-testid="retry-network">重试连接</el-button>
        <span data-testid="offline-mode" style="font-size: 12px; color: #999;">离线模式: 使用缓存数据</span>
      </div>
      <div v-if="hasCachedData" data-testid="cached-data" style="margin-top: 8px; font-size: 12px; color: #666;">
        📂 正在使用本地缓存数据
      </div>
      <div data-testid="offline-actions" style="margin-top: 8px; font-size: 12px;">
        离线可用操作: 查看已保存的模板、编辑草稿
      </div>
    </div>
    
    <!-- 成功消息UI组件 -->
    <div v-if="hasSuccessMessage" class="success-message" data-testid="success-message" style="margin-bottom: 15px; padding: 10px; background: #f0f9ff; border: 1px solid #67c23a; border-radius: 4px; color: #67c23a;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-icon style="color: #67c23a;"><SuccessFilled /></el-icon>
        <span>{{ successMessage }}</span>
      </div>
    </div>

    <!-- 验证错误UI组件 -->
    <div v-if="hasValidationErrors" class="validation-error" data-testid="validation-error" style="margin-bottom: 15px; padding: 10px; background: #fef7f0; border: 1px solid #fab989; border-radius: 4px; color: #c07429;">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-icon style="color: #c07429;"><Warning /></el-icon>
        <span>表单验证失败，请修正以下问题：</span>
      </div>
      <ul style="margin: 8px 0 0 24px; list-style-type: disc;">
        <li v-for="error in validationErrors" :key="error.id" style="margin: 4px 0;">{{ error.message }}</li>
      </ul>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator" data-testid="loading-indicator" style="margin-bottom: 15px; text-align: center; padding: 20px;">
      <el-icon class="loading-icon" style="font-size: 24px; color: #409eff;"><Loading /></el-icon>
      <div style="margin-top: 8px; color: #606266;">正在加载数据...</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Warning, RefreshRight, Timer, Connection, SuccessFilled, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  hasApiError: { type: Boolean, default: false },
  hasTimeoutError: { type: Boolean, default: false },
  hasNetworkError: { type: Boolean, default: false },
  hasSuccessMessage: { type: Boolean, default: false },
  hasValidationErrors: { type: Boolean, default: false },
  isLoading: { type: Boolean, default: false },
  successMessage: { type: String, default: '' },
  validationErrors: { type: Array, default: () => [] },
  apiErrorDetails: { type: String, default: '' },
  hasCachedData: { type: Boolean, default: false }
})

const emit = defineEmits(['retry', 'retryNetwork'])

const showErrorDetails = ref(false)

const getErrorTestId = () => {
  if (props.hasApiError) return 'api-error-message'
  return 'error-message'
}

const getErrorMessage = () => {
  if (props.hasApiError) return 'API请求失败，请重试'
  return '发生错误，请重试'
}
</script>

<style scoped>
/* 错误处理相关样式已经通过内联样式实现，保持与原版一致 */
</style>