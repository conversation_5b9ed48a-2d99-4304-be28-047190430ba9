<template>
  <div class="load-curve-chart">
    <div class="chart-header" v-if="showHeader">
      <h4>训练负荷趋势</h4>
      <div class="chart-controls">
        <el-button-group size="small">
          <el-button 
            :type="viewMode === 'day' ? 'primary' : 'default'"
            @click="setViewMode('day')"
          >
            日
          </el-button>
          <el-button 
            :type="viewMode === 'week' ? 'primary' : 'default'"
            @click="setViewMode('week')"
          >
            周
          </el-button>
          <el-button 
            :type="viewMode === 'month' ? 'primary' : 'default'"
            @click="setViewMode('month')"
          >
            月
          </el-button>
        </el-button-group>
        
        <el-dropdown @command="exportChart" v-if="showExport">
          <el-button size="small" type="text">
            导出数据
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="png">导出为PNG</el-dropdown-item>
              <el-dropdown-item command="jpg">导出为JPG</el-dropdown-item>
              <el-dropdown-item command="svg">导出为SVG</el-dropdown-item>
              <el-dropdown-item command="csv">导出数据(CSV)</el-dropdown-item>
              <el-dropdown-item command="json">导出数据(JSON)</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div 
      class="chart-container" 
      :style="{ height: height + 'px' }"
      ref="chartRef"
    >
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :init-options="initOptions"
        ref="chartInstance"
        autoresize
      />
    </div>

    <!-- 图表说明 -->
    <div class="chart-legend" v-if="showLegend">
      <div class="legend-item">
        <div class="legend-color low-load"></div>
        <span>低负荷 (0-40)</span>
      </div>
      <div class="legend-item">
        <div class="legend-color medium-load"></div>
        <span>中负荷 (40-70)</span>
      </div>
      <div class="legend-item">
        <div class="legend-color high-load"></div>
        <span>高负荷 (70+)</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { use } from 'echarts/core'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'
import { LineChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { ArrowDown } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import isoWeek from 'dayjs/plugin/isoWeek'

dayjs.extend(weekOfYear)
dayjs.extend(isoWeek)

// 注册ECharts组件
use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  LineChart,
  CanvasRenderer
])

// Props
interface LoadData {
  date: string
  load: number
  rpe?: number
  duration?: number
  intensity?: number
  volume?: number
}

interface Props {
  data: LoadData[]
  height?: number
  showHeader?: boolean
  showLegend?: boolean
  showReferenceLine?: boolean
  showExport?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  showHeader: true,
  showLegend: true,
  showReferenceLine: true,
  showExport: true
})

// 响应式数据
const chartRef = ref<HTMLElement>()
const chartInstance = ref<InstanceType<typeof VChart>>()
const viewMode = ref<'day' | 'week' | 'month'>('week')

// 初始化选项
const initOptions = {
  renderer: 'canvas' as const
}

// 计算属性
const chartData = computed(() => {
  return props.data.slice().sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  )
})

const processedData = computed(() => {
  const data = chartData.value
  if (data.length === 0) return { dates: [], loads: [], rpes: [], intensities: [], volumes: [] }
  
  // 根据视图模式聚合数据
  const aggregatedData = new Map()
  
  data.forEach(item => {
    let key: string
    const date = dayjs(item.date)
    
    switch (viewMode.value) {
      case 'day':
        key = date.format('YYYY-MM-DD')
        break
      case 'week':
        key = date.startOf('week').format('YYYY-[W]WW')
        break
      case 'month':
        key = date.format('YYYY-MM')
        break
      default:
        key = date.format('YYYY-MM-DD')
    }
    
    if (!aggregatedData.has(key)) {
      aggregatedData.set(key, {
        date: key,
        loads: [],
        rpes: [],
        intensities: [],
        volumes: [],
        durations: []
      })
    }
    
    const entry = aggregatedData.get(key)
    entry.loads.push(item.load)
    if (item.rpe) entry.rpes.push(item.rpe)
    if (item.intensity) entry.intensities.push(item.intensity)
    if (item.volume) entry.volumes.push(item.volume)
    if (item.duration) entry.durations.push(item.duration)
  })
  
  // 计算平均值
  const result = {
    dates: [] as string[],
    loads: [] as number[],
    rpes: [] as number[],
    intensities: [] as number[],
    volumes: [] as number[]
  }
  
  Array.from(aggregatedData.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([key, entry]) => {
      result.dates.push(key)
      result.loads.push(Math.round(entry.loads.reduce((sum: number, val: number) => sum + val, 0) / entry.loads.length))
      result.rpes.push(entry.rpes.length > 0 ? Math.round(entry.rpes.reduce((sum: number, val: number) => sum + val, 0) / entry.rpes.length * 10) / 10 : 0)
      result.intensities.push(entry.intensities.length > 0 ? Math.round(entry.intensities.reduce((sum: number, val: number) => sum + val, 0) / entry.intensities.length) : 0)
      result.volumes.push(entry.volumes.length > 0 ? Math.round(entry.volumes.reduce((sum: number, val: number) => sum + val, 0) / entry.volumes.length) : 0)
    })
  
  return result
})

const averageLoad = computed(() => {
  const loads = processedData.value.loads
  if (loads.length === 0) return 0
  return Math.round(loads.reduce((sum, load) => sum + load, 0) / loads.length)
})

const chartOption = computed(() => {
  const data = processedData.value
  
  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: (params: any[]) => {
        const dataIndex = params[0]?.dataIndex
        if (dataIndex === undefined) return ''
        
        const date = data.dates[dataIndex]
        let title = ''
        
        switch (viewMode.value) {
          case 'day':
            title = dayjs(date).format('YYYY年MM月DD日')
            break
          case 'week':
            title = `${date} (周平均)`
            break
          case 'month':
            title = dayjs(date).format('YYYY年MM月') + ' (月平均)'
            break
        }
        
        let content = `<div style="margin-bottom: 4px; font-weight: 600;">${title}</div>`
        
        params.forEach(param => {
          const value = param.value
          const color = param.color
          let unit = ''
          let label = param.seriesName
          
          if (param.seriesName === 'RPE') unit = '/10'
          if (param.seriesName === '强度') unit = '%'
          if (param.seriesName === '训练量') unit = 'kg'
          
          content += `<div style="color: ${color};">${label}: ${value}${unit}</div>`
        })
        
        return content
      }
    },
    legend: {
      show: props.showLegend,
      bottom: 0,
      textStyle: {
        color: '#606266',
        fontSize: 12
      }
    },
    grid: {
      top: 30,
      left: 50,
      right: 30,
      bottom: props.showLegend ? 60 : 30,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: data.dates,
      axisLabel: {
        color: '#606266',
        fontSize: 12,
        formatter: (value: string) => {
          switch (viewMode.value) {
            case 'day':
              return dayjs(value).format('MM/DD')
            case 'week':
              return value.replace(/^\d{4}-/, '')
            case 'month':
              return dayjs(value).format('MM月')
            default:
              return value
          }
        }
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '负荷值',
        nameTextStyle: {
          color: '#606266',
          fontSize: 12
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#e4e7ed',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: 'RPE/强度%',
        nameTextStyle: {
          color: '#606266',
          fontSize: 12
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '训练负荷',
        type: 'line',
        yAxisIndex: 0,
        data: data.loads,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#67c23a' },
              { offset: 0.5, color: '#409eff' },
              { offset: 1, color: '#f56c6c' }
            ]
          }
        },
        itemStyle: {
          color: (params: any) => {
            const value = params.value
            if (value < 40) return '#67c23a'
            if (value < 70) return '#e6a23c'
            return '#f56c6c'
          },
          borderWidth: 2,
          borderColor: '#fff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        markLine: props.showReferenceLine ? {
          data: [
            {
              name: '平均负荷',
              yAxis: averageLoad.value,
              lineStyle: {
                color: '#e6a23c',
                type: 'dashed',
                width: 2
              },
              label: {
                formatter: `平均负荷: ${averageLoad.value}`,
                position: 'insideEndTop',
                color: '#e6a23c',
                fontSize: 11
              }
            }
          ]
        } : undefined
      },
      ...(data.rpes.some(v => v > 0) ? [{
        name: 'RPE',
        type: 'line',
        yAxisIndex: 1,
        data: data.rpes,
        smooth: true,
        symbol: 'triangle',
        symbolSize: 5,
        lineStyle: {
          width: 2,
          color: '#e6a23c',
          type: 'dashed'
        },
        itemStyle: {
          color: '#e6a23c'
        }
      }] : []),
      ...(data.intensities.some(v => v > 0) ? [{
        name: '强度',
        type: 'line',
        yAxisIndex: 1,
        data: data.intensities,
        smooth: true,
        symbol: 'diamond',
        symbolSize: 5,
        lineStyle: {
          width: 2,
          color: '#909399',
          type: 'dotted'
        },
        itemStyle: {
          color: '#909399'
        }
      }] : [])
    ],
    toolbox: {
      show: props.showExport,
      right: 20,
      top: 0,
      feature: {
        saveAsImage: {
          title: '保存为图片',
          type: 'png',
          backgroundColor: '#fff'
        }
      },
      iconStyle: {
        borderColor: '#606266'
      },
      emphasis: {
        iconStyle: {
          borderColor: '#409eff'
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none'
      },
      {
        type: 'slider',
        show: data.dates.length > 10,
        xAxisIndex: 0,
        bottom: props.showLegend ? 30 : 0,
        height: 20,
        textStyle: {
          color: '#606266',
          fontSize: 10
        },
        handleStyle: {
          color: '#409eff'
        },
        borderColor: '#e4e7ed'
      }
    ],
    animation: true,
    animationDuration: 800,
    animationEasing: 'cubicOut' as const
  }
})

// 方法
const setViewMode = async (mode: 'day' | 'week' | 'month') => {
  viewMode.value = mode
  await nextTick()
}

const exportChart = async (format: string) => {
  if (!chartInstance.value) return
  
  const chart = (chartInstance.value as any)?.chart
  if (!chart) return
  
  switch (format) {
    case 'png':
      downloadImage(chart.getDataURL({ type: 'png', backgroundColor: '#fff' }), 'load-curve.png')
      break
    case 'jpg':
      downloadImage(chart.getDataURL({ type: 'jpeg', backgroundColor: '#fff' }), 'load-curve.jpg')
      break
    case 'svg':
      downloadImage(chart.getDataURL({ type: 'svg', backgroundColor: '#fff' }), 'load-curve.svg')
      break
    case 'csv':
      exportToCsv()
      break
    case 'json':
      exportToJson()
      break
  }
}

const downloadImage = (dataURL: string, filename: string) => {
  const link = document.createElement('a')
  link.href = dataURL
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const exportToCsv = () => {
  const data = processedData.value
  let csv = '日期,训练负荷,RPE,强度,训练量\n'
  
  for (let i = 0; i < data.dates.length; i++) {
    csv += `${data.dates[i]},${data.loads[i]},${data.rpes[i] || ''},${data.intensities[i] || ''},${data.volumes[i] || ''}\n`
  }
  
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `load-curve-${viewMode.value}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const exportToJson = () => {
  const data = {
    viewMode: viewMode.value,
    generatedAt: new Date().toISOString(),
    data: processedData.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `load-curve-${viewMode.value}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 监听器
watch(() => props.data, () => {
  // 数据变化时重新渲染
}, { deep: true })

// 生命周期
onMounted(async () => {
  await nextTick()
})
</script>

<style scoped>
.load-curve-chart {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chart-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  flex-shrink: 0;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.chart-container {
  position: relative;
  width: 100%;
  min-height: 200px;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e4e7ed;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #606266;
  flex-shrink: 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-color.low-load {
  background: #67c23a;
}

.legend-color.medium-load {
  background: #e6a23c;
}

.legend-color.high-load {
  background: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-container {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .load-curve-chart {
    padding: 0.75rem;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .chart-header h4 {
    text-align: center;
  }
  
  .chart-controls {
    justify-content: center;
  }
  
  .chart-container {
    min-height: 280px;
  }
  
  .chart-legend {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
  
  :deep(.echarts-for-vue) {
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .load-curve-chart {
    padding: 0.5rem;
    margin: 0 -0.5rem;
    border-radius: 0;
  }
  
  .chart-container {
    min-height: 320px;
  }
  
  .chart-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  :deep(.el-button-group) {
    width: 100%;
  }
  
  :deep(.el-button) {
    flex: 1;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chart {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .load-curve-chart {
    background: #1d1e1f;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .chart-header h4 {
    color: #e4e7ed;
  }
  
  .legend-item {
    color: #c0c4cc;
  }
  
  .chart-legend {
    border-top-color: #4c4d4f;
  }
}

/* 打印样式 */
@media print {
  .load-curve-chart {
    background: white;
    box-shadow: none;
    border: 1px solid #ddd;
    page-break-inside: avoid;
  }
  
  .chart-controls {
    display: none;
  }
  
  .chart-container {
    height: 300px !important;
    min-height: 300px;
  }
}

/* 动画和过渡效果 */
.load-curve-chart {
  transition: all 0.3s ease;
}

.chart-header {
  transition: all 0.2s ease;
}

.legend-item {
  transition: color 0.2s ease;
}

.legend-item:hover {
  color: #409eff;
}

/* 可访问性优化 */
@media (prefers-reduced-motion: reduce) {
  .load-curve-chart,
  .chart-header,
  .legend-item {
    transition: none;
  }
  
  :deep(.echarts-for-vue) {
    animation: none !important;
  }
}

/* 焦点状态 */
.chart:focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 加载状态 */
.chart-container.loading {
  position: relative;
}

.chart-container.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.chart-container.loading::after {
  content: '加载中...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  color: #606266;
  font-size: 14px;
}
</style>