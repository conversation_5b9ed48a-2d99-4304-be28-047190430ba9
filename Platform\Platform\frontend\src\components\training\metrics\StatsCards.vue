<template>
  <div class="modern-stats-section">
    <div class="ds-grid ds-grid--cols-4 ds-gap-md">
      <div class="ds-stat-card">
        <div class="stat-content-simple">
          <div class="stat-number-modern">{{ stats.totalMetrics }}</div>
          <div class="stat-label-modern">总指标数</div>
          <div class="stat-trend stat-trend--stable">稳定增长</div>
        </div>
      </div>
      
      <div class="ds-stat-card">
        <div class="stat-content-simple">
          <div class="stat-number-modern">{{ stats.activeMetrics }}</div>
          <div class="stat-label-modern">启用指标</div>
          <div class="stat-trend stat-trend--up">活跃使用</div>
        </div>
      </div>
      
      <div class="ds-stat-card">
        <div class="stat-content-simple">
          <div class="stat-number-modern">{{ stats.categoriesCount }}</div>
          <div class="stat-label-modern">分类数量</div>
          <div class="stat-trend stat-trend--stable">结构完整</div>
        </div>
      </div>
      
      <div class="ds-stat-card">
        <div class="stat-content-simple">
          <div class="stat-number-modern">{{ stats.usageRate }}%</div>
          <div class="stat-label-modern">使用率</div>
          <div class="stat-trend stat-trend--up">持续优化</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  stats: {
    type: Object,
    required: true
  }
})

const { stats } = toRefs(props)
</script>

<style lang="scss" scoped>
@import '@/styles/metrics-library.scss';
</style>