/**
 * Service Worker for Athletic Platform v1.2.0
 * 提供资源缓存、离线支持和推送通知功能
 */

const CACHE_NAME = 'athletic-platform-v120'
const STATIC_CACHE_NAME = 'athletic-static-v120'
const DYNAMIC_CACHE_NAME = 'athletic-dynamic-v120'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/static/js/app.js',
  '/static/css/app.css',
  '/static/images/logo.png',
  '/static/images/default-avatar.png',
  '/fonts/inter-var.woff2',
  '/fonts/source-han-sans.woff2'
]

// 需要网络优先的API路径
const NETWORK_FIRST_PATHS = [
  '/api/auth',
  '/api/competitions/create',
  '/api/results/submit',
  '/api/memoirs/create'
]

// 需要缓存优先的API路径
const CACHE_FIRST_PATHS = [
  '/api/competitions',
  '/api/athletes',
  '/api/organizations',
  '/api/hall-of-fame'
]

// Service Worker安装事件
self.addEventListener('install', event => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => self.skipWaiting())
      .catch(error => {
        console.error('Failed to cache static assets:', error)
      })
  )
})

// Service Worker激活事件
self.addEventListener('activate', event => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            // 删除旧版本的缓存
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME && 
                cacheName.startsWith('athletic-')) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => self.clients.claim())
      .catch(error => {
        console.error('Failed to activate service worker:', error)
      })
  )
})

// 拦截网络请求
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 忽略非HTTP请求
  if (!request.url.startsWith('http')) {
    return
  }

  // 忽略Chrome扩展请求
  if (url.protocol === 'chrome-extension:') {
    return
  }

  // 根据请求类型选择缓存策略
  if (request.method === 'GET') {
    if (url.pathname.startsWith('/api/')) {
      event.respondWith(handleAPIRequest(request))
    } else if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|woff2|woff|ttf)$/)) {
      event.respondWith(handleStaticAsset(request))
    } else {
      event.respondWith(handlePageRequest(request))
    }
  } else {
    // POST/PUT/DELETE等请求直接通过网络
    event.respondWith(fetch(request))
  }
})

/**
 * 处理API请求
 */
async function handleAPIRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  try {
    // 网络优先策略（用于实时数据）
    if (NETWORK_FIRST_PATHS.some(path => pathname.startsWith(path))) {
      return await networkFirst(request, DYNAMIC_CACHE_NAME)
    }
    
    // 缓存优先策略（用于相对静态的数据）
    if (CACHE_FIRST_PATHS.some(path => pathname.startsWith(path))) {
      return await cacheFirst(request, DYNAMIC_CACHE_NAME)
    }
    
    // 默认使用网络优先策略
    return await networkFirst(request, DYNAMIC_CACHE_NAME)
    
  } catch (error) {
    console.error('API request failed:', error)
    
    // 返回离线页面或缓存的响应
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线提示
    return new Response(JSON.stringify({
      error: 'Network unavailable',
      message: 'Please check your internet connection',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

/**
 * 处理静态资源请求
 */
async function handleStaticAsset(request) {
  try {
    // 对静态资源使用缓存优先策略
    return await cacheFirst(request, STATIC_CACHE_NAME)
  } catch (error) {
    console.error('Static asset request failed:', error)
    
    // 如果是关键资源，尝试从网络获取
    return fetch(request)
  }
}

/**
 * 处理页面请求
 */
async function handlePageRequest(request) {
  try {
    // 对页面使用网络优先策略
    const networkResponse = await fetch(request)
    
    // 缓存成功的页面响应
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
    
  } catch (error) {
    console.error('Page request failed:', error)
    
    // 尝试从缓存获取
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线页面
    return caches.match('/offline.html') || new Response(
      getOfflineHTML(),
      { headers: { 'Content-Type': 'text/html' } }
    )
  }
}

/**
 * 网络优先策略
 */
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request)
    
    // 只缓存成功的GET请求
    if (networkResponse.ok && request.method === 'GET') {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
    
  } catch (error) {
    // 网络失败，尝试从缓存获取
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      // 添加缓存标识头
      const response = cachedResponse.clone()
      response.headers.set('X-Served-From', 'cache')
      return response
    }
    
    throw error
  }
}

/**
 * 缓存优先策略
 */
async function cacheFirst(request, cacheName) {
  const cachedResponse = await caches.match(request)
  
  if (cachedResponse) {
    // 后台更新缓存
    fetch(request)
      .then(networkResponse => {
        if (networkResponse.ok) {
          caches.open(cacheName)
            .then(cache => cache.put(request, networkResponse))
        }
      })
      .catch(() => {
        // 忽略后台更新失败
      })
    
    return cachedResponse
  }
  
  // 缓存中没有，从网络获取
  const networkResponse = await fetch(request)
  
  if (networkResponse.ok) {
    const cache = await caches.open(cacheName)
    cache.put(request, networkResponse.clone())
  }
  
  return networkResponse
}

/**
 * 推送通知处理
 */
self.addEventListener('push', event => {
  if (!event.data) {
    return
  }

  try {
    const data = event.data.json()
    const options = {
      body: data.body || '您有新的消息',
      icon: '/static/images/logo-192.png',
      badge: '/static/images/badge-72.png',
      tag: data.tag || 'default',
      data: data.data || {},
      actions: data.actions || [
        {
          action: 'view',
          title: '查看',
          icon: '/static/images/action-view.png'
        },
        {
          action: 'close',
          title: '关闭',
          icon: '/static/images/action-close.png'
        }
      ],
      vibrate: [100, 50, 100],
      requireInteraction: data.requireInteraction || false
    }

    event.waitUntil(
      self.registration.showNotification(data.title || '运动平台通知', options)
    )
  } catch (error) {
    console.error('Push notification error:', error)
  }
})

/**
 * 通知点击处理
 */
self.addEventListener('notificationclick', event => {
  event.notification.close()

  const action = event.action
  const data = event.notification.data

  if (action === 'close') {
    return
  }

  // 处理通知点击
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        // 如果已有窗口打开，聚焦到该窗口
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus()
          }
        }
        
        // 否则打开新窗口
        const targetUrl = data.url || '/'
        if (clients.openWindow) {
          return clients.openWindow(targetUrl)
        }
      })
  )
})

/**
 * 后台同步处理
 */
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

async function doBackgroundSync() {
  try {
    // 获取待同步的数据
    const cache = await caches.open('sync-cache')
    const requests = await cache.keys()
    
    for (const request of requests) {
      try {
        // 重新发送请求
        await fetch(request)
        // 成功后从缓存中删除
        await cache.delete(request)
      } catch (error) {
        console.error('Background sync failed for:', request.url, error)
      }
    }
  } catch (error) {
    console.error('Background sync error:', error)
  }
}

/**
 * 生成离线页面HTML
 */
function getOfflineHTML() {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>离线模式 - 运动平台</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
          margin: 0;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .offline-container {
          text-align: center;
          background: white;
          border-radius: 12px;
          padding: 40px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
          max-width: 400px;
        }
        .offline-icon {
          font-size: 64px;
          margin-bottom: 20px;
        }
        .offline-title {
          font-size: 24px;
          color: #2c3e50;
          margin-bottom: 12px;
        }
        .offline-message {
          color: #7f8c8d;
          margin-bottom: 30px;
          line-height: 1.5;
        }
        .retry-btn {
          background: #3498db;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 16px;
          transition: background 0.3s;
        }
        .retry-btn:hover {
          background: #2980b9;
        }
      </style>
    </head>
    <body>
      <div class="offline-container">
        <div class="offline-icon">📶</div>
        <h1 class="offline-title">网络连接中断</h1>
        <p class="offline-message">
          抱歉，当前无法连接到网络。<br>
          请检查您的网络连接后重试。
        </p>
        <button class="retry-btn" onclick="window.location.reload()">
          重试连接
        </button>
      </div>
    </body>
    </html>
  `
}

/**
 * 清理过期缓存
 */
async function cleanupExpiredCaches() {
  const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000 // 7天

  try {
    const cache = await caches.open(DYNAMIC_CACHE_NAME)
    const requests = await cache.keys()
    
    for (const request of requests) {
      const response = await cache.match(request)
      if (response) {
        const dateHeader = response.headers.get('date')
        if (dateHeader) {
          const responseDate = new Date(dateHeader)
          if (Date.now() - responseDate.getTime() > CACHE_EXPIRY) {
            await cache.delete(request)
          }
        }
      }
    }
  } catch (error) {
    console.error('Cache cleanup failed:', error)
  }
}

// 定期清理过期缓存
setInterval(cleanupExpiredCaches, 60 * 60 * 1000) // 每小时清理一次