<!--
Metrics Selector Component - 指标选择器
用于选择和配置训练动作的指标
-->

<template>
  <div class="metrics-selector">
    <!-- 搜索和筛选 -->
    <div class="selector-header">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索指标名称或代码"
          prefix-icon="Search"
          clearable
          @input="onSearchInput"
          class="search-input"
        />
      </div>
      
      <div class="filter-section">
        <el-select
          v-model="selectedCategory"
          placeholder="选择分类"
          clearable
          @change="onCategoryChange"
          style="width: 160px"
        >
          <el-option label="全部分类" value="" />
          <el-option
            v-for="category in metricCategories"
            :key="category.id"
            :label="category.category_name"
            :value="category.id"
          />
        </el-select>
        
        <el-checkbox v-model="showNeutralOnly" @change="onFilterChange">
          仅中性指标
        </el-checkbox>
        
        <el-checkbox v-model="showRequiredOnly" @change="onFilterChange">
          仅必需指标
        </el-checkbox>
      </div>
    </div>
    
    <!-- 已选择的指标 -->
    <div v-if="selectedMetrics.length > 0" class="selected-metrics">
      <div class="selected-header">
        <h4>已选择的指标 ({{ selectedMetrics.length }})</h4>
        <el-button @click="clearAllSelection" size="small" type="danger" text>
          清除所有
        </el-button>
      </div>
      
      <div class="selected-list">
        <draggable
          v-model="selectedMetrics"
          @change="onMetricsReorder"
          item-key="id"
          handle=".drag-handle"
        >
          <template #item="{ element: metric, index }">
            <div class="selected-metric-item" :class="{ 'required': metric.is_required }">
              <div class="drag-handle">
                <el-icon><Rank /></el-icon>
              </div>
              
              <div class="metric-info">
                <span class="metric-name">{{ metric.metric_name }}</span>
                <span class="metric-code">({{ metric.metric_code }})</span>
                <span class="metric-unit" v-if="metric.unit">{{ metric.unit }}</span>
              </div>
              
              <div class="metric-badges">
                <el-tag v-if="metric.is_required" type="danger" size="small">必需</el-tag>
                <el-tag v-if="metric.is_neutral" type="warning" size="small">中性</el-tag>
              </div>
              
              <div class="metric-config" v-if="metric.is_neutral">
                <el-input-number
                  v-model="metric.default_neutral_level"
                  :min="0"
                  :max="metric.neutral_level ? metric.neutral_level * 2 : 100"
                  size="small"
                  placeholder="中性值"
                />
              </div>
              
              <div class="metric-actions">
                <el-button 
                  @click="removeMetric(index)" 
                  size="small" 
                  type="danger" 
                  text
                  :disabled="metric.is_required"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
    
    <!-- 可选指标列表 -->
    <div class="available-metrics" v-loading="isLoading">
      <div class="available-header">
        <h4>可选指标</h4>
        <div class="header-actions">
          <el-button @click="addRecommendedMetrics" size="small" type="primary">
            <el-icon><MagicStick /></el-icon>
            添加推荐
          </el-button>
        </div>
      </div>
      
      <div v-if="hasError" class="error-state">
        <el-empty description="加载失败">
          <el-button @click="retryLoad" type="primary">重试</el-button>
        </el-empty>
      </div>
      
      <div v-else-if="filteredMetrics.length === 0" class="empty-state">
        <el-empty 
          :description="searchQuery ? '未找到匹配的指标' : '暂无可用指标'"
          :image-size="80"
        />
      </div>
      
      <div v-else class="metrics-grid">
        <div
          v-for="metric in paginatedMetrics"
          :key="metric.id"
          class="metric-item"
          :class="{ 
            'selected': isMetricSelected(metric),
            'neutral': metric.is_neutral,
            'required': metric.is_required
          }"
          @click="toggleMetric(metric)"
        >
          <div class="metric-card">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              
              <div class="metric-badges">
                <el-tag v-if="metric.is_required" type="danger" size="small">必需</el-tag>
                <el-tag v-if="metric.is_neutral" type="warning" size="small">中性</el-tag>
                <el-tag v-if="isMetricSelected(metric)" type="success" size="small">已选</el-tag>
              </div>
            </div>
            
            <div class="metric-info">
              <h4 class="metric-name">{{ metric.metric_name }}</h4>
              <p class="metric-code">{{ metric.metric_code }}</p>
              <p class="metric-category">{{ getMetricCategoryName(metric.category_id) }}</p>
              
              <div class="metric-details">
                <div class="detail-item">
                  <span class="detail-label">数据类型:</span>
                  <span class="detail-value">{{ getDataTypeText(metric.data_type) }}</span>
                </div>
                
                <div class="detail-item" v-if="metric.unit">
                  <span class="detail-label">单位:</span>
                  <span class="detail-value">{{ metric.unit }}</span>
                </div>
                
                <div class="detail-item" v-if="metric.is_neutral && metric.neutral_level">
                  <span class="detail-label">中性值:</span>
                  <span class="detail-value">{{ metric.neutral_level }}</span>
                </div>
              </div>
              
              <div class="metric-description" v-if="metric.description">
                <p>{{ truncateDescription(metric.description, 100) }}</p>
              </div>
            </div>
            
            <div class="metric-actions">
              <el-checkbox
                :model-value="isMetricSelected(metric)"
                @change="toggleMetric(metric)"
                @click.stop
              >
                选择
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredMetrics.length"
          :page-sizes="[12, 24, 36, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPageSizeChange"
          @current-change="onCurrentPageChange"
        />
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="selector-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleConfirm" :disabled="selectedMetrics.length === 0">
        确认选择 ({{ selectedMetrics.length }})
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import draggable from 'vuedraggable'
import {
  Rank, Delete, MagicStick, DataBoard
} from '@element-plus/icons-vue'

import type { Metric, ExerciseMetric, MetricCategory } from '@/types'
import { useMetricsStore } from '@/stores/metrics'

// Props
interface Props {
  exerciseId?: number
  selectedMetrics?: ExerciseMetric[]
  maxSelection?: number
}

const props = withDefaults(defineProps<Props>(), {
  selectedMetrics: () => [],
  maxSelection: 10
})

// Emits
const emit = defineEmits<{
  'metrics-updated': [metrics: ExerciseMetric[]]
  close: []
}>()

// Stores
const metricsStore = useMetricsStore()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref<number | ''>('')
const showNeutralOnly = ref(false)
const showRequiredOnly = ref(false)

// 已选择的指标
const selectedMetrics = ref<ExerciseMetric[]>([...props.selectedMetrics])

// 分页
const currentPage = ref(1)
const pageSize = ref(24)

// 计算属性
const { 
  metrics, 
  categories: metricCategories, 
  isLoading, 
  hasError 
} = metricsStore

const filteredMetrics = computed(() => {
  let result = metrics

  // 文本搜索
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    result = result.filter(metric => 
      metric.metric_name.toLowerCase().includes(query) ||
      metric.metric_code.toLowerCase().includes(query) ||
      metric.description?.toLowerCase().includes(query)
    )
  }

  // 分类筛选
  if (selectedCategory.value !== '') {
    result = result.filter(metric => metric.category.id === selectedCategory.value)
  }

  // 仅中性指标
  if (showNeutralOnly.value) {
    result = result.filter(metric => metric.is_neutral)
  }

  // 仅必需指标
  if (showRequiredOnly.value) {
    result = result.filter(metric => metric.is_required)
  }

  // 只显示活跃指标
  result = result.filter(metric => metric.is_active)

  return result
})

const totalPages = computed(() => Math.ceil(filteredMetrics.value.length / pageSize.value))

const paginatedMetrics = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMetrics.value.slice(start, end)
})

// 方法
const loadMetrics = async () => {
  try {
    await metricsStore.fetchMetrics()
    await metricsStore.fetchCategories()
    
    // 如果有指定动作，加载动作相关的推荐指标
    if (props.exerciseId) {
      await loadExerciseMetrics(props.exerciseId)
    }
  } catch (error) {
    ElMessage.error('加载指标列表失败')
  }
}

const loadExerciseMetrics = async (exerciseId: number) => {
  try {
    // 这里可以加载动作特定的推荐指标
    // const exerciseMetrics = await exercisesStore.fetchExerciseMetrics(exerciseId)
    // 根据动作的默认指标更新选中状态
  } catch (error) {
    console.error('加载动作指标失败:', error)
  }
}

const retryLoad = () => {
  loadMetrics()
}

const onSearchInput = () => {
  currentPage.value = 1
}

const onCategoryChange = () => {
  currentPage.value = 1
}

const onFilterChange = () => {
  currentPage.value = 1
}

const isMetricSelected = (metric: Metric): boolean => {
  return selectedMetrics.value.some(selected => selected.metric_id === metric.id)
}

const toggleMetric = (metric: Metric) => {
  const index = selectedMetrics.value.findIndex(selected => selected.metric_id === metric.id)
  
  if (index > -1) {
    // 移除指标（如果不是必需的）
    const selectedMetric = selectedMetrics.value[index]
    if (selectedMetric.is_required) {
      ElMessage.warning('必需指标不能取消选择')
      return
    }
    selectedMetrics.value.splice(index, 1)
  } else {
    // 添加指标
    if (selectedMetrics.value.length >= props.maxSelection) {
      ElMessage.warning(`最多只能选择 ${props.maxSelection} 个指标`)
      return
    }
    
    const exerciseMetric: ExerciseMetric = {
      id: Date.now(), // 临时ID
      metric_id: metric.id,
      metric_code: metric.metric_code,
      metric_name: metric.metric_name,
      category: metric.category.name,
      unit: metric.unit,
      is_neutral: metric.is_neutral,
      neutral_level: metric.neutral_level,
      is_required: metric.is_required,
      is_default: false,
      default_neutral_level: metric.neutral_level,
      display_order: selectedMetrics.value.length + 1
    }
    
    selectedMetrics.value.push(exerciseMetric)
  }
}

const removeMetric = (index: number) => {
  const metric = selectedMetrics.value[index]
  if (metric.is_required) {
    ElMessage.warning('必需指标不能删除')
    return
  }
  selectedMetrics.value.splice(index, 1)
  updateDisplayOrder()
}

const clearAllSelection = () => {
  selectedMetrics.value = selectedMetrics.value.filter(metric => metric.is_required)
  ElMessage.success('已清除所有非必需指标')
}

const addRecommendedMetrics = () => {
  // 添加一些通用的推荐指标
  const recommendedCodes = ['sets', 'reps', 'weight', 'duration', 'rest_time']
  const recommended = metrics.filter(metric => 
    recommendedCodes.includes(metric.metric_code) && 
    !isMetricSelected(metric)
  ).slice(0, 5)
  
  recommended.forEach(metric => {
    if (selectedMetrics.value.length < props.maxSelection) {
      toggleMetric(metric)
    }
  })
  
  if (recommended.length > 0) {
    ElMessage.success(`已添加 ${recommended.length} 个推荐指标`)
  } else {
    ElMessage.info('暂无可添加的推荐指标')
  }
}

const onMetricsReorder = () => {
  updateDisplayOrder()
}

const updateDisplayOrder = () => {
  selectedMetrics.value.forEach((metric, index) => {
    metric.display_order = index + 1
  })
}

const onPageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const onCurrentPageChange = (page: number) => {
  currentPage.value = page
}

const handleConfirm = () => {
  emit('metrics-updated', selectedMetrics.value)
}

const handleCancel = () => {
  emit('close')
}

const handleReset = () => {
  selectedMetrics.value = [...props.selectedMetrics]
  searchQuery.value = ''
  selectedCategory.value = ''
  showNeutralOnly.value = false
  showRequiredOnly.value = false
  currentPage.value = 1
}

// 工具函数
const getDataTypeText = (dataType: string): string => {
  const types: Record<string, string> = {
    'integer': '整数',
    'decimal': '小数',
    'string': '文本',
    'boolean': '布尔值'
  }
  return types[dataType] || dataType
}

const getMetricCategoryName = (categoryId: number): string => {
  const category = metricCategories.find(cat => cat.id === categoryId)
  return category?.category_name || '未知分类'
}

const truncateDescription = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

// 监听器
watch(() => filteredMetrics.value.length, () => {
  if (currentPage.value > totalPages.value) {
    currentPage.value = Math.max(1, totalPages.value)
  }
})

// 生命周期
onMounted(() => {
  loadMetrics()
})
</script>

<style scoped lang="scss">
.metrics-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selector-header {
  padding: 0 0 16px 0;
  border-bottom: 1px solid #e1e8ed;
  margin-bottom: 16px;
  
  .search-section {
    margin-bottom: 12px;
    
    .search-input {
      width: 100%;
    }
  }
  
  .filter-section {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
  }
}

.selected-metrics {
  margin-bottom: 24px;
  
  .selected-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #2c3e50;
    }
  }
  
  .selected-list {
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    background: #fafbfc;
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .selected-metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    transition: all 0.2s;
    
    &.required {
      border-left: 3px solid #f56c6c;
    }
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .drag-handle {
      cursor: grab;
      color: #6c7b7f;
      
      &:hover {
        color: #409eff;
      }
    }
    
    .metric-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .metric-name {
        font-weight: 500;
        color: #2c3e50;
      }
      
      .metric-code {
        color: #6c7b7f;
        font-size: 13px;
      }
      
      .metric-unit {
        background: #f0f2f5;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        color: #6c7b7f;
      }
    }
    
    .metric-badges {
      display: flex;
      gap: 4px;
    }
    
    .metric-config {
      min-width: 120px;
    }
  }
}

.available-metrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .available-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #2c3e50;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .error-state,
  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
  }
}

.metric-item {
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &.selected {
    .metric-card {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  &.neutral {
    .metric-card {
      border-left: 3px solid #e6a23c;
    }
  }
  
  &.required {
    .metric-card {
      border-left: 3px solid #f56c6c;
    }
  }
}

.metric-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 16px;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.2s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  }
}

.metric-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
  
  .metric-icon {
    width: 36px;
    height: 36px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #409eff;
    font-size: 16px;
    flex-shrink: 0;
  }
  
  .metric-badges {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
  }
}

.metric-info {
  flex: 1;
  margin-bottom: 12px;
  
  .metric-name {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
  }
  
  .metric-code {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: #6c7b7f;
    font-family: monospace;
    background: #f0f2f5;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
  }
  
  .metric-category {
    margin: 0 0 12px 0;
    font-size: 13px;
    color: #6c7b7f;
  }
  
  .metric-details {
    margin-bottom: 12px;
    
    .detail-item {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .detail-label {
        color: #6c7b7f;
      }
      
      .detail-value {
        color: #2c3e50;
        font-weight: 500;
      }
    }
  }
  
  .metric-description {
    p {
      margin: 0;
      font-size: 13px;
      color: #6c7b7f;
      line-height: 1.4;
    }
  }
}

.metric-actions {
  display: flex;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #e1e8ed;
  margin-top: auto;
}

.selector-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e1e8ed;
  margin-top: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    
    > * {
      width: 100% !important;
    }
  }
  
  .selected-header,
  .available-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .selector-actions {
    flex-direction: column;
  }
}
</style>