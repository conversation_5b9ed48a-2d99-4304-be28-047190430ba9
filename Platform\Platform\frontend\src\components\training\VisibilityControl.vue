<template>
  <div class="visibility-control">
    <div class="control-header">
      <div class="control-info">
        <h4>可见性设置</h4>
        <p class="control-description">控制谁可以查看和使用此训练计划</p>
      </div>
      <el-switch
        v-model="isPublic"
        size="large"
        :active-text="isPublic ? '公开' : ''"
        :inactive-text="!isPublic ? '私有' : ''"
        active-color="#67C23A"
        inactive-color="#909399"
        @change="handleVisibilityChange"
      />
    </div>

    <div class="visibility-options">
      <div class="option-card" :class="{ active: !isPublic }">
        <div class="option-icon">
          <el-icon><Lock /></el-icon>
        </div>
        <div class="option-content">
          <h5>私有</h5>
          <p>仅您可以查看和使用此训练计划</p>
          <ul class="option-features">
            <li>完全私有，其他人无法访问</li>
            <li>可随时修改或删除</li>
            <li>不会出现在公开搜索中</li>
          </ul>
        </div>
        <el-radio v-model="visibilityValue" label="private" @change="updateVisibility" />
      </div>

      <div class="option-card" :class="{ active: isPublic }">
        <div class="option-icon">
          <el-icon><Unlock /></el-icon>
        </div>
        <div class="option-content">
          <h5>组织内公开</h5>
          <p>组织内所有成员都可以查看和使用</p>
          <ul class="option-features">
            <li>组织内成员可搜索和使用</li>
            <li>有助于团队协作和分享</li>
            <li>仍保持对外私密</li>
          </ul>
        </div>
        <el-radio v-model="visibilityValue" label="public" @change="updateVisibility" />
      </div>
    </div>

    <!-- 高级选项 -->
    <div class="advanced-options" v-if="showAdvanced">
      <el-divider content-position="left">高级设置</el-divider>
      
      <div class="advanced-settings">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="setting-item">
              <label>允许复制</label>
              <el-switch 
                v-model="advancedSettings.allowCopy"
                @change="updateAdvancedSettings"
              />
              <p class="setting-description">允许其他用户复制此训练计划</p>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="setting-item">
              <label>允许修改建议</label>
              <el-switch 
                v-model="advancedSettings.allowSuggestions"
                @change="updateAdvancedSettings"
              />
              <p class="setting-description">允许其他用户提出修改建议</p>
            </div>
          </el-col>
        </el-row>

        <div class="setting-item">
          <label>分享范围</label>
          <el-select 
            v-model="advancedSettings.shareScope" 
            placeholder="选择分享范围"
            @change="updateAdvancedSettings"
          >
            <el-option label="仅限本组织" value="organization" />
            <el-option label="特定用户" value="specific" />
            <el-option label="所有人（需要链接）" value="link" />
          </el-select>
          <p class="setting-description">控制具体的分享范围</p>
        </div>

        <div v-if="advancedSettings.shareScope === 'specific'" class="setting-item">
          <label>指定用户</label>
          <el-select
            v-model="advancedSettings.allowedUsers"
            multiple
            placeholder="选择允许访问的用户"
            @change="updateAdvancedSettings"
          >
            <!-- 这里应该动态加载用户列表 -->
            <el-option label="用户1" value="user1" />
            <el-option label="用户2" value="user2" />
          </el-select>
        </div>
      </div>
    </div>

    <div class="control-actions">
      <el-button type="text" @click="showAdvanced = !showAdvanced">
        {{ showAdvanced ? '隐藏' : '显示' }}高级选项
        <el-icon>
          <ArrowDown v-if="!showAdvanced" />
          <ArrowUp v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Lock, Unlock, ArrowDown, ArrowUp } from '@element-plus/icons-vue'

// Props 和 Emits
interface Props {
  modelValue: string
  disabled?: boolean
}

interface Emits {
  'update:modelValue': [value: string]
  'change': [visibility: string, settings: any]
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

const emit = defineEmits<Emits>()

// 组件状态
const showAdvanced = ref(false)
const visibilityValue = ref(props.modelValue)
const advancedSettings = reactive({
  allowCopy: true,
  allowSuggestions: false,
  shareScope: 'organization',
  allowedUsers: [] as string[]
})

// 计算属性
const isPublic = computed({
  get: () => visibilityValue.value === 'public',
  set: (value: boolean) => {
    visibilityValue.value = value ? 'public' : 'private'
    updateVisibility()
  }
})

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
  visibilityValue.value = newValue
}, { immediate: true })

// 方法
const updateVisibility = () => {
  emit('update:modelValue', visibilityValue.value)
  emit('change', visibilityValue.value, advancedSettings)
}

const handleVisibilityChange = () => {
  updateVisibility()
}

const updateAdvancedSettings = () => {
  emit('change', visibilityValue.value, advancedSettings)
}
</script>

<style scoped>
.visibility-control {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.control-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.control-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.visibility-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.option-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-card:hover {
  background: #f5f7fa;
}

.option-card.active {
  background: #e7f4ff;
  border-color: #409eff;
}

.option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 50%;
  color: #409eff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.option-content {
  flex: 1;
}

.option-content h5 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.option-content p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
}

.option-features {
  margin: 0;
  padding: 0;
  list-style: none;
}

.option-features li {
  position: relative;
  padding-left: 16px;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 2px;
}

.option-features li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #409eff;
}

.advanced-options {
  margin-top: 24px;
}

.advanced-settings {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.setting-description {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.control-actions {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .visibility-control {
    padding: 16px;
  }
  
  .control-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .option-card {
    padding: 12px;
  }
  
  .advanced-settings .el-row {
    flex-direction: column;
  }
  
  .advanced-settings .el-col {
    width: 100% !important;
    margin-bottom: 16px;
  }
}
</style>