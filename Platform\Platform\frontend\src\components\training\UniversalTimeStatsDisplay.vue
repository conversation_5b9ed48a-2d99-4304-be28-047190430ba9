<!--
  通用训练时间统计显示组件
  Universal Training Time Statistics Display Component
  支持不同层级的时间统计：单动作、训练日、训练周期
-->

<template>
  <div class="universal-time-stats" :class="[`level-${level}`, themeClass]" data-testid="universal-time-stats">
    <div class="stats-container">
      <!-- 标题区域 -->
      <div class="stats-header">
        <h3 class="stats-title">
          <el-icon><Clock /></el-icon>
          {{ title }}
        </h3>
        <div class="stats-subtitle" v-if="subtitle">
          {{ subtitle }}
        </div>
      </div>
      
      <!-- 主要统计数据 -->
      <div class="stats-grid" :class="{ 'compact': compact }">
        <!-- 准备时间 -->
        <div class="stat-card" v-if="showPrepTime && prepTime > 0">
          <div class="stat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(prepTime) }}</div>
            <div class="stat-label">准备时间</div>
          </div>
        </div>
        
        <!-- 合计时间 -->
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><Stopwatch /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(totalTime) }}</div>
            <div class="stat-label">合计时间</div>
          </div>
        </div>
        
        <!-- 平均时间 -->
        <div class="stat-card" v-if="showAverageTime && itemCount > 1">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(averageTime) }}</div>
            <div class="stat-label">平均时间</div>
          </div>
        </div>
      </div>
      
      <!-- 详细时间分解 -->
      <div v-if="showBreakdown && breakdown.length > 0" class="time-breakdown">
        <div class="breakdown-header">
          <h4>时间构成分析</h4>
          <el-switch
            v-model="showDetailed"
            active-text="详细"
            inactive-text="简化"
            size="small"
            v-if="!compact"
          />
        </div>
        
        <div class="breakdown-content">
          <!-- 时间对比图表 -->
          <div class="comparison-chart">
            <div v-for="item in breakdown" :key="item.name" class="chart-item">
              <span class="item-label">{{ item.name }}</span>
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :class="item.type"
                  :style="{ width: `${item.percentage}%` }"
                  :title="`${formatTime(item.value)} (${item.percentage.toFixed(1)}%)`"
                ></div>
              </div>
              <span class="item-value">{{ formatTime(item.value) }}</span>
            </div>
          </div>
          
          <!-- 详细分解（仅在详细模式显示）-->
          <div v-if="showDetailed && detailedBreakdown.length > 0" class="detailed-breakdown">
            <div v-for="section in detailedBreakdown" :key="section.title" class="breakdown-section">
              <div class="section-title">{{ section.title }}</div>
              <div class="breakdown-items">
                <div v-for="item in section.items" :key="item.name" class="breakdown-item">
                  <span class="item-label">{{ item.name }}</span>
                  <span class="item-value">{{ formatTime(item.value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 优化建议 -->
        <div v-if="showOptimizationTips && optimizationTip" class="optimization-tips">
          <el-alert
            :title="optimizationTip.title"
            :description="optimizationTip.description"
            :type="optimizationTip.type"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Clock, Timer, Stopwatch, DataAnalysis } from '@element-plus/icons-vue'

interface TimeBreakdownItem {
  name: string
  value: number
  type: 'exercise' | 'recording' | 'rest' | 'prep' | 'other'
  percentage: number
}

interface DetailedBreakdownSection {
  title: string
  items: Array<{
    name: string
    value: number
  }>
}

interface OptimizationTip {
  title: string
  description: string
  type: 'success' | 'warning' | 'error' | 'info'
}

interface Props {
  // 基本配置
  level?: 'exercise' | 'daily' | 'cycle'  // 统计层级
  title?: string
  subtitle?: string
  compact?: boolean
  
  // 时间数据
  totalTime: number
  prepTime?: number
  breakdown?: TimeBreakdownItem[]
  detailedBreakdown?: DetailedBreakdownSection[]
  
  // 显示选项
  showPrepTime?: boolean
  showAverageTime?: boolean
  showBreakdown?: boolean
  showOptimizationTips?: boolean
  
  // 用于平均值计算
  itemCount?: number
  
  // 主题
  theme?: 'default' | 'orange' | 'blue' | 'green'
}

const props = withDefaults(defineProps<Props>(), {
  level: 'exercise',
  title: '时间统计',
  compact: false,
  prepTime: 0,
  breakdown: () => [],
  detailedBreakdown: () => [],
  showPrepTime: true,
  showAverageTime: true,
  showBreakdown: true,
  showOptimizationTips: true,
  itemCount: 1,
  theme: 'default'
})

const emit = defineEmits<{
  timeUpdate: [data: { totalTime: number, breakdown: TimeBreakdownItem[] }]
}>()

// 响应式状态
const showDetailed = ref(false)

// 计算属性
const averageTime = computed(() => {
  return props.itemCount > 0 ? props.totalTime / props.itemCount : 0
})

const themeClass = computed(() => {
  return `theme-${props.theme}`
})

const optimizationTip = computed((): OptimizationTip | null => {
  if (!props.showOptimizationTips || props.breakdown.length === 0) {
    return null
  }
  
  const recordingItem = props.breakdown.find(item => item.type === 'recording')
  if (recordingItem) {
    const recordingPercent = recordingItem.percentage
    
    if (recordingPercent > 40) {
      return {
        title: '数据记录时间较长',
        description: `当前记录时间占 ${recordingPercent.toFixed(0)}%，建议优化记录流程`,
        type: 'warning'
      }
    } else if (recordingPercent < 15) {
      return {
        title: '时间配比优秀',
        description: `记录时间仅占 ${recordingPercent.toFixed(0)}%，训练效率较高`,
        type: 'success'
      }
    }
  }
  
  return {
    title: '时间配比合理',
    description: '当前配置的时间分配比较均衡，训练效率良好',
    type: 'info'
  }
})

// 工具函数
const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(0)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 
      ? `${minutes}分${remainingSeconds.toFixed(0)}秒` 
      : `${minutes}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const remainingMinutes = Math.floor((seconds % 3600) / 60)
    return remainingMinutes > 0 
      ? `${hours}小时${remainingMinutes}分钟` 
      : `${hours}小时`
  }
}

// 监听变化并通知父组件
watch(
  () => ({ totalTime: props.totalTime, breakdown: props.breakdown }),
  (newData) => {
    emit('timeUpdate', newData)
  },
  { immediate: true, deep: true }
)
</script>

<style scoped lang="scss">
.universal-time-stats {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  &.compact {
    padding: 16px;
  }
  
  // 不同层级的样式
  &.level-exercise {
    border-left: 4px solid #409eff;
  }
  
  &.level-daily {
    border-left: 4px solid #67c23a;
  }
  
  &.level-cycle {
    border-left: 4px solid #e6a23c;
  }
}

.stats-container {
  .stats-header {
    margin-bottom: 20px;
    
    .stats-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 6px 0;
    }
    
    .stats-subtitle {
      font-size: 13px;
      color: #909399;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    
    &.compact {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      margin-bottom: 16px;
    }
  }
  
  .stat-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border-color: #409eff;
    }
    
    &.primary {
      background: linear-gradient(135deg, #409eff, #4dabf7);
      color: white;
      border: none;
      
      .stat-icon {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.1);
      }
      
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    .stat-icon {
      font-size: 24px;
      color: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background: rgba(64, 158, 255, 0.1);
      border-radius: 6px;
    }
    
    .stat-content {
      flex: 1;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 2px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
        line-height: 1.2;
      }
    }
  }
  
  .time-breakdown {
    background: #fafbfc;
    border-radius: 8px;
    padding: 16px;
    
    .breakdown-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        font-size: 15px;
        color: #303133;
      }
    }
    
    .comparison-chart {
      margin-bottom: 16px;
      
      .chart-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-label {
          width: 80px;
          font-size: 13px;
          color: #606266;
          text-align: right;
          margin-right: 12px;
        }
        
        .progress-bar {
          flex: 1;
          height: 8px;
          background: #f0f2f5;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 8px;
          
          .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            
            &.exercise {
              background: linear-gradient(135deg, #52c41a, #73d13d);
            }
            
            &.recording {
              background: linear-gradient(135deg, #409eff, #4dabf7);
            }
            
            &.rest {
              background: linear-gradient(135deg, #faad14, #ffc53d);
            }
            
            &.prep {
              background: linear-gradient(135deg, #eb2f96, #f759ab);
            }
            
            &.other {
              background: linear-gradient(135deg, #722ed1, #9c36b5);
            }
          }
        }
        
        .item-value {
          width: 60px;
          font-size: 12px;
          color: #606266;
          text-align: right;
        }
      }
    }
    
    .detailed-breakdown {
      .breakdown-section {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 8px;
          padding-bottom: 4px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .breakdown-items {
          .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 13px;
            
            .item-label {
              color: #909399;
            }
            
            .item-value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .optimization-tips {
      margin-top: 16px;
    }
  }
}

// 主题样式
.theme-orange {
  .stat-card {
    &.primary {
      background: linear-gradient(135deg, #e76f51, #f4a261);
    }
    
    .stat-icon {
      color: #e76f51;
      background: rgba(231, 111, 81, 0.1);
    }
    
    &:hover {
      border-color: #e76f51;
    }
  }
  
  .progress-fill {
    &.exercise {
      background: linear-gradient(135deg, #e76f51, #f4a261);
    }
    
    &.recording {
      background: linear-gradient(135deg, #2a9d8f, #43aa8b);
    }
    
    &.rest {
      background: linear-gradient(135deg, #f4a261, #e9c46a);
    }
  }
}

.theme-blue {
  .stat-card {
    &.primary {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
    }
    
    .stat-icon {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
    }
    
    &:hover {
      border-color: #1890ff;
    }
  }
}

.theme-green {
  .stat-card {
    &.primary {
      background: linear-gradient(135deg, #52c41a, #73d13d);
    }
    
    .stat-icon {
      color: #52c41a;
      background: rgba(82, 196, 26, 0.1);
    }
    
    &:hover {
      border-color: #52c41a;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .universal-time-stats {
    padding: 16px;
    
    .stats-grid {
      grid-template-columns: 1fr;
      gap: 12px;
      
      &.compact {
        gap: 8px;
      }
    }
    
    .stat-card {
      padding: 12px;
      
      .stat-icon {
        width: 32px;
        height: 32px;
        font-size: 20px;
      }
      
      .stat-value {
        font-size: 18px;
      }
    }
    
    .time-breakdown {
      padding: 12px;
    }
    
    .chart-item {
      .item-label {
        width: 60px;
        font-size: 12px;
      }
      
      .item-value {
        width: 50px;
      }
    }
  }
}
</style>