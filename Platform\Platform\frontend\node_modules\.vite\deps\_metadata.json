{"hash": "6090003e", "configHash": "ac3665a8", "lockfileHash": "bf62a84a", "browserHash": "6ced6723", "optimized": {"element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "2a2b33d2", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "9ce0d82d", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "eb742136", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "471c7954", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "18c12cef", "needsInterop": false}, "@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "f58f9b03", "needsInterop": false}, "@lucawahlen/vue-human-muscle-anatomy": {"src": "../../@lucawahlen/vue-human-muscle-anatomy/dist/vue-human-muscle-anatomy.js", "file": "@lucawahlen_vue-human-muscle-anatomy.js", "fileHash": "f95888b2", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "64176dec", "needsInterop": true}, "vuedraggable": {"src": "../../vuedraggable/dist/vuedraggable.umd.js", "file": "vuedraggable.js", "fileHash": "ca40e89a", "needsInterop": true}}, "chunks": {"chunk-HYZ2CRGS": {"file": "chunk-HYZ2CRGS.js"}, "chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-PU4SQPHJ": {"file": "chunk-PU4SQPHJ.js"}, "chunk-VOOKDI6C": {"file": "chunk-VOOKDI6C.js"}, "chunk-KJDIXYYC": {"file": "chunk-KJDIXYYC.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}