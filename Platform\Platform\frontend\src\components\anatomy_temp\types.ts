// TypeScript interfaces for anatomy components
export interface MuscleGroup {
  id: string;
  transform?: string;
  paths: string[];
  opacity?: number;
}

export interface SVGAnatomyData {
  viewBox: string;
  muscleGroups: Record<string, MuscleGroup>;
  bodyOutlinePaths: string[];
  hairPaths?: string[]; // For gender differentiation
}

export interface AnatomyProps {
  gender: 'MALE' | 'FEMALE';
  view?: 'front' | 'back';
  defaultMuscleColor: string;
  backgroundColor: string;
  primaryHighlightColor: string;
  secondaryHighlightColor: string;
  primaryOpacity: number;
  secondaryOpacity: number;
  selectedPrimaryMuscleGroups?: string[];
  selectedSecondaryMuscleGroups?: string[];
}

export type MuscleGroupId = 
  | 'abs'
  | 'adductors'
  | 'abductors'
  | 'biceps'
  | 'calves'
  | 'chest'
  | 'forearms'
  | 'frontDelts'
  | 'glutes'
  | 'hamstrings'
  | 'lats'
  | 'lowerBack'
  | 'neck'
  | 'obliques'
  | 'quads'
  | 'rearDelts'
  | 'rotatorCuffs'
  | 'shins'
  | 'sideDelts'
  | 'traps'
  | 'triceps';