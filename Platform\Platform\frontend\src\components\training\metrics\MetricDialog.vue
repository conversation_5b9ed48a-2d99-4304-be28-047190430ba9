<template>
  <!-- 创建/编辑指标对话框 - 已修复重复按钮和基准指标说明 -->
  <el-dialog
    :model-value="modelValue"
    :title="isEditing ? '编辑指标' : '创建指标'"
    width="600px"
    :before-close="handleClose"
    data-testid="metric-dialog"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <el-form
      ref="metricFormRef"
      :model="metricForm"
      :rules="metricRules"
      label-width="120px"
      :show-message="true"
      :inline-message="false"
    >
      <el-form-item label="指标名称" prop="name" ref="metricNameItem">
        <el-input
          v-model="metricForm.name"
          name="name"
          id="metric-name"
          data-testid="metric-name-input"
          placeholder="请输入指标名称"
          @blur="handleMetricNameBlur"
          @input="handleMetricNameInput"
        />
        <!-- 特定的验证错误提示 -->
        <div v-if="metricForm.name && metricForm.name.length === 1" class="validation-error" data-testid="error-min-length" style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
          指标名称至少需要2个字符
        </div>
        <div v-if="metricForm.name && metricForm.name.length > 50" class="validation-error" data-testid="error-max-length" style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
          指标名称最多50个字符
        </div>
      </el-form-item>
      
      
      <el-form-item label="分类">
        <select
          v-model="metricForm.category_id"
          name="category_id"
          class="form-select"
          style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;"
        >
          <option value="">请选择分类</option>
          <option
            v-for="category in categories"
            :key="category.id"
            :value="category.id"
          >
            {{ category.name || category.display_name }}
          </option>
        </select>
      </el-form-item>
      
      <el-form-item label="数据类型">
        <select
          v-model="metricForm.data_type"
          @change="handleDataTypeChange"
          name="data_type"
          id="metric-type"
          data-testid="metric-type"
          :class="['form-select', { 'auto-inferred': isAutoInferred }]"
          style="width: 100%; padding: 8px; border-radius: 4px;"
        >
          <option value="">请选择数据类型</option>
          <option value="integer">整数</option>
          <option value="decimal">小数</option>
          <option value="text">文本</option>
          <option value="boolean">布尔</option>
          <option value="measurement">measurement</option>
        </select>
        <div v-if="dataTypeInferReason" class="infer-reason" style="margin-top: 5px; color: #67c23a; font-size: 12px;">
          <i class="el-icon-info"></i> 智能推断: {{ dataTypeInferReason }}
        </div>
      </el-form-item>
      
      <el-form-item label="单位" prop="unit">
        <el-input
          v-model="metricForm.unit"
          @input="handleUnitInput"
          name="unit"
          id="metric-unit"
          data-testid="metric-unit"
          placeholder="请输入单位（如：次、秒、kg、米等）"
        />
      </el-form-item>
      
      <el-form-item label="描述">
        <textarea
          v-model="metricForm.description"
          name="description"
          rows="3"
          placeholder="请输入指标描述"
          style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px; resize: vertical;"
        />
      </el-form-item>
      
      <el-form-item label="指标角色" prop="metric_role">
        <el-radio-group v-model="metricForm.metric_role">
          <el-radio-button value="normal">
            <i class="el-icon-document"></i> 普通指标
          </el-radio-button>
          <el-radio-button value="neutral">
            <i class="el-icon-data-line"></i> 基准指标
          </el-radio-button>
          <el-radio-button value="target">
            <i class="el-icon-aim"></i> 目标指标
          </el-radio-button>
          <el-radio-button value="actual">
            <i class="el-icon-finished"></i> 实际指标
          </el-radio-button>
        </el-radio-group>
        <div class="role-description">
          {{ getRoleDescription(metricForm.metric_role) }}
        </div>
        
        <!-- 基准指标创建选项 -->
        <div v-if="metricForm.metric_role === 'neutral'" class="baseline-options">
          <el-checkbox v-model="shouldCreateRelatedMetrics" class="baseline-checkbox">
            同时创建对应的目标指标和实际指标
          </el-checkbox>
          <div class="baseline-hint">
            <el-icon class="hint-icon"><InfoFilled /></el-icon>
            建议勾选此选项，系统将自动创建"目标{{ metricForm.name }}"和"实际{{ metricForm.name }}"指标
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="使用频率" v-if="!isEditing">
        <el-rate
          v-model="metricForm.usage_frequency"
          :max="5"
          show-text
          :texts="['很少', '较少', '一般', '较多', '很多']"
        />
        <div class="frequency-buttons" style="margin-top: 10px">
          <el-button
            v-for="freq in [1, 2, 3, 4, 5]"
            :key="freq"
            size="small"
            :type="metricForm.usage_frequency === freq ? 'primary' : ''"
            :data-testid="`usage-frequency-${freq}`"
            @click="metricForm.usage_frequency = freq"
          >
            {{ freq }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" data-testid="save-metric-btn" @click="handleSubmit" :loading="loading">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  metricForm: {
    type: Object,
    required: true
  },
  categories: {
    type: Array,
    default: () => []
  },
  dataTypeOptions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'close'])

const metricFormRef = ref()
const shouldCreateRelatedMetrics = ref(true) // 默认勾选创建相关指标

// 智能推断相关
const isAutoInferred = ref(false) // 是否为自动推断的值
const dataTypeInferReason = ref('') // 推断理由

// 监听名称和单位变化以触发智能推断
watch(() => props.metricForm.name, () => {
  inferDataType()
})

watch(() => props.metricForm.unit, () => {
  inferDataType()
})

// 表单验证规则
const metricRules = computed(() => ({
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}))

const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}

const handleSubmit = async () => {
  try {
    console.log('🔴 SAVE BUTTON CLICKED!!! About to call submitMetricForm')
    await metricFormRef.value?.validate()
    emit('submit', { 
      shouldCreateRelated: metricForm.metric_role === 'neutral' ? shouldCreateRelatedMetrics.value : false
    })
  } catch (error) {
    console.warn('表单验证失败:', error)
  }
}

const handleMetricNameBlur = () => {
  // 处理失焦事件，触发智能推断
  inferDataType()
}

const handleMetricNameInput = () => {
  // 处理输入事件，实时智能推断
  inferDataType()
}

// 智能推断数据类型
const inferDataType = () => {
  // 如果用户已经手动选择了数据类型，且不是自动推断的，不自动覆盖
  // 允许在初始状态或自动推断状态下重新推断
  if (props.metricForm.data_type && !isAutoInferred.value && props.metricForm.data_type !== '') {
    return
  }
  
  const name = (props.metricForm.name || '').toLowerCase()
  const unit = (props.metricForm.unit || '').toLowerCase()
  
  let inferredType = ''
  let inferReason = ''
  
  // 基于单位的推断规则（优先级最高）
  if (unit) {
    if (unit.includes('次') || unit.includes('组') || unit.includes('个')) {
      inferredType = 'integer'
      inferReason = `根据单位"${props.metricForm.unit}"推断为整数类型`
    } else if (unit.includes('秒') || unit.includes('分') || unit.includes('米') || 
               unit.includes('kg') || unit.includes('千克') || unit.includes('%')) {
      inferredType = 'decimal'
      inferReason = `根据单位"${props.metricForm.unit}"推断为小数类型`
    }
  }
  
  // 基于名称的推断规则（如果单位没有推断出结果）
  if (!inferredType && name) {
    if (name.includes('次数') || name.includes('组数') || name.includes('数量')) {
      inferredType = 'integer'
      inferReason = `根据名称"${props.metricForm.name}"推断为整数类型`
    } else if (name.includes('时间') || name.includes('距离') || name.includes('重量') || 
               name.includes('速度') || name.includes('百分比')) {
      inferredType = 'decimal'
      inferReason = `根据名称"${props.metricForm.name}"推断为小数类型`
    } else if (name.includes('日期')) {
      inferredType = 'date'
      inferReason = `根据名称"${props.metricForm.name}"推断为日期类型`
    } else if (name.includes('是否') || name.includes('有无')) {
      inferredType = 'boolean'
      inferReason = `根据名称"${props.metricForm.name}"推断为布尔类型`
    } else if (name.includes('姓名') || name.includes('备注') || name.includes('描述') || 
               name.includes('类型') || name.includes('名称')) {
      inferredType = 'text'
      inferReason = `根据名称"${props.metricForm.name}"推断为文本类型`
    }
  }
  
  // 如果推断出了类型，自动设置
  if (inferredType) {
    props.metricForm.data_type = inferredType
    isAutoInferred.value = true
    dataTypeInferReason.value = inferReason
  } else {
    // 没有推断出结果时，清空推断原因
    dataTypeInferReason.value = ''
    isAutoInferred.value = false
  }
}

// 处理单位输入变化
const handleUnitInput = () => {
  inferDataType()
}

// 处理数据类型手动更改
const handleDataTypeChange = () => {
  // 用户手动选择后，标记为非自动推断
  isAutoInferred.value = false
  dataTypeInferReason.value = ''
}

// 获取角色描述
const getRoleDescription = (role) => {
  const descriptions = {
    'normal': '普通记录指标，如备注、状态、天气等信息',
    'neutral': '基准指标用于定义可测量的核心概念，如"完成时间"、"重量"等。通常需要配套的目标值和实际值指标。',
    'target': '目标值指标，如"目标完成时间"、"目标重量"',
    'actual': '实际值指标，如"实际完成时间"、"实际重量"'
  }
  return descriptions[role] || ''
}
</script>

<style lang="scss" scoped>
/* ========== 对话框主容器样式 ========== */
.metric-dialog {
  margin-top: 8vh;
}

/* 自动推断样式 */
.form-select.auto-inferred {
  border: 1px solid #67c23a !important;
  background-color: #f0f9ff !important;
}

.form-select {
  border: 1px solid #dcdfe6;
  background-color: white;
  transition: all 0.3s ease;
}

/* ========== Element Plus 对话框深度样式 ========== */
:deep(.el-dialog) {
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  width: 680px;
  max-width: 90vw;
}

/* 对话框头部 - 深色背景 */
:deep(.el-dialog__header) {
  background: #2c3e50;
  padding: 18px 24px;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
  height: 56px;
  box-sizing: border-box;
}

/* 对话框标题 - 白色文字 */
:deep(.el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2;
}

/* 关闭按钮 - 白色样式 */
:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
}

:deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

:deep(.el-dialog__close:hover) {
  color: #ecf0f1;
}

/* 对话框主体内容 - 深色背景 */
:deep(.el-dialog__body) {
  padding: 0;
  border-radius: 0 0 8px 8px;
  background: #2c3e50;
}

/* ========== 表单样式 - 深色主题 ========== */
:deep(.el-form) {
  padding: 24px;
  background: #2c3e50;
  border-radius: 0 0 8px 8px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

:deep(.el-form-item__content) {
  flex: 1;
}

/* 表单标签统一宽度 - 浅灰色文字 */
:deep(.el-form-item__label) {
  width: 100px !important;
  font-weight: 500;
  color: #95a5a6;
  line-height: 1.5;
  text-align: right;
  padding-right: 20px;
  font-size: 14px;
}

/* Element Plus 输入框样式优化 - 确保白色背景 */
:deep(.el-input__wrapper) {
  background: #ffffff !important;
  border: 1px solid #dcdfe6 !important;
  border-radius: 4px !important;
  height: 36px !important;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc !important;
  box-shadow: none;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

:deep(.el-input__inner) {
  background: #ffffff !important;
  color: #303133 !important;
  font-size: 14px;
  padding: 0 12px;
  height: 34px;
  line-height: 34px;
}

/* ========== 原生HTML元素样式 ========== */
select {
  width: 100%; 
  height: 36px;
  padding: 0 32px 0 12px; 
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #ffffff;
  font-size: 14px;
  color: #303133;
  transition: border-color 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23909399' d='m2 0-2 2h4zm0 5 2-2h-4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 8px 10px;
}

select:hover {
  border-color: #c0c4cc;
}

select:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

textarea {
  width: 100%; 
  padding: 8px 12px; 
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #ffffff;
  font-size: 14px;
  color: #303133;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
  font-family: inherit;
  line-height: 1.5;
}

textarea:hover {
  border-color: #c0c4cc;
}

textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

textarea::placeholder {
  color: #c0c4cc;
}

/* ========== 复选框和单选按钮样式 ========== */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  cursor: pointer;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

:deep(.el-radio-button) {
  border-radius: 4px;
  margin-bottom: 8px;
}

:deep(.el-radio-button__inner) {
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  background: #ffffff !important;
  color: #303133 !important;
  border: 1px solid #dcdfe6 !important;
}

:deep(.el-radio-button__inner:hover) {
  background: #ecf5ff !important;
  border-color: #409eff !important;
  color: #409eff !important;
}

:deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: #409eff !important;
  border-color: #409eff !important;
  color: #ffffff !important;
  box-shadow: none !important;
}

/* 现在全局已使用蓝色主题，不需要特殊覆盖 */

:deep(.el-radio-button__inner i) {
  font-size: 16px;
}

/* 角色描述文字样式 */
.role-description {
  margin-top: 8px;
  font-size: 13px;
  color: #95a5a6;
  line-height: 1.4;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 基准指标选项样式 */
.baseline-options {
  margin-top: 12px;
  padding: 12px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 6px;
}

.baseline-checkbox :deep(.el-checkbox__label) {
  color: #ecf0f1 !important;
  font-weight: 500;
  font-size: 14px;
}

.baseline-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.baseline-hint {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #bdc3c7;
  line-height: 1.4;
}

.hint-icon {
  color: #409eff;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* ========== 频率按钮组 ========== */
.frequency-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.frequency-buttons :deep(.el-button) {
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  height: 32px;
  min-width: 48px;
  transition: all 0.2s ease;
  border: none;
  background: #67748e;
  color: #95a5a6;
}

.frequency-buttons :deep(.el-button.is-active) {
  background: #ff976a;
  color: white;
}

/* ========== 已删除维度配置区域样式 ========== */

/* ========== 星级评分 ========== */
:deep(.el-rate) {
  line-height: 1;
  margin: 8px 0;
}

:deep(.el-rate__item) {
  margin-right: 4px;
}

:deep(.el-rate__icon) {
  color: #f7ba2a;
}

:deep(.el-rate__icon.el-icon) {
  font-size: 16px;
}

/* ========== 错误提示样式 ========== */
.validation-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  padding-top: 4px;
}

/* ========== 对话框底部按钮区域 - 深色主题 ========== */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: #2c3e50;
  border-radius: 0 0 8px 8px;
}

.dialog-footer :deep(.el-button) {
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dialog-footer :deep(.el-button--primary) {
  background: #409eff;
  border-color: #409eff;
}

.dialog-footer :deep(.el-button--primary:hover) {
  background: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-1px);
}

.dialog-footer :deep(.el-button--default) {
  background: #f5f7fa;
  border-color: #dcdfe6;
  color: #606266;
}

.dialog-footer :deep(.el-button--default:hover) {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* ========== 响应式调整 ========== */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95vw;
    margin: 0 auto;
  }
  
  :deep(.el-form) {
    padding: 20px 16px;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
    font-size: 13px;
  }
  
  .frequency-buttons {
    gap: 6px;
  }
  
  /* 已删除维度配置样式 */
}
</style>