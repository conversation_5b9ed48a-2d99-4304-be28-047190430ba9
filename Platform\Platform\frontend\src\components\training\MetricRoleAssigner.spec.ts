import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import MetricRoleAssigner from './MetricRoleAssigner.vue'
import type { Metric, MetricRole } from '@/types'

// Mock draggable component
vi.mock('vuedraggable', () => ({
  default: {
    name: 'draggable',
    template: '<div><slot /></div>',
    props: ['modelValue', 'group', 'animation', 'class']
  }
}))

describe('MetricRoleAssigner.vue', () => {
  let wrapper: any
  
  const mockMetrics: Metric[] = [
    {
      id: 1,
      name: '重量',
      display_name: '重量',
      unit: 'kg',
      category: '力量',
      data_type: 'numeric',
      description: '训练重量'
    },
    {
      id: 2,
      name: '次数',
      display_name: '次数',
      unit: '次',
      category: '量度',
      data_type: 'numeric',
      description: '完成次数'
    },
    {
      id: 3,
      name: '组数',
      display_name: '组数',
      unit: '组',
      category: '量度',
      data_type: 'numeric',
      description: '训练组数'
    },
    {
      id: 4,
      name: '温度',
      display_name: '环境温度',
      unit: '°C',
      category: '环境',
      data_type: 'numeric',
      description: '训练环境温度'
    },
    {
      id: 5,
      name: '湿度',
      display_name: '环境湿度',
      unit: '%',
      category: '环境',
      data_type: 'numeric',
      description: '训练环境湿度'
    },
    {
      id: 6,
      name: '心率',
      display_name: '平均心率',
      unit: 'bpm',
      category: '生理',
      data_type: 'numeric',
      description: '训练过程平均心率'
    }
  ]
  
  beforeEach(() => {
    setActivePinia(createPinia())
    
    wrapper = mount(MetricRoleAssigner, {
      props: {
        availableMetrics: mockMetrics
      },
      global: {
        plugins: [createPinia()],
        stubs: {
          'el-tag': true,
          'el-icon': true,
          'el-input': true,
          'el-select': true,
          'el-option': true,
          'el-button': true,
          'el-alert': true,
          'draggable': {
            template: '<div class="draggable-mock"><slot /></div>',
            props: ['modelValue', 'group', 'animation']
          },
          'MetricCard': {
            template: '<div class="metric-card-mock">{{ metric.name }}</div>',
            props: ['metric', 'role']
          }
        }
      }
    })
  })
  
  afterEach(() => {
    wrapper?.unmount()
  })
  
  describe('Component Initialization', () => {
    it('should render the metric role assigner correctly', () => {
      expect(wrapper.find('.metric-role-assigner').exists()).toBe(true)
      expect(wrapper.find('.assigner-header').exists()).toBe(true)
      expect(wrapper.find('.role-sections').exists()).toBe(true)
    })
    
    it('should display all three role sections', () => {
      const roleSections = wrapper.findAll('.role-section')
      expect(roleSections.length).toBe(3)
      
      expect(wrapper.find('.role-section.environment').exists()).toBe(true)
      expect(wrapper.find('.role-section.measurement').exists()).toBe(true)
      expect(wrapper.find('.role-section.variation').exists()).toBe(true)
    })
    
    it('should display available metrics pool', () => {
      expect(wrapper.find('.available-metrics-pool').exists()).toBe(true)
      expect(wrapper.find('.pool-header').exists()).toBe(true)
      expect(wrapper.find('.metrics-grid').exists()).toBe(true)
    })
    
    it('should show search and filter controls', () => {
      expect(wrapper.find('.pool-controls').exists()).toBe(true)
      expect(wrapper.find('el-input-stub[placeholder="搜索指标..."]').exists()).toBe(true)
      expect(wrapper.find('el-select-stub[placeholder="筛选分类"]').exists()).toBe(true)
    })
  })
  
  describe('Metric Assignment', () => {
    it('should start with all metrics unassigned', () => {
      const vm = wrapper.vm as any
      expect(vm.environmentMetrics.length).toBe(0)
      expect(vm.measurementMetrics.length).toBe(0)
      expect(vm.variationMetrics.length).toBe(0)
      expect(vm.unassignedMetrics.length).toBe(mockMetrics.length)
    })
    
    it('should assign metrics to roles correctly', async () => {
      const vm = wrapper.vm as any
      
      // Add metrics to different roles
      vm.addMetricToRole(1, 'measurement')
      vm.addMetricToRole(2, 'measurement')
      vm.addMetricToRole(4, 'environment')
      vm.addMetricToRole(6, 'variation')
      
      await wrapper.vm.$nextTick()
      
      expect(vm.measurementMetrics.length).toBe(2)
      expect(vm.environmentMetrics.length).toBe(1)
      expect(vm.variationMetrics.length).toBe(1)
      expect(vm.unassignedMetrics.length).toBe(2)
    })
    
    it('should prevent duplicate assignments', async () => {
      const vm = wrapper.vm as any
      
      // Try to assign the same metric twice
      vm.addMetricToRole(1, 'measurement')
      vm.addMetricToRole(1, 'measurement')
      
      await wrapper.vm.$nextTick()
      
      expect(vm.measurementMetrics.length).toBe(1)
    })
    
    it('should emit assignments-changed event', async () => {
      const vm = wrapper.vm as any
      vm.addMetricToRole(1, 'measurement')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('assignments-changed')).toBeTruthy()
      const emittedAssignments = wrapper.emitted('assignments-changed')[0][0]
      expect(emittedAssignments[1]).toBe('measurement')
    })
  })
  
  describe('Role Changes', () => {
    beforeEach(async () => {
      const vm = wrapper.vm as any
      vm.addMetricToRole(1, 'measurement')
      vm.addMetricToRole(2, 'environment')
      await wrapper.vm.$nextTick()
    })
    
    it('should change metric role correctly', async () => {
      const vm = wrapper.vm as any
      
      // Change metric 1 from measurement to variation
      vm.changeMetricRole(1, 'measurement', 'variation')
      
      await wrapper.vm.$nextTick()
      
      expect(vm.measurementMetrics.length).toBe(0)
      expect(vm.variationMetrics.length).toBe(1)
      expect(vm.variationMetrics[0].id).toBe(1)
    })
    
    it('should remove metric from role correctly', async () => {
      const vm = wrapper.vm as any
      
      vm.removeMetricFromRole(1, 'measurement')
      
      await wrapper.vm.$nextTick()
      
      expect(vm.measurementMetrics.length).toBe(0)
      expect(vm.unassignedMetrics.length).toBe(mockMetrics.length - 1) // One still in environment
    })
  })
  
  describe('Search and Filtering', () => {
    it('should filter metrics by search keyword', async () => {
      await wrapper.setData({ searchKeyword: '重量' })
      
      const vm = wrapper.vm as any
      const filtered = vm.filteredUnassignedMetrics
      
      expect(filtered.length).toBe(1)
      expect(filtered[0].name).toBe('重量')
    })
    
    it('should filter metrics by category', async () => {
      await wrapper.setData({ categoryFilter: '环境' })
      
      const vm = wrapper.vm as any
      const filtered = vm.filteredUnassignedMetrics
      
      expect(filtered.length).toBe(2) // temperature and humidity
      expect(filtered.every((m: Metric) => m.category === '环境')).toBe(true)
    })
    
    it('should combine search and category filters', async () => {
      await wrapper.setData({ 
        searchKeyword: '温度',
        categoryFilter: '环境'
      })
      
      const vm = wrapper.vm as any
      const filtered = vm.filteredUnassignedMetrics
      
      expect(filtered.length).toBe(1)
      expect(filtered[0].name).toBe('温度')
    })
    
    it('should extract unique categories correctly', () => {
      const vm = wrapper.vm as any
      const categories = vm.metricCategories
      
      expect(categories).toContain('力量')
      expect(categories).toContain('量度')
      expect(categories).toContain('环境')
      expect(categories).toContain('生理')
    })
  })
  
  describe('Validation', () => {
    it('should validate that measurement metrics are required', () => {
      const vm = wrapper.vm as any
      
      // No metrics assigned
      expect(vm.validationErrors).toContain('度量指标区至少需要一个指标才能继续')
      expect(vm.isValidConfiguration).toBe(false)
    })
    
    it('should pass validation when measurement metrics are assigned', async () => {
      const vm = wrapper.vm as any
      
      vm.addMetricToRole(1, 'measurement')
      await wrapper.vm.$nextTick()
      
      expect(vm.validationErrors.length).toBe(0)
      expect(vm.isValidConfiguration).toBe(true)
    })
    
    it('should emit validation-changed event', async () => {
      const vm = wrapper.vm as any
      
      vm.addMetricToRole(1, 'measurement')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('validation-changed')).toBeTruthy()
      const emittedValidation = wrapper.emitted('validation-changed').slice(-1)[0]
      expect(emittedValidation[0]).toBe(true) // isValid
      expect(emittedValidation[1]).toEqual([]) // errors
    })
  })
  
  describe('Reset and Confirmation', () => {
    beforeEach(async () => {
      const vm = wrapper.vm as any
      vm.addMetricToRole(1, 'measurement')
      vm.addMetricToRole(2, 'environment')
      vm.addMetricToRole(3, 'variation')
      await wrapper.vm.$nextTick()
    })
    
    it('should reset all assignments correctly', async () => {
      const vm = wrapper.vm as any
      
      expect(vm.hasAssignments).toBe(true)
      
      vm.resetAssignments()
      await wrapper.vm.$nextTick()
      
      expect(vm.environmentMetrics.length).toBe(0)
      expect(vm.measurementMetrics.length).toBe(0)
      expect(vm.variationMetrics.length).toBe(0)
      expect(vm.unassignedMetrics.length).toBe(mockMetrics.length)
      expect(vm.hasAssignments).toBe(false)
    })
    
    it('should confirm assignments when valid', async () => {
      const vm = wrapper.vm as any
      
      vm.confirmAssignments()
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('assignments-changed')).toBeTruthy()
    })
    
    it('should show total assigned metrics count', () => {
      const vm = wrapper.vm as any
      expect(vm.totalAssignedMetrics).toBe(3)
    })
  })
  
  describe('Initial Assignments', () => {
    it('should load initial assignments correctly', async () => {
      const initialAssignments = {
        1: 'measurement' as MetricRole,
        2: 'measurement' as MetricRole,
        4: 'environment' as MetricRole
      }
      
      const wrapperWithInitial = mount(MetricRoleAssigner, {
        props: {
          availableMetrics: mockMetrics,
          initialAssignments
        },
        global: {
          plugins: [createPinia()],
          stubs: {
            'el-tag': true,
            'el-icon': true,
            'el-input': true,
            'el-select': true,
            'el-option': true,
            'el-button': true,
            'el-alert': true,
            'draggable': {
              template: '<div class="draggable-mock"><slot /></div>',
              props: ['modelValue', 'group', 'animation']
            },
            'MetricCard': {
              template: '<div class="metric-card-mock">{{ metric.name }}</div>',
              props: ['metric', 'role']
            }
          }
        }
      })
      
      await wrapperWithInitial.vm.$nextTick()
      
      const vm = wrapperWithInitial.vm as any
      expect(vm.measurementMetrics.length).toBe(2)
      expect(vm.environmentMetrics.length).toBe(1)
      expect(vm.variationMetrics.length).toBe(0)
      
      wrapperWithInitial.unmount()
    })
  })
  
  describe('Drag and Drop States', () => {
    it('should handle drag start and end events', () => {
      const vm = wrapper.vm as any
      
      vm.onDragStart()
      // Verify drag start behavior if needed
      
      vm.onDragEnd()
      expect(vm.dragState.environment).toBe(false)
      expect(vm.dragState.measurement).toBe(false)
      expect(vm.dragState.variation).toBe(false)
    })
    
    it('should handle metric moved events', async () => {
      const vm = wrapper.vm as any
      const mockEvent = { added: { element: mockMetrics[0] } }
      
      vm.onMetricMoved(mockEvent, 'measurement')
      
      expect(wrapper.emitted('assignments-changed')).toBeTruthy()
    })
  })
  
  describe('Accessibility and UX', () => {
    it('should display metric counts in section headers', () => {
      const tagStubs = wrapper.findAll('el-tag-stub')
      expect(tagStubs.length).toBeGreaterThan(0)
    })
    
    it('should show empty zones when no metrics assigned', () => {
      const emptyZones = wrapper.findAll('.empty-zone')
      expect(emptyZones.length).toBe(3) // One for each role section
    })
    
    it('should show required badge for measurement section', () => {
      const measurementSection = wrapper.find('.role-section.measurement')
      expect(measurementSection.exists()).toBe(true)
      // Check for required indication in DOM structure
    })
  })

  describe('Enhanced Drag and Drop Visual Feedback', () => {
    it('should initialize drag options with visual feedback', () => {
      const vm = wrapper.vm as any
      expect(vm.dragOptions).toBeDefined()
      expect(vm.dragOptions.ghostClass).toBe('ghost-card')
      expect(vm.dragOptions.dragClass).toBe('dragging-card')
      expect(vm.dragOptions.chosenClass).toBe('chosen-card')
    })

    it('should show drop zones when dragging starts', async () => {
      const vm = wrapper.vm as any
      
      // Mock drag start
      if (vm.dragOptions.onStart) {
        vm.dragOptions.onStart({})
      }
      
      expect(vm.showDropZones).toBe(true)
    })

    it('should hide drop zones when dragging ends', async () => {
      const vm = wrapper.vm as any
      
      // Start dragging first
      if (vm.dragOptions.onStart) {
        vm.dragOptions.onStart({})
      }
      
      // Then end dragging
      if (vm.dragOptions.onEnd) {
        vm.dragOptions.onEnd({})
      }
      
      expect(vm.showDropZones).toBe(false)
    })

    it('should add body class when dragging', async () => {
      const vm = wrapper.vm as any
      
      if (vm.dragOptions.onStart) {
        vm.dragOptions.onStart({})
      }
      
      // Note: In a real browser, document.body.classList would be modified
      // In this test environment, we just verify the function is called
      expect(vm.showDropZones).toBe(true)
    })
  })

  describe('Batch Operations', () => {
    it('should initialize batch operations functions', () => {
      const vm = wrapper.vm as any
      expect(vm.batchOperations).toBeDefined()
      expect(vm.batchOperations.selectAll).toBeInstanceOf(Function)
      expect(vm.batchOperations.clearSelection).toBeInstanceOf(Function)
      expect(vm.batchOperations.assignToRole).toBeInstanceOf(Function)
    })

    it('should select all unassigned metrics', async () => {
      const vm = wrapper.vm as any
      
      vm.batchOperations.selectAll()
      await wrapper.vm.$nextTick()
      
      expect(vm.selectedMetrics.length).toBe(mockMetrics.length)
    })

    it('should clear all selections', async () => {
      const vm = wrapper.vm as any
      
      // First select some metrics
      vm.selectedMetrics = [1, 2, 3]
      
      vm.batchOperations.clearSelection()
      await wrapper.vm.$nextTick()
      
      expect(vm.selectedMetrics.length).toBe(0)
    })

    it('should assign selected metrics to role', async () => {
      const vm = wrapper.vm as any
      
      // Select some metrics
      vm.selectedMetrics = [1, 2]
      
      vm.batchOperations.assignToRole('measurement')
      await wrapper.vm.$nextTick()
      
      expect(vm.measurementMetrics.length).toBe(2)
      expect(vm.selectedMetrics.length).toBe(0) // Should clear after assignment
    })

    it('should show batch operations UI when metrics are available', async () => {
      expect(wrapper.find('.batch-operations').exists()).toBe(true)
    })

    it('should handle select all checkbox correctly', async () => {
      const vm = wrapper.vm as any
      
      vm.handleSelectAll(true)
      expect(vm.selectedMetrics.length).toBe(mockMetrics.length)
      
      vm.handleSelectAll(false)
      expect(vm.selectedMetrics.length).toBe(0)
    })

    it('should toggle individual metric selection', async () => {
      const vm = wrapper.vm as any
      
      // Select a metric
      vm.toggleMetricSelection(1)
      expect(vm.selectedMetrics).toContain(1)
      
      // Deselect the same metric
      vm.toggleMetricSelection(1)
      expect(vm.selectedMetrics).not.toContain(1)
    })
  })

  describe('Keyboard Navigation', () => {
    beforeEach(() => {
      // Mock document.addEventListener
      document.addEventListener = vi.fn()
      document.removeEventListener = vi.fn()
    })

    it('should initialize keyboard navigation state', () => {
      const vm = wrapper.vm as any
      expect(vm.currentFocusIndex).toBe(-1)
      expect(vm.keyboardNavigationActive).toBe(false)
    })

    it('should handle arrow down key navigation', async () => {
      const vm = wrapper.vm as any
      
      const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' })
      keyEvent.preventDefault = vi.fn()
      
      vm.handleKeyDown(keyEvent)
      
      expect(keyEvent.preventDefault).toHaveBeenCalled()
      expect(vm.keyboardNavigationActive).toBe(true)
      expect(vm.currentFocusIndex).toBe(0)
    })

    it('should handle arrow up key navigation', async () => {
      const vm = wrapper.vm as any
      vm.currentFocusIndex = 1
      
      const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowUp' })
      keyEvent.preventDefault = vi.fn()
      
      vm.handleKeyDown(keyEvent)
      
      expect(keyEvent.preventDefault).toHaveBeenCalled()
      expect(vm.currentFocusIndex).toBe(0)
    })

    it('should handle space key for selection', async () => {
      const vm = wrapper.vm as any
      vm.currentFocusIndex = 0
      
      const keyEvent = new KeyboardEvent('keydown', { key: ' ' })
      keyEvent.preventDefault = vi.fn()
      
      vm.handleKeyDown(keyEvent)
      
      expect(keyEvent.preventDefault).toHaveBeenCalled()
      expect(vm.selectedMetrics).toContain(mockMetrics[0].id)
    })

    it('should handle escape key for clearing selections', async () => {
      const vm = wrapper.vm as any
      vm.selectedMetrics = [1, 2]
      vm.currentFocusIndex = 1
      vm.keyboardNavigationActive = true
      
      const keyEvent = new KeyboardEvent('keydown', { key: 'Escape' })
      keyEvent.preventDefault = vi.fn()
      
      vm.handleKeyDown(keyEvent)
      
      expect(keyEvent.preventDefault).toHaveBeenCalled()
      expect(vm.selectedMetrics.length).toBe(0)
      expect(vm.currentFocusIndex).toBe(-1)
      expect(vm.keyboardNavigationActive).toBe(false)
    })

    it('should handle number keys for role assignment', async () => {
      const vm = wrapper.vm as any
      vm.selectedMetrics = [1]
      
      const keyEvent = new KeyboardEvent('keydown', { key: '2' })
      keyEvent.preventDefault = vi.fn()
      
      vm.handleKeyDown(keyEvent)
      
      expect(keyEvent.preventDefault).toHaveBeenCalled()
      expect(vm.measurementMetrics.length).toBe(1)
    })

    it('should show keyboard navigation hints when active', async () => {
      const vm = wrapper.vm as any
      vm.keyboardNavigationActive = true
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.keyboard-navigation-hint').exists()).toBe(true)
    })

    it('should add event listeners on mount', () => {
      // Event listeners should be added when component mounts
      expect(document.addEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })
  })

  describe('MetricCard Selection Integration', () => {
    it('should pass selected state to metric cards', async () => {
      const vm = wrapper.vm as any
      vm.selectedMetrics = [mockMetrics[0].id]
      
      await wrapper.vm.$nextTick()
      
      // Check that the selected prop is passed correctly
      // This would be verified through the MetricCard stub behavior
      expect(vm.selectedMetrics).toContain(mockMetrics[0].id)
    })

    it('should pass focused state to metric cards', async () => {
      const vm = wrapper.vm as any
      vm.keyboardNavigationActive = true
      vm.currentFocusIndex = 0
      
      await wrapper.vm.$nextTick()
      
      // Verify that focus state is managed correctly
      expect(vm.keyboardNavigationActive).toBe(true)
      expect(vm.currentFocusIndex).toBe(0)
    })
  })
})