<!--
  训练统计时间显示组件
  Training Statistics Time Display Component
-->

<template>
  <div class="training-stats-display" data-testid="training-stats-display">
    <div class="stats-container">
      <!-- 标题区域 -->
      <div class="stats-header">
        <h3 class="stats-title">
          <el-icon><Clock /></el-icon>
          训练统计时间
        </h3>
        <div class="stats-subtitle">
          基于当前指标配置的时间统计信息
        </div>
      </div>
      
      <!-- 主要统计数据 -->
      <div class="stats-grid">
        <!-- 总用时预估 -->
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(totalEstimatedTime) }}</div>
            <div class="stat-label">预计总用时</div>
          </div>
        </div>
        
        <!-- 训练时间 -->
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Stopwatch /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(exerciseTime) }}</div>
            <div class="stat-label">训练执行时间</div>
          </div>
        </div>
        
        <!-- 记录时间 -->
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(recordingTime) }}</div>
            <div class="stat-label">数据记录时间</div>
          </div>
        </div>
        
        <!-- 休息时间 -->
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Coffee /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatTime(restTime) }}</div>
            <div class="stat-label">休息间歇时间</div>
          </div>
        </div>
      </div>
      
      <!-- 详细时间分解 -->
      <div class="time-breakdown">
        <div class="breakdown-header">
          <h4>时间构成分析</h4>
          <el-switch
            v-model="showDetailed"
            active-text="详细"
            inactive-text="简化"
            size="small"
          />
        </div>
        
        <div v-if="showDetailed" class="breakdown-details">
          <!-- 训练结构时间 -->
          <div class="breakdown-section">
            <div class="section-title">训练执行 ({{ formatTime(exerciseTime) }})</div>
            <div class="breakdown-items">
              <div class="breakdown-item">
                <span class="item-label">动作执行</span>
                <span class="item-value">{{ formatTime(actionExecutionTime) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">组间休息</span>
                <span class="item-value">{{ formatTime(setRestTime) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">调整准备</span>
                <span class="item-value">{{ formatTime(adjustmentTime) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 数据记录时间 -->
          <div class="breakdown-section">
            <div class="section-title">数据记录 ({{ formatTime(recordingTime) }})</div>
            <div class="breakdown-items">
              <div class="breakdown-item">
                <span class="item-label">指标录入 ({{ totalMetricsCount }}个)</span>
                <span class="item-value">{{ formatTime(metricsInputTime) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">数据校验</span>
                <span class="item-value">{{ formatTime(validationTime) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 时间优化建议 -->
        <div v-if="showOptimizationTips" class="optimization-tips">
          <el-alert
            :title="optimizationTip.title"
            :description="optimizationTip.description"
            :type="optimizationTip.type"
            show-icon
            :closable="false"
          />
        </div>
      </div>
      
      <!-- 时间对比 -->
      <div class="time-comparison">
        <div class="comparison-header">
          <h4>效率对比</h4>
        </div>
        <div class="comparison-bars">
          <div class="comparison-item">
            <span class="comparison-label">训练执行</span>
            <div class="progress-bar">
              <div class="progress-fill exercise" :style="{ width: `${exerciseTimePercent}%` }"></div>
            </div>
            <span class="comparison-percent">{{ exerciseTimePercent.toFixed(0) }}%</span>
          </div>
          <div class="comparison-item">
            <span class="comparison-label">数据记录</span>
            <div class="progress-bar">
              <div class="progress-fill recording" :style="{ width: `${recordingTimePercent}%` }"></div>
            </div>
            <span class="comparison-percent">{{ recordingTimePercent.toFixed(0) }}%</span>
          </div>
          <div class="comparison-item">
            <span class="comparison-label">休息间歇</span>
            <div class="progress-bar">
              <div class="progress-fill rest" :style="{ width: `${restTimePercent}%` }"></div>
            </div>
            <span class="comparison-percent">{{ restTimePercent.toFixed(0) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Clock, Timer, Stopwatch, Edit, Coffee } from '@element-plus/icons-vue'

interface Props {
  // 训练配置参数
  sets?: number
  reps?: number
  measurementGranularity?: 'overall' | 'set' | 'rep'
  
  // 指标数据
  measurementMetrics?: any[]
  variationMetrics?: any[]
  environmentMetrics?: any[]
  
  // 显示选项
  showOptimizationTips?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  sets: 3,
  reps: 8,
  measurementGranularity: 'set',
  measurementMetrics: () => [],
  variationMetrics: () => [],
  environmentMetrics: () => [],
  showOptimizationTips: true,
  compact: false
})

const emit = defineEmits<{
  timeUpdate: [totalTime: number]
}>()

// 响应式状态
const showDetailed = ref(false)

// 计算属性 - 指标数量统计
const totalMetricsCount = computed(() => {
  return (props.measurementMetrics?.length || 0) + 
         (props.variationMetrics?.length || 0) + 
         (props.environmentMetrics?.length || 0)
})

const measurementMetricsCount = computed(() => props.measurementMetrics?.length || 0)
const variationMetricsCount = computed(() => props.variationMetrics?.length || 0)
const environmentMetricsCount = computed(() => props.environmentMetrics?.length || 0)

// 时间计算常量 (秒)
const TIME_CONSTANTS = {
  // 基础执行时间
  ACTION_EXECUTION_BASE: 30, // 每组动作执行基础时间
  ACTION_EXECUTION_PER_REP: 8, // 每次额外时间
  
  // 休息时间
  REST_BETWEEN_SETS: 90, // 组间休息
  REST_ADJUSTMENT: 15, // 调整准备时间
  
  // 记录时间
  METRIC_INPUT_BASE: 3, // 每个指标录入基础时间
  METRIC_INPUT_COMPLEX: 5, // 复杂指标额外时间
  DATA_VALIDATION: 10, // 数据校验时间
  
  // 颗粒度调整系数
  GRANULARITY_MULTIPLIER: {
    overall: 0.6, // 整体记录较快
    set: 1.0, // 按组记录标准
    rep: 1.8 // 按次记录较慢
  }
}

// 计算属性 - 时间计算
const actionExecutionTime = computed(() => {
  const baseTime = TIME_CONSTANTS.ACTION_EXECUTION_BASE * props.sets
  const repTime = TIME_CONSTANTS.ACTION_EXECUTION_PER_REP * props.sets * props.reps
  return baseTime + repTime
})

const setRestTime = computed(() => {
  return TIME_CONSTANTS.REST_BETWEEN_SETS * Math.max(0, props.sets - 1)
})

const adjustmentTime = computed(() => {
  return TIME_CONSTANTS.REST_ADJUSTMENT * props.sets
})

const exerciseTime = computed(() => {
  return actionExecutionTime.value + setRestTime.value + adjustmentTime.value
})

const metricsInputTime = computed(() => {
  const granularityMultiplier = TIME_CONSTANTS.GRANULARITY_MULTIPLIER[props.measurementGranularity!]
  const baseInputTime = totalMetricsCount.value * TIME_CONSTANTS.METRIC_INPUT_BASE
  const complexMetricsTime = variationMetricsCount.value * TIME_CONSTANTS.METRIC_INPUT_COMPLEX
  
  return (baseInputTime + complexMetricsTime) * granularityMultiplier
})

const validationTime = computed(() => {
  return TIME_CONSTANTS.DATA_VALIDATION
})

const recordingTime = computed(() => {
  return metricsInputTime.value + validationTime.value
})

const restTime = computed(() => {
  return setRestTime.value + adjustmentTime.value
})

const totalEstimatedTime = computed(() => {
  return exerciseTime.value + recordingTime.value
})

// 计算属性 - 百分比
const exerciseTimePercent = computed(() => {
  return (exerciseTime.value / totalEstimatedTime.value) * 100
})

const recordingTimePercent = computed(() => {
  return (recordingTime.value / totalEstimatedTime.value) * 100
})

const restTimePercent = computed(() => {
  return (restTime.value / totalEstimatedTime.value) * 100
})

// 计算属性 - 优化建议
const optimizationTip = computed(() => {
  const recordingPercent = recordingTimePercent.value
  
  if (recordingPercent > 40) {
    return {
      title: '数据记录时间较长',
      description: `当前记录时间占 ${recordingPercent.toFixed(0)}%，建议简化指标或优化记录流程`,
      type: 'warning'
    }
  } else if (recordingPercent < 15) {
    return {
      title: '时间配比优秀',
      description: `记录时间仅占 ${recordingPercent.toFixed(0)}%，训练效率较高`,
      type: 'success'
    }
  } else {
    return {
      title: '时间配比合理',
      description: `当前配置的时间分配比较均衡，训练效率良好`,
      type: 'info'
    }
  }
})

const showOptimizationTips = computed(() => {
  return props.showOptimizationTips && totalMetricsCount.value > 0
})

// 工具函数
const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(0)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 
      ? `${minutes}分${remainingSeconds.toFixed(0)}秒` 
      : `${minutes}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const remainingMinutes = Math.floor((seconds % 3600) / 60)
    return remainingMinutes > 0 
      ? `${hours}小时${remainingMinutes}分钟` 
      : `${hours}小时`
  }
}

// 监听变化并通知父组件
watch(
  totalEstimatedTime,
  (newTime) => {
    emit('timeUpdate', newTime)
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.training-stats-display {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  &.compact {
    padding: 16px;
  }
}

.stats-container {
  .stats-header {
    margin-bottom: 20px;
    
    .stats-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 6px 0;
    }
    
    .stats-subtitle {
      font-size: 13px;
      color: #909399;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border-color: #409eff;
    }
    
    &.primary {
      background: linear-gradient(135deg, #409eff, #4dabf7);
      color: white;
      border: none;
      
      .stat-icon {
        color: rgba(255, 255, 255, 0.9);
      }
      
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    .stat-icon {
      font-size: 24px;
      color: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background: rgba(64, 158, 255, 0.1);
      border-radius: 6px;
    }
    
    .stat-content {
      flex: 1;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 2px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
        line-height: 1.2;
      }
    }
  }
  
  .time-breakdown {
    background: #fafbfc;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    
    .breakdown-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        font-size: 15px;
        color: #303133;
      }
    }
    
    .breakdown-details {
      .breakdown-section {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 8px;
          padding-bottom: 4px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .breakdown-items {
          .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 13px;
            
            .item-label {
              color: #909399;
            }
            
            .item-value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .optimization-tips {
      margin-top: 16px;
    }
  }
  
  .time-comparison {
    .comparison-header {
      margin-bottom: 12px;
      
      h4 {
        margin: 0;
        font-size: 15px;
        color: #303133;
      }
    }
    
    .comparison-bars {
      .comparison-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .comparison-label {
          width: 80px;
          font-size: 13px;
          color: #606266;
          text-align: right;
          margin-right: 12px;
        }
        
        .progress-bar {
          flex: 1;
          height: 8px;
          background: #f0f2f5;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 8px;
          
          .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            
            &.exercise {
              background: linear-gradient(135deg, #52c41a, #73d13d);
            }
            
            &.recording {
              background: linear-gradient(135deg, #409eff, #4dabf7);
            }
            
            &.rest {
              background: linear-gradient(135deg, #faad14, #ffc53d);
            }
          }
        }
        
        .comparison-percent {
          width: 40px;
          font-size: 12px;
          color: #909399;
          text-align: right;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .training-stats-display {
    padding: 16px;
    
    .stats-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .stat-card {
      padding: 12px;
      
      .stat-icon {
        width: 32px;
        height: 32px;
        font-size: 20px;
      }
      
      .stat-value {
        font-size: 18px;
      }
    }
    
    .time-breakdown {
      padding: 12px;
    }
    
    .comparison-item {
      .comparison-label {
        width: 60px;
        font-size: 12px;
      }
    }
  }
}

// 主题适配
.athletics-orange-theme {
  .stat-card {
    &.primary {
      background: linear-gradient(135deg, #e76f51, #f4a261);
    }
    
    .stat-icon {
      color: #e76f51;
      background: rgba(231, 111, 81, 0.1);
    }
    
    &:hover {
      border-color: #e76f51;
    }
  }
  
  .progress-fill {
    &.exercise {
      background: linear-gradient(135deg, #e76f51, #f4a261);
    }
    
    &.recording {
      background: linear-gradient(135deg, #2a9d8f, #43aa8b);
    }
    
    &.rest {
      background: linear-gradient(135deg, #f4a261, #e9c46a);
    }
  }
}
</style>