<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑运动员' : '添加运动员'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="form.gender">
              <el-radio label="male">男</el-radio>
              <el-radio label="female">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birth_date">
            <el-date-picker
              v-model="form.birth_date"
              type="date"
              placeholder="选择出生日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专项" prop="speciality">
            <el-select
              v-model="form.speciality"
              placeholder="选择专项"
              style="width: 100%"
              filterable
              allow-create
            >
              <el-option v-for="event in trackEvents" :key="event" :label="event" :value="event" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身高(cm)" prop="height">
            <el-input-number
              v-model="form.height"
              :min="100"
              :max="250"
              placeholder="身高"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="体重(kg)" prop="weight">
            <el-input-number
              v-model="form.weight"
              :min="30"
              :max="200"
              :precision="1"
              placeholder="体重"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { athleteApi } from '@/services/api'
import type { Athlete } from '@/types'

interface AthleteForm {
  name: string
  gender: 'MALE' | 'FEMALE'
  birth_date: string
  speciality: string
  height?: number
  weight?: number
  notes?: string
}

const props = defineProps<{
  modelValue: boolean
  athlete?: Athlete | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.athlete)

const form = reactive<AthleteForm>({
  name: '',
  gender: 'MALE',
  birth_date: '',
  speciality: '',
  height: undefined,
  weight: undefined,
  notes: ''
})

const trackEvents = [
  '100米', '200米', '400米', '800米', '1500米', '3000米', '5000米', '10000米',
  '110米栏', '100米栏', '400米栏', '3000米障碍',
  '跳高', '跳远', '三级跳远', '撑杆跳高',
  '铅球', '铁饼', '标枪', '链球',
  '十项全能', '七项全能',
  '20公里竞走', '50公里竞走',
  '马拉松'
]

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  birth_date: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  speciality: [
    { required: true, message: '请选择专项', trigger: 'change' }
  ]
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    gender: 'MALE',
    birth_date: '',
    speciality: '',
    height: undefined,
    weight: undefined,
    notes: ''
  })
  formRef.value?.clearValidate()
}

const loadAthleteData = () => {
  if (props.athlete) {
    Object.assign(form, {
      name: props.athlete.name,
      gender: props.athlete.gender,
      birth_date: props.athlete.birth_date,
      speciality: props.athlete.speciality,
      height: props.athlete.height,
      weight: props.athlete.weight,
      notes: (props.athlete as any).notes || ''
    })
  } else {
    resetForm()
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
  setTimeout(resetForm, 300)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    loading.value = true
    
    if (isEdit.value && props.athlete) {
      await athleteApi.updateAthlete(props.athlete.id, form)
      ElMessage.success('运动员信息更新成功')
    } else {
      await athleteApi.createAthlete(form)
      ElMessage.success('运动员创建成功')
    }
    
    emit('success')
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

watch(() => props.modelValue, (visible) => {
  if (visible) {
    loadAthleteData()
  }
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }
  
  .el-row {
    .el-col {
      margin-bottom: 10px;
    }
  }
}
</style>