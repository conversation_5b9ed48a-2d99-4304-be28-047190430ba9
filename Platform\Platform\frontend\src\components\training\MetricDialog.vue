<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑指标' : '添加指标'"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      @submit.prevent
    >
      <el-form-item label="指标名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入指标名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="指标分类" prop="category_id">
        <el-select
          v-model="formData.category_id"
          placeholder="请选择指标分类"
          style="width: 100%"
        >
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据类型" prop="data_type">
        <el-select
          v-model="formData.data_type"
          placeholder="请选择数据类型"
          style="width: 100%"
        >
          <el-option label="整数" value="integer" />
          <el-option label="小数" value="decimal" />
          <el-option label="文本" value="string" />
          <el-option label="时间" value="time" />
          <el-option label="日期" value="date" />
          <el-option label="布尔值" value="boolean" />
        </el-select>
      </el-form-item>

      <el-form-item label="单位" prop="unit">
        <el-input
          v-model="formData.unit"
          placeholder="如：kg, 秒, 米等（可选）"
          maxlength="20"
        />
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入指标描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="默认值">
        <el-input
          v-model="formData.default_value"
          placeholder="指标的默认值（可选）"
        />
      </el-form-item>

      <el-form-item label="状态">
        <el-switch
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { createMetric, updateMetric } from '@/api/training'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  metric: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 数据
const formRef = ref()
const visible = ref(false)
const saving = ref(false)

const formData = reactive({
  name: '',
  description: '',
  category_id: null,
  data_type: '',
  unit: '',
  default_value: '',
  is_active: true
})

const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择指标分类', trigger: 'change' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.metric?.id)

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const initForm = () => {
  if (props.metric) {
    // 编辑模式
    Object.assign(formData, {
      name: props.metric.name || '',
      description: props.metric.description || '',
      category_id: props.metric.category_id || null,
      data_type: props.metric.data_type || '',
      unit: props.metric.unit || '',
      default_value: props.metric.default_value || '',
      is_active: props.metric.is_active ?? true
    })
  } else {
    // 新建模式
    resetForm()
  }
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    category_id: null,
    data_type: '',
    unit: '',
    default_value: '',
    is_active: true
  })
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    const data = { ...formData }
    
    if (isEdit.value) {
      await updateMetric(props.metric.id, data)
      ElMessage.success('指标更新成功')
    } else {
      await createMetric(data)
      ElMessage.success('指标创建成功')
    }
    
    visible.value = false
    emit('save')
  } catch (error) {
    ElMessage.error(error.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>