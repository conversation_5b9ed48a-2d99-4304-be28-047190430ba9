<template>
  <el-dialog
    v-model="visible"
    :title="`${config.cycleName || '新周期计划'} - 配置预览`"
    width="80%"
    :before-close="handleClose"
    class="preview-dialog"
    append-to-body
  >
    <el-tabs v-model="activeTab" class="preview-tabs" @tab-change="handleTabChange">
      <!-- 基础配置标签页 -->
      <el-tab-pane label="基础配置" name="basic" data-testid="basic-config-tab">
        <div class="tab-content basic-config-content">
          <div class="config-grid">
            <div class="config-card">
              <div class="card-header">
                <el-icon><User /></el-icon>
                <span>目标群体</span>
              </div>
              <div class="card-content">
                <div class="info-item">
                  <span class="label">性别：</span>
                  <span class="value">{{ formatGender(config.gender) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">运动员等级：</span>
                  <span class="value">{{ formatAthleteLevel(config.athleteLevel) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">专项：</span>
                  <span class="value">{{ formatSpecialties(config.specialties) }}</span>
                </div>
              </div>
            </div>

            <div class="config-card">
              <div class="card-header">
                <el-icon><Calendar /></el-icon>
                <span>训练结构</span>
              </div>
              <div class="card-content">
                <div class="info-item">
                  <span class="label">训练天数：</span>
                  <span class="value">{{ config.trainingDays }}天/周期</span>
                </div>
                <div class="info-item">
                  <span class="label">周期重复：</span>
                  <span class="value">{{ config.cycleRepeats }}次</span>
                </div>
                <div class="info-item">
                  <span class="label">总训练天数：</span>
                  <span class="value highlight">{{ totalDays }}天</span>
                </div>
                <div class="info-item">
                  <span class="label">预计时长：</span>
                  <span class="value">约{{ Math.ceil(totalDays / 7) }}周</span>
                </div>
              </div>
            </div>

            <div v-if="hasAdvancedConfig" class="config-card advanced-card">
              <div class="card-header">
                <el-icon><Setting /></el-icon>
                <span>高级配置</span>
              </div>
              <div class="card-content">
                <div v-if="config.phase" class="info-item">
                  <span class="label">训练阶段：</span>
                  <span class="value">{{ formatPhase(config.phase) }}</span>
                </div>
                <div v-if="config.intensity" class="info-item">
                  <span class="label">训练强度：</span>
                  <span class="value">{{ config.intensity }}%</span>
                  <div class="intensity-bar" data-testid="intensity-bar-basic">
                    <div class="intensity-fill" :style="{ width: `${config.intensity}%` }"></div>
                  </div>
                </div>
                <div v-if="config.loadProgression" class="info-item">
                  <span class="label">负荷递增：</span>
                  <span class="value">{{ formatLoadProgression(config.loadProgression) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 训练安排标签页 -->
      <el-tab-pane label="训练安排" name="schedule" data-testid="schedule-tab">
        <div class="tab-content schedule-content">
          <div class="schedule-layout">
            <div class="calendar-section">
              <div class="section-header">
                <el-icon><Calendar /></el-icon>
                <span>训练日历</span>
              </div>
              <!-- 迷你日历组件 -->
              <div class="mini-calendar" data-testid="mini-calendar">
                <div class="calendar-header">
                  <span class="month-year">{{ currentMonth }}</span>
                  <div class="nav-buttons">
                    <el-button size="small" @click="prevMonth" :icon="ArrowLeft" circle />
                    <el-button size="small" @click="nextMonth" :icon="ArrowRight" circle />
                  </div>
                </div>
                <div class="calendar-grid">
                  <div class="weekdays">
                    <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
                  </div>
                  <div class="days">
                    <div 
                      v-for="day in calendarDays" 
                      :key="`${day.date}-${day.month}`"
                      class="day"
                      :class="{
                        'other-month': day.isOtherMonth,
                        'training-day': day.isTrainingDay,
                        'rest-day': day.isRestDay,
                        'today': day.isToday
                      }"
                    >
                      {{ day.date }}
                    </div>
                  </div>
                </div>
                <div class="calendar-legend">
                  <div class="legend-item">
                    <div class="legend-color training"></div>
                    <span>训练日</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color rest"></div>
                    <span>休息日</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="stats-section">
              <div class="section-header">
                <el-icon><DataAnalysis /></el-icon>
                <span>训练统计</span>
              </div>
              <div class="stats-grid" data-testid="training-stats">
                <div class="stat-card">
                  <div class="stat-value">{{ trainingStats.totalTrainingDays }}</div>
                  <div class="stat-label">总训练天数</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ trainingStats.totalRestDays }}</div>
                  <div class="stat-label">总休息天数</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ trainingStats.weeksCount }}</div>
                  <div class="stat-label">训练周数</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value">{{ trainingStats.cyclesCount }}</div>
                  <div class="stat-label">训练周期</div>
                </div>
              </div>

              <div class="weekly-pattern">
                <div class="pattern-header">周训练模式</div>
                <div class="pattern-days">
                  <div 
                    v-for="(day, index) in weekPattern" 
                    :key="index"
                    class="pattern-day"
                    :class="{ 'is-training': day.isTraining }"
                  >
                    <div class="day-name">{{ day.name }}</div>
                    <div class="day-status">{{ day.isTraining ? '训练' : '休息' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 负荷分析标签页 -->
      <el-tab-pane label="负荷分析" name="analysis" data-testid="analysis-tab">
        <div class="tab-content analysis-content">
          <div class="analysis-layout">
            <div class="chart-section">
              <div class="section-header">
                <el-icon><TrendCharts /></el-icon>
                <span>负荷趋势</span>
              </div>
              <!-- SVG趋势图 -->
              <div class="chart-container" data-testid="svg-trend-chart">
                <svg class="trend-chart" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                  <!-- 网格线 -->
                  <defs>
                    <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                      <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                  
                  <!-- Y轴标签 -->
                  <g class="y-axis">
                    <text x="15" y="25" class="axis-label">高</text>
                    <text x="15" y="105" class="axis-label">中</text>
                    <text x="15" y="185" class="axis-label">低</text>
                  </g>
                  
                  <!-- 趋势线 -->
                  <polyline
                    :points="trendLinePoints"
                    fill="none"
                    stroke="#409eff"
                    stroke-width="2"
                    class="trend-line"
                  />
                  
                  <!-- 数据点 -->
                  <circle
                    v-for="(point, index) in trendPoints"
                    :key="index"
                    :cx="point.x"
                    :cy="point.y"
                    r="4"
                    fill="#409eff"
                    class="data-point"
                  />
                  
                  <!-- X轴标签 -->
                  <g class="x-axis">
                    <text x="60" y="195" class="axis-label">周1</text>
                    <text x="120" y="195" class="axis-label">周2</text>
                    <text x="180" y="195" class="axis-label">周3</text>
                    <text x="240" y="195" class="axis-label">周4</text>
                    <text x="300" y="195" class="axis-label">周5</text>
                    <text x="360" y="195" class="axis-label">周6</text>
                  </g>
                </svg>
              </div>
            </div>

            <div class="analysis-stats">
              <div class="section-header">
                <el-icon><PieChart /></el-icon>
                <span>负荷特征</span>
              </div>
              <div class="load-characteristics" data-testid="load-characteristics">
                <div class="characteristic-item">
                  <div class="char-label">平均强度</div>
                  <div class="char-value">{{ loadAnalysis.averageIntensity }}%</div>
                  <div class="char-bar">
                    <div class="char-fill" :style="{ width: `${loadAnalysis.averageIntensity}%` }"></div>
                  </div>
                </div>
                
                <div class="characteristic-item">
                  <div class="char-label">负荷变化</div>
                  <div class="char-value">{{ loadAnalysis.loadVariation }}</div>
                  <div class="char-description">{{ loadAnalysis.variationDescription }}</div>
                </div>
                
                <div class="characteristic-item">
                  <div class="char-label">恢复建议</div>
                  <div class="char-value">{{ loadAnalysis.recoveryRecommendation }}</div>
                  <div class="char-description">{{ loadAnalysis.recoveryDescription }}</div>
                </div>
                
                <div class="characteristic-item">
                  <div class="char-label">适应性评估</div>
                  <div class="char-value">{{ loadAnalysis.adaptationLevel }}</div>
                  <div class="char-description">{{ loadAnalysis.adaptationDescription }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleConfirm">确认配置</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { User, Calendar, Setting, DataAnalysis, TrendCharts, PieChart, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

// 类型定义
interface CycleConfigForm {
  gender: string
  athleteLevel: string
  specialties: string[]
  trainingDays: number
  cycleRepeats: number
  cycleName: string
  phase?: string
  intensity?: number
  loadProgression?: string
  restDays?: string[]
  targetCategory?: string
  enableTrainingReminder?: boolean
  reminderTime?: number
  description?: string
}

interface Props {
  modelValue: boolean
  config: CycleConfigForm
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', config: CycleConfigForm): void
  (e: 'tab-change', tabName: string): void
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    gender: '',
    athleteLevel: '',
    specialties: [],
    trainingDays: 5,
    cycleRepeats: 4,
    cycleName: ''
  })
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('basic')
const currentMonth = ref('2024年1月')

// 计算属性
const totalDays = computed(() => {
  return (props.config.trainingDays || 0) * (props.config.cycleRepeats || 0)
})

const hasAdvancedConfig = computed(() => {
  return !!(props.config.phase || props.config.intensity || props.config.loadProgression || 
           (props.config.restDays && props.config.restDays.length > 0) || 
           props.config.targetCategory || props.config.enableTrainingReminder)
})

// 日历相关数据
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

const calendarDays = computed(() => {
  // 模拟一个月的日历数据
  const days = []
  const trainingPattern = [1, 2, 3, 4, 5] // 周一到周五训练
  
  for (let i = 1; i <= 31; i++) {
    const dayOfWeek = (i - 1) % 7
    days.push({
      date: i,
      month: 1,
      isOtherMonth: false,
      isTrainingDay: trainingPattern.includes(dayOfWeek),
      isRestDay: !trainingPattern.includes(dayOfWeek),
      isToday: i === 15 // 假设今天是15号
    })
  }
  
  return days
})

// 训练统计数据
const trainingStats = computed(() => {
  const totalDays = props.config.trainingDays * props.config.cycleRepeats
  const restDays = totalDays * 0.3 // 假设30%是休息日
  
  return {
    totalTrainingDays: totalDays - Math.floor(restDays),
    totalRestDays: Math.floor(restDays),
    weeksCount: Math.ceil(totalDays / 7),
    cyclesCount: props.config.cycleRepeats
  }
})

// 周训练模式
const weekPattern = computed(() => {
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const trainingDays = [true, true, true, true, true, false, false] // 周一到周五训练
  
  return days.map((name, index) => ({
    name,
    isTraining: trainingDays[index]
  }))
})

// SVG趋势图数据
const trendPoints = computed(() => {
  const intensity = props.config.intensity || 60
  const baseY = 200 - (intensity / 100 * 160) // 转换为SVG坐标
  
  return [
    { x: 60, y: baseY + 20 },
    { x: 120, y: baseY - 10 },
    { x: 180, y: baseY + 15 },
    { x: 240, y: baseY - 25 },
    { x: 300, y: baseY + 5 },
    { x: 360, y: baseY - 15 }
  ]
})

const trendLinePoints = computed(() => {
  return trendPoints.value.map(p => `${p.x},${p.y}`).join(' ')
})

// 负荷分析数据
const loadAnalysis = computed(() => {
  const intensity = props.config.intensity || 60
  
  return {
    averageIntensity: intensity,
    loadVariation: intensity > 70 ? '高变化' : intensity > 50 ? '中等变化' : '低变化',
    variationDescription: intensity > 70 ? '训练强度波动较大，需要充分恢复' : '训练强度适中，负荷递增稳定',
    recoveryRecommendation: intensity > 70 ? '增加休息日' : '保持当前恢复节奏',
    recoveryDescription: intensity > 70 ? '建议每3天安排1天完全休息' : '当前休息安排合理',
    adaptationLevel: intensity > 70 ? '高适应期' : intensity > 50 ? '适应期' : '基础期',
    adaptationDescription: intensity > 70 ? '身体正在适应高强度训练' : '稳步提升训练水平'
  }
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleConfirm = () => {
  emit('confirm', props.config)
  visible.value = false
}

const handleTabChange = (tabName: string) => {
  emit('tab-change', tabName)
}

const prevMonth = () => {
  // 实现月份切换逻辑
  currentMonth.value = '2023年12月'
}

const nextMonth = () => {
  // 实现月份切换逻辑  
  currentMonth.value = '2024年2月'
}

// 格式化函数
const formatGender = (gender: string): string => {
  const genderMap: Record<string, string> = {
    'MALE': '男性',
    'FEMALE': '女性',
    'MIXED': '混合'
  }
  return genderMap[gender] || gender || '未设置'
}

const formatAthleteLevel = (level: string): string => {
  const levelMap: Record<string, string> = {
    'BEGINNER': '初学者',
    'INTERMEDIATE': '中级',
    'ADVANCED': '高级',
    'PROFESSIONAL': '专业',
    'ELITE': '精英'
  }
  return levelMap[level] || level || '未设置'
}

const formatSpecialties = (specialties: string[]): string => {
  if (!specialties || specialties.length === 0) return '未设置'
  return specialties.join('、')
}

const formatPhase = (phase: string): string => {
  const phaseMap: Record<string, string> = {
    'PREPARATION': '准备期',
    'SPECIFIC': '专门准备期',
    'COMPETITION': '比赛期',
    'RECOVERY': '恢复期',
    'TRANSITION': '过渡期',
    'ADJUSTMENT': '调整期'
  }
  return phaseMap[phase] || phase
}

const formatLoadProgression = (progression: string): string => {
  const progressionMap: Record<string, string> = {
    'LINEAR': '线性递增',
    'WAVE': '波浪递增',
    'STEP': '阶梯递增',
    'UNDULATING': '波动递增',
    'PLATEAU': '平台期'
  }
  return progressionMap[progression] || progression
}
</script>

<style lang="scss" scoped>
.preview-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }
  
  :deep(.el-dialog__header) {
    padding: 20px 24px 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px 24px;
  }
}

.preview-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
    
    .el-tabs__item {
      font-size: 14px;
      font-weight: 500;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      
      &.is-active {
        color: #409eff;
        font-weight: 600;
      }
    }
    
    .el-tabs__active-bar {
      height: 3px;
      border-radius: 2px;
    }
  }
}

.tab-content {
  min-height: 400px;
  padding: 16px 0;
}

// 基础配置样式
.basic-config-content {
  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .config-card {
    background: #fafbfc;
    border: 1px solid #e1e6eb;
    border-radius: 8px;
    padding: 20px;
    
    &.advanced-card {
      border-color: #d9ecff;
      background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e1e6eb;
      
      .el-icon {
        color: #409eff;
      }
    }
    
    .card-content {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #606266;
          min-width: 80px;
          font-weight: 500;
        }
        
        .value {
          color: #303133;
          font-weight: 500;
          flex: 1;
          
          &.highlight {
            color: #409eff;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.intensity-bar {
  margin-top: 8px;
  width: 100%;
  height: 6px;
  background-color: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
  
  .intensity-fill {
    height: 100%;
    background: linear-gradient(90deg, #67c23a 0%, #e6a23c 70%, #f56c6c 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
  }
}

// 训练安排样式
.schedule-content {
  .schedule-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 32px;
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  
  .el-icon {
    color: #409eff;
  }
}

.mini-calendar {
  background: white;
  border: 1px solid #e1e6eb;
  border-radius: 8px;
  padding: 16px;
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .month-year {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .nav-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .calendar-grid {
    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px;
      margin-bottom: 8px;
      
      .weekday {
        text-align: center;
        font-size: 12px;
        color: #909399;
        font-weight: 500;
        padding: 4px;
      }
    }
    
    .days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px;
      
      .day {
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        
        &.other-month {
          color: #c0c4cc;
        }
        
        &.training-day {
          background: #e1f3d8;
          color: #67c23a;
          font-weight: 500;
        }
        
        &.rest-day {
          background: #fdf2ec;
          color: #e6a23c;
        }
        
        &.today {
          background: #409eff;
          color: white;
          font-weight: 600;
        }
        
        &:hover:not(.today) {
          background: #f5f7fa;
        }
      }
    }
  }
  
  .calendar-legend {
    display: flex;
    gap: 16px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f2f5;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #606266;
      
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        
        &.training {
          background: #67c23a;
        }
        
        &.rest {
          background: #e6a23c;
        }
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  
  .stat-card {
    background: white;
    border: 1px solid #e1e6eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

.weekly-pattern {
  background: white;
  border: 1px solid #e1e6eb;
  border-radius: 8px;
  padding: 16px;
  
  .pattern-header {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
  }
  
  .pattern-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    
    .pattern-day {
      text-align: center;
      padding: 8px 4px;
      border-radius: 6px;
      background: #fdf2ec;
      color: #e6a23c;
      
      &.is-training {
        background: #e1f3d8;
        color: #67c23a;
      }
      
      .day-name {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .day-status {
        font-size: 11px;
      }
    }
  }
}

// 负荷分析样式
.analysis-content {
  .analysis-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 32px;
  }
}

.chart-container {
  background: white;
  border: 1px solid #e1e6eb;
  border-radius: 8px;
  padding: 20px;
  
  .trend-chart {
    width: 100%;
    height: 200px;
    
    .axis-label {
      font-size: 12px;
      fill: #909399;
      text-anchor: middle;
    }
    
    .trend-line {
      filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
    }
    
    .data-point {
      filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.4));
      
      &:hover {
        r: 6;
        fill: #337ecc;
      }
    }
  }
}

.load-characteristics {
  background: white;
  border: 1px solid #e1e6eb;
  border-radius: 8px;
  padding: 20px;
  
  .characteristic-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .char-label {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .char-value {
      font-size: 16px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 4px;
    }
    
    .char-description {
      font-size: 12px;
      color: #606266;
      line-height: 1.4;
      margin-bottom: 8px;
    }
    
    .char-bar {
      height: 4px;
      background: #f0f2f5;
      border-radius: 2px;
      overflow: hidden;
      
      .char-fill {
        height: 100%;
        background: #409eff;
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .schedule-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .analysis-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .basic-config-content {
    .config-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .pattern-days {
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    
    .pattern-day {
      padding: 6px 2px;
      
      .day-name {
        font-size: 10px;
      }
      
      .day-status {
        font-size: 9px;
      }
    }
  }
}
</style>